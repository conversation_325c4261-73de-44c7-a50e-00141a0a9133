{"name": "erp-intelligent-assistant", "version": "1.0.0", "description": "基于AI大模型的企业ERP系统用户体验增强插件", "main": "dist/background.js", "scripts": {"build": "webpack --mode=production", "build:dev": "webpack --mode=development", "watch": "webpack --mode=development --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.{js,ts}", "lint:fix": "eslint src/**/*.{js,ts} --fix", "format": "prettier --write src/**/*.{js,ts,json,css,html}", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "npm run clean && npm run build:dev && npm run watch"}, "keywords": ["chrome-extension", "erp", "ai", "automation", "documentation", "user-guidance"], "author": "ERP Assistant Team", "license": "MIT", "devDependencies": {"@types/chrome": "^0.0.268", "@types/jest": "^29.5.12", "@types/node": "^20.11.17", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.10.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "mini-css-extract-plugin": "^2.8.0", "playwright": "^1.40.0", "@playwright/test": "^1.40.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "style-loader": "^3.3.4", "supertest": "^6.3.3", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "webpack": "^5.90.1", "webpack-cli": "^5.1.4"}, "dependencies": {"idb": "^8.0.0", "lodash": "^4.17.21", "uuid": "^9.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/erp-intelligent-assistant.git"}, "bugs": {"url": "https://github.com/your-org/erp-intelligent-assistant/issues"}, "homepage": "https://github.com/your-org/erp-intelligent-assistant#readme"}