<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ERP智能助手 - UI组件系统演示</title>
  <style>
    :root {
      /* Scribe风格配色方案 */
      --color-primary: #6366f1;
      --color-primary-hover: #5855eb;
      --color-primary-light: #e0e7ff;
      --color-primary-dark: #4f46e5;
      --color-secondary: #64748b;
      --color-background: #f8fafc;
      --color-surface: #ffffff;
      --color-text-primary: #1e293b;
      --color-text-secondary: #64748b;
      --color-text-muted: #94a3b8;
      --color-border: #e2e8f0;
      --color-border-light: #f1f5f9;
      --color-success: #10b981;
      --color-warning: #f59e0b;
      --color-error: #ef4444;
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
      --radius-sm: 6px;
      --radius-md: 8px;
      --radius-lg: 12px;
      --spacing-xs: 4px;
      --spacing-sm: 8px;
      --spacing-md: 16px;
      --spacing-lg: 24px;
      --spacing-xl: 32px;
      --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      --font-size-xs: 12px;
      --font-size-sm: 14px;
      --font-size-md: 16px;
      --font-size-lg: 18px;
      --font-size-xl: 20px;
      --font-weight-normal: 400;
      --font-weight-medium: 500;
      --font-weight-semibold: 600;
      --font-weight-bold: 700;
      --duration-fast: 150ms;
      --duration-normal: 250ms;
      --duration-slow: 350ms;
      --easing-ease: ease;
      --easing-ease-in: ease-in;
      --easing-ease-out: ease-out;
      --easing-ease-in-out: ease-in-out;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: var(--font-family);
      background: var(--color-background);
      color: var(--color-text-primary);
      line-height: 1.5;
      padding: var(--spacing-lg);
    }

    .demo-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .demo-header {
      text-align: center;
      margin-bottom: var(--spacing-xl);
      padding: var(--spacing-xl);
      background: var(--color-surface);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-md);
    }

    .demo-header h1 {
      font-size: 2.5rem;
      font-weight: var(--font-weight-bold);
      color: var(--color-primary);
      margin-bottom: var(--spacing-sm);
    }

    .demo-header p {
      font-size: var(--font-size-lg);
      color: var(--color-text-secondary);
    }

    .demo-section {
      margin-bottom: var(--spacing-xl);
      background: var(--color-surface);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-md);
      overflow: hidden;
    }

    .demo-section-header {
      padding: var(--spacing-lg);
      background: var(--color-primary);
      color: white;
    }

    .demo-section-header h2 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      margin-bottom: var(--spacing-xs);
    }

    .demo-section-header p {
      opacity: 0.9;
    }

    .demo-section-content {
      padding: var(--spacing-lg);
    }

    .demo-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg);
    }

    .demo-item {
      padding: var(--spacing-md);
      border: 1px solid var(--color-border-light);
      border-radius: var(--radius-md);
      background: var(--color-background);
    }

    .demo-item h3 {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      margin-bottom: var(--spacing-sm);
      color: var(--color-text-primary);
    }

    /* 按钮样式 */
    .erp-button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      border: none;
      border-radius: var(--radius-md);
      font-weight: var(--font-weight-medium);
      cursor: pointer;
      transition: all var(--duration-fast) var(--easing-ease);
      outline: none;
      position: relative;
      overflow: hidden;
      user-select: none;
      font-family: var(--font-family);
    }

    .erp-button:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .erp-button:active:not(:disabled) {
      transform: translateY(0);
    }

    .erp-button:focus-visible {
      outline: 2px solid var(--color-primary);
      outline-offset: 2px;
    }

    .erp-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    /* 按钮尺寸 */
    .erp-button-xs { padding: 4px 8px; font-size: 12px; height: 24px; }
    .erp-button-sm { padding: 6px 12px; font-size: 14px; height: 32px; }
    .erp-button-md { padding: 8px 16px; font-size: 16px; height: 40px; }
    .erp-button-lg { padding: 12px 20px; font-size: 18px; height: 48px; }
    .erp-button-xl { padding: 16px 24px; font-size: 20px; height: 56px; }

    /* 按钮变体 */
    .erp-button-primary {
      background-color: var(--color-primary);
      color: white;
      border: 1px solid var(--color-primary);
    }

    .erp-button-primary:hover:not(:disabled) {
      background-color: var(--color-primary-hover);
    }

    .erp-button-secondary {
      background-color: var(--color-surface);
      color: var(--color-text-primary);
      border: 1px solid var(--color-border);
    }

    .erp-button-secondary:hover:not(:disabled) {
      background-color: var(--color-border-light);
      border-color: var(--color-primary);
    }

    .erp-button-success {
      background-color: var(--color-success);
      color: white;
      border: 1px solid var(--color-success);
    }

    .erp-button-warning {
      background-color: var(--color-warning);
      color: white;
      border: 1px solid var(--color-warning);
    }

    .erp-button-error {
      background-color: var(--color-error);
      color: white;
      border: 1px solid var(--color-error);
    }

    .erp-button-outline {
      background-color: transparent;
      color: var(--color-primary);
      border: 1px solid var(--color-primary);
    }

    .erp-button-outline:hover:not(:disabled) {
      background-color: var(--color-primary);
      color: white;
    }

    .erp-button-ghost {
      background-color: transparent;
      color: var(--color-text-primary);
      border: 1px solid transparent;
    }

    .erp-button-ghost:hover:not(:disabled) {
      background-color: var(--color-border-light);
    }

    /* 卡片样式 */
    .erp-card {
      background-color: var(--color-surface);
      border-radius: var(--radius-lg);
      transition: all var(--duration-normal) var(--easing-ease);
      overflow: hidden;
      border: 1px solid var(--color-border-light);
      box-shadow: var(--shadow-sm);
    }

    .erp-card.hoverable:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
      cursor: pointer;
    }

    .erp-card-header {
      padding: var(--spacing-md);
      border-bottom: 1px solid var(--color-border-light);
      background-color: var(--color-background);
    }

    .erp-card-body {
      padding: var(--spacing-md);
    }

    .erp-card-footer {
      padding: var(--spacing-md);
      border-top: 1px solid var(--color-border-light);
      background-color: var(--color-background);
    }

    /* 输入框样式 */
    .erp-input-wrapper {
      font-family: var(--font-family);
    }

    .erp-input {
      width: 100%;
      border: 1px solid var(--color-border);
      border-radius: var(--radius-md);
      background-color: var(--color-surface);
      color: var(--color-text-primary);
      font-family: var(--font-family);
      transition: all var(--duration-fast) var(--easing-ease);
      outline: none;
      padding: 10px 12px;
      font-size: 14px;
      height: 40px;
    }

    .erp-input:focus {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px var(--color-primary-light);
    }

    .erp-input:hover:not(:disabled):not(:focus) {
      border-color: var(--color-primary);
    }

    .erp-input:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background-color: var(--color-border-light);
    }

    .erp-input::placeholder {
      color: var(--color-text-muted);
    }

    .erp-input-label {
      display: block;
      margin-bottom: 6px;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }

    .erp-input-helper-text {
      margin-top: 4px;
      font-size: var(--font-size-xs);
      color: var(--color-text-secondary);
    }

    .erp-input-error {
      border-color: var(--color-error) !important;
    }

    .erp-input-error:focus {
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
    }

    .erp-input-error + .erp-input-helper-text {
      color: var(--color-error);
    }

    @keyframes ripple {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <div class="demo-header">
      <h1>🎨 ERP智能助手 UI组件系统</h1>
      <p>基于Scribe设计风格的现代化UI组件库</p>
    </div>

    <!-- 按钮组件演示 -->
    <div class="demo-section">
      <div class="demo-section-header">
        <h2>🔘 Button 按钮组件</h2>
        <p>支持多种变体、尺寸和状态的按钮组件</p>
      </div>
      <div class="demo-section-content">
        <div class="demo-item">
          <h3>按钮变体</h3>
          <div style="display: flex; gap: 12px; flex-wrap: wrap;">
            <button class="erp-button erp-button-primary erp-button-md">Primary</button>
            <button class="erp-button erp-button-secondary erp-button-md">Secondary</button>
            <button class="erp-button erp-button-success erp-button-md">Success</button>
            <button class="erp-button erp-button-warning erp-button-md">Warning</button>
            <button class="erp-button erp-button-error erp-button-md">Error</button>
            <button class="erp-button erp-button-outline erp-button-md">Outline</button>
            <button class="erp-button erp-button-ghost erp-button-md">Ghost</button>
          </div>
        </div>

        <div class="demo-item">
          <h3>按钮尺寸</h3>
          <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
            <button class="erp-button erp-button-primary erp-button-xs">Extra Small</button>
            <button class="erp-button erp-button-primary erp-button-sm">Small</button>
            <button class="erp-button erp-button-primary erp-button-md">Medium</button>
            <button class="erp-button erp-button-primary erp-button-lg">Large</button>
            <button class="erp-button erp-button-primary erp-button-xl">Extra Large</button>
          </div>
        </div>

        <div class="demo-item">
          <h3>按钮状态</h3>
          <div style="display: flex; gap: 12px; flex-wrap: wrap;">
            <button class="erp-button erp-button-primary erp-button-md">
              <span>🚀</span>
              <span>带图标</span>
            </button>
            <button class="erp-button erp-button-primary erp-button-md" disabled>
              <span>禁用状态</span>
            </button>
            <button class="erp-button erp-button-primary erp-button-md">
              <span style="display: inline-block; width: 16px; height: 16px; border: 2px solid currentColor; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></span>
              <span>加载中</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片组件演示 -->
    <div class="demo-section">
      <div class="demo-section-header">
        <h2>🃏 Card 卡片组件</h2>
        <p>灵活的内容容器，支持头部、主体和底部区域</p>
      </div>
      <div class="demo-section-content">
        <div class="demo-grid">
          <div class="erp-card">
            <div class="erp-card-header">
              <h3 style="margin: 0; font-size: 16px; font-weight: 600;">基础卡片</h3>
              <p style="margin: 4px 0 0 0; font-size: 14px; color: var(--color-text-secondary);">这是一个基础的卡片组件</p>
            </div>
            <div class="erp-card-body">
              <p>卡片内容区域，可以放置任何内容。支持文本、图片、表单等各种元素。</p>
            </div>
            <div class="erp-card-footer">
              <div style="display: flex; gap: 8px; justify-content: flex-end;">
                <button class="erp-button erp-button-secondary erp-button-sm">取消</button>
                <button class="erp-button erp-button-primary erp-button-sm">确定</button>
              </div>
            </div>
          </div>

          <div class="erp-card hoverable">
            <div class="erp-card-body">
              <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">可悬停卡片</h3>
              <p style="color: var(--color-text-secondary);">鼠标悬停时会有动画效果，适合用作可点击的卡片。</p>
            </div>
          </div>

          <div class="erp-card">
            <div class="erp-card-body">
              <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                <div style="width: 48px; height: 48px; background: var(--color-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px;">
                  📊
                </div>
                <div>
                  <h3 style="margin: 0; font-size: 16px; font-weight: 600;">数据统计</h3>
                  <p style="margin: 0; font-size: 14px; color: var(--color-text-secondary);">实时数据展示</p>
                </div>
              </div>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="font-size: 24px; font-weight: 700; color: var(--color-primary);">1,234</span>
                <span style="font-size: 12px; color: var(--color-success);">+12.5%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入框组件演示 -->
    <div class="demo-section">
      <div class="demo-section-header">
        <h2>📝 Input 输入框组件</h2>
        <p>支持多种类型和状态的输入框组件</p>
      </div>
      <div class="demo-section-content">
        <div class="demo-grid">
          <div class="demo-item">
            <h3>基础输入框</h3>
            <div class="erp-input-wrapper">
              <label class="erp-input-label" for="basic-input">用户名</label>
              <input id="basic-input" class="erp-input" type="text" placeholder="请输入用户名">
              <div class="erp-input-helper-text">请输入您的用户名</div>
            </div>
          </div>

          <div class="demo-item">
            <h3>密码输入框</h3>
            <div class="erp-input-wrapper">
              <label class="erp-input-label" for="password-input">密码</label>
              <input id="password-input" class="erp-input" type="password" placeholder="请输入密码">
            </div>
          </div>

          <div class="demo-item">
            <h3>错误状态</h3>
            <div class="erp-input-wrapper">
              <label class="erp-input-label" for="error-input">邮箱地址</label>
              <input id="error-input" class="erp-input erp-input-error" type="email" placeholder="请输入邮箱" value="invalid-email">
              <div class="erp-input-helper-text">请输入有效的邮箱地址</div>
            </div>
          </div>

          <div class="demo-item">
            <h3>禁用状态</h3>
            <div class="erp-input-wrapper">
              <label class="erp-input-label" for="disabled-input">只读字段</label>
              <input id="disabled-input" class="erp-input" type="text" value="只读内容" disabled>
            </div>
          </div>

          <div class="demo-item">
            <h3>搜索框</h3>
            <div class="erp-input-wrapper">
              <label class="erp-input-label" for="search-input">搜索</label>
              <div style="position: relative;">
                <input id="search-input" class="erp-input" type="search" placeholder="搜索内容..." style="padding-right: 40px;">
                <div style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: var(--color-text-secondary);">
                  🔍
                </div>
              </div>
            </div>
          </div>

          <div class="demo-item">
            <h3>文本域</h3>
            <div class="erp-input-wrapper">
              <label class="erp-input-label" for="textarea-input">描述</label>
              <textarea id="textarea-input" class="erp-input" rows="4" placeholder="请输入描述信息..." style="height: auto; resize: vertical;"></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主题展示 -->
    <div class="demo-section">
      <div class="demo-section-header">
        <h2>🎨 主题系统</h2>
        <p>基于CSS变量的主题系统，支持动态切换</p>
      </div>
      <div class="demo-section-content">
        <div class="demo-item">
          <h3>颜色系统</h3>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 12px;">
            <div style="text-align: center;">
              <div style="width: 60px; height: 60px; background: var(--color-primary); border-radius: 50%; margin: 0 auto 8px;"></div>
              <div style="font-size: 12px; color: var(--color-text-secondary);">Primary</div>
            </div>
            <div style="text-align: center;">
              <div style="width: 60px; height: 60px; background: var(--color-success); border-radius: 50%; margin: 0 auto 8px;"></div>
              <div style="font-size: 12px; color: var(--color-text-secondary);">Success</div>
            </div>
            <div style="text-align: center;">
              <div style="width: 60px; height: 60px; background: var(--color-warning); border-radius: 50%; margin: 0 auto 8px;"></div>
              <div style="font-size: 12px; color: var(--color-text-secondary);">Warning</div>
            </div>
            <div style="text-align: center;">
              <div style="width: 60px; height: 60px; background: var(--color-error); border-radius: 50%; margin: 0 auto 8px;"></div>
              <div style="font-size: 12px; color: var(--color-text-secondary);">Error</div>
            </div>
          </div>
        </div>

        <div class="demo-item">
          <h3>阴影系统</h3>
          <div style="display: flex; gap: 16px; flex-wrap: wrap;">
            <div style="width: 80px; height: 80px; background: var(--color-surface); border-radius: var(--radius-md); box-shadow: var(--shadow-sm); display: flex; align-items: center; justify-content: center; font-size: 12px; text-align: center;">Small</div>
            <div style="width: 80px; height: 80px; background: var(--color-surface); border-radius: var(--radius-md); box-shadow: var(--shadow-md); display: flex; align-items: center; justify-content: center; font-size: 12px; text-align: center;">Medium</div>
            <div style="width: 80px; height: 80px; background: var(--color-surface); border-radius: var(--radius-md); box-shadow: var(--shadow-lg); display: flex; align-items: center; justify-content: center; font-size: 12px; text-align: center;">Large</div>
          </div>
        </div>

        <div class="demo-item">
          <h3>圆角系统</h3>
          <div style="display: flex; gap: 16px; flex-wrap: wrap;">
            <div style="width: 80px; height: 80px; background: var(--color-primary); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; text-align: center;">Small</div>
            <div style="width: 80px; height: 80px; background: var(--color-primary); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; text-align: center;">Medium</div>
            <div style="width: 80px; height: 80px; background: var(--color-primary); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; text-align: center;">Large</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 添加按钮点击波纹效果
    document.querySelectorAll('.erp-button').forEach(button => {
      button.addEventListener('click', function(e) {
        if (this.disabled) return;
        
        const rect = this.getBoundingClientRect();
        const ripple = document.createElement('span');
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
          position: absolute;
          width: ${size}px;
          height: ${size}px;
          left: ${x}px;
          top: ${y}px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          transform: scale(0);
          animation: ripple 0.6s ease-out;
          pointer-events: none;
          z-index: 1;
        `;
        
        this.appendChild(ripple);
        
        setTimeout(() => {
          ripple.remove();
        }, 600);
      });
    });

    // 输入框交互效果
    document.querySelectorAll('.erp-input').forEach(input => {
      input.addEventListener('focus', function() {
        this.parentElement.style.transform = 'translateY(-1px)';
      });
      
      input.addEventListener('blur', function() {
        this.parentElement.style.transform = '';
      });
    });

    console.log('🎨 ERP智能助手 UI组件系统演示页面已加载');
    console.log('📦 包含组件：Button、Card、Input');
    console.log('🎯 设计风格：Scribe风格，现代化设计');
    console.log('✨ 特性：响应式、可访问性、动画效果');
  </script>
</body>
</html>
