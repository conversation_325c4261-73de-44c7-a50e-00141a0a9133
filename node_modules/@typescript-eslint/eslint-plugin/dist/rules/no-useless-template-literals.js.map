{"version": 3, "file": "no-useless-template-literals.js", "sourceRoot": "", "sources": ["../../src/rules/no-useless-template-literals.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,+CAAiC;AAEjC,kCAOiB;AAIjB,kBAAe,IAAA,iBAAU,EAAgB;IACvC,IAAI,EAAE,8BAA8B;IACpC,IAAI,EAAE;QACJ,OAAO,EAAE,MAAM;QACf,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,2CAA2C;YACxD,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,+BAA+B,EAC7B,mEAAmE;SACtE;QACD,MAAM,EAAE,EAAE;QACV,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,uDAAuD,CAAC;KACtE;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAE5C,SAAS,sBAAsB,CAC7B,UAA+B;YAE/B,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEhE,MAAM,QAAQ,GAAG,CAAC,CAAU,EAAW,EAAE;gBACvC,OAAO,IAAA,oBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACnD,CAAC,CAAC;YAEF,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;YAED,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAED,SAAS,SAAS,CAAC,UAA+B;YAChD,OAAO,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,CAAC;QACpD,CAAC;QAED,SAAS,iBAAiB,CAAC,UAA+B;YACxD,OAAO,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CAAC;QAC5D,CAAC;QAED,SAAS,oBAAoB,CAAC,UAA+B;YAC3D,OAAO,CACL,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC7C,UAAU,CAAC,IAAI,KAAK,UAAU,CAC/B,CAAC;QACJ,CAAC;QAED,SAAS,eAAe,CAAC,UAA+B;YACtD,OAAO,CACL,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC7C,UAAU,CAAC,IAAI,KAAK,KAAK,CAC1B,CAAC;QACJ,CAAC;QAED,OAAO;YACL,eAAe,CAAC,IAA8B;gBAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,EAAE,CAAC;oBACjE,OAAO;gBACT,CAAC;gBAED,MAAM,uBAAuB,GAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;oBACxB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,EAAE;oBAC/B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,EAAE;oBAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;oBAC7B,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE9C,IAAI,uBAAuB,EAAE,CAAC;oBAC5B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;wBACzB,SAAS,EAAE,iCAAiC;wBAC5C,GAAG,CAAC,KAAK;4BACP,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;4BAE3C,mCAAmC;4BACnC,OAAO;gCACL,KAAK,CAAC,WAAW,CAAC;oCAChB,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;oCACtB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;iCAC7B,CAAC;gCAEF,KAAK,CAAC,WAAW,CAAC;oCAChB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;oCAC5B,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;iCACvB,CAAC;6BACH,CAAC;wBACJ,CAAC;qBACF,CAAC,CAAC;oBAEH,OAAO;gBACT,CAAC;gBAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAChD,UAAU,CAAC,EAAE,CACX,SAAS,CAAC,UAAU,CAAC;oBACrB,iBAAiB,CAAC,UAAU,CAAC;oBAC7B,IAAA,4BAAqB,EAAC,UAAU,CAAC;oBACjC,oBAAoB,CAAC,UAAU,CAAC;oBAChC,eAAe,CAAC,UAAU,CAAC,CAC9B,CAAC;gBAEF,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;oBACtC,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,UAAU;wBAChB,SAAS,EAAE,iCAAiC;wBAC5C,GAAG,CAAC,KAAK;4BACP,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;4BACnD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;4BACrC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;4BAEzC,uEAAuE;4BACvE,MAAM,KAAK,GAAG;gCACZ,KAAK,CAAC,WAAW,CAAC;oCAChB,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;oCACtB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;iCACpB,CAAC;gCAEF,KAAK,CAAC,WAAW,CAAC;oCAChB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;oCACnB,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;iCACvB,CAAC;6BACH,CAAC;4BAEF,MAAM,WAAW,GAAG,IAAA,2BAAoB,EAAC,UAAU,CAAC,CAAC;4BAErD,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;gCACxB,MAAM,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;gCAE9D,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;4BAC1D,CAAC;iCAAM,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC;gCACzC,2EAA2E;gCAC3E,yDAAyD;gCACzD,KAAK,CAAC,IAAI,CACR,KAAK,CAAC,WAAW,CAAC;oCAChB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;oCACnB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;iCACxB,CAAC,EACF,KAAK,CAAC,WAAW,CAAC;oCAChB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;oCACvB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;iCACpB,CAAC,CACH,CAAC;4BACJ,CAAC;4BAED,OAAO,KAAK,CAAC;wBACf,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}