{"version": 3, "file": "prefer-readonly-parameter-types.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-readonly-parameter-types.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAG1D,kCAMiB;AAYjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,iCAAiC;IACvC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,gGAAgG;YAClG,oBAAoB,EAAE,IAAI;SAC3B;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,KAAK,EAAE,gCAAyB,CAAC,UAAU,CAAC,KAAK;oBACjD,wBAAwB,EAAE;wBACxB,IAAI,EAAE,SAAS;qBAChB;oBACD,mBAAmB,EAAE;wBACnB,IAAI,EAAE,SAAS;qBAChB;oBACD,sBAAsB,EACpB,gCAAyB,CAAC,UAAU,CAAC,sBAAsB;iBAC9D;aACF;SACF;QACD,QAAQ,EAAE;YACR,gBAAgB,EAAE,uCAAuC;SAC1D;KACF;IACD,cAAc,EAAE;QACd;YACE,KAAK,EAAE,kCAA2B,CAAC,KAAK;YACxC,wBAAwB,EAAE,IAAI;YAC9B,mBAAmB,EAAE,KAAK;YAC1B,sBAAsB,EACpB,kCAA2B,CAAC,sBAAsB;SACrD;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,KAAK,EACL,wBAAwB,EACxB,mBAAmB,EACnB,sBAAsB,GACvB,EACF;QAED,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAE5C,OAAO;YACL,CAAC;gBACC,sBAAc,CAAC,uBAAuB;gBACtC,sBAAc,CAAC,mBAAmB;gBAClC,sBAAc,CAAC,kBAAkB;gBACjC,sBAAc,CAAC,0BAA0B;gBACzC,sBAAc,CAAC,+BAA+B;gBAC9C,sBAAc,CAAC,iBAAiB;gBAChC,sBAAc,CAAC,6BAA6B;gBAC5C,sBAAc,CAAC,cAAc;gBAC7B,sBAAc,CAAC,iBAAiB;aACjC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CACX,IAS8B;gBAE9B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBAChC,IACE,CAAC,wBAAwB;wBACzB,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EACjD,CAAC;wBACD,SAAS;oBACX,CAAC;oBAED,MAAM,WAAW,GACf,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;wBAC/C,CAAC,CAAC,KAAK,CAAC,SAAS;wBACjB,CAAC,CAAC,KAAK,CAAC;oBAEZ,IAAI,mBAAmB,IAAI,WAAW,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;wBAC9D,SAAS;oBACX,CAAC;oBAED,MAAM,IAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;oBACrD,MAAM,UAAU,GAAG,IAAA,qBAAc,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE;wBACxD,sBAAsB,EAAE,CAAC,CAAC,sBAAsB;wBAChD,KAAK;qBACN,CAAC,CAAC;oBAEH,IAAI,CAAC,UAAU,EAAE,CAAC;wBAChB,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,kBAAkB;yBAC9B,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}