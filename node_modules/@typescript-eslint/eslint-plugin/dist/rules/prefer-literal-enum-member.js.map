{"version": 3, "file": "prefer-literal-enum-member.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-literal-enum-member.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAA2D;AAE3D,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,4BAA4B;IAClC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,+CAA+C;YAC5D,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,KAAK;SAC5B;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,kFAAkF;SAC/F;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,uBAAuB,EAAE;wBACvB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,uBAAuB,EAAE,KAAK;SAC/B;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,uBAAuB,EAAE,CAAC;QAC3C,SAAS,oBAAoB,CAAC,IAAmB,EAAE,IAAY;YAC7D,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;QACvE,CAAC;QAED,SAAS,aAAa,CACpB,IAAgC,EAChC,IAAY;YAEZ,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CACtB,MAAM,CAAC,EAAE,CACP,oBAAoB,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC;gBACrC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBACxC,IAAA,2BAAoB,EAAC,MAAM,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,CAC9C,CAAC;QACJ,CAAC;QAED,SAAS,gBAAgB,CACvB,IAAgC,EAChC,IAAmB;YAEnB,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;gBAC5C,OAAO,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;YAED,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAC/C,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;oBACrD,OAAO,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACjD,CAAC;gBAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,MAAM,YAAY,GAAG,IAAA,2BAAoB,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACzD,IAAI,YAAY,EAAE,CAAC;wBACjB,OAAO,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;oBAC3C,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,uBAAuB,CAC9B,IAAgC,EAChC,IAAmB;YAEnB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CACrE,CAAC;QACJ,CAAC;QAED,OAAO;YACL,YAAY,CAAC,IAAI;gBACf,wFAAwF;gBACxF,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;oBAC7B,OAAO;gBACT,CAAC;gBACD,kBAAkB;gBAClB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;oBACrD,OAAO;gBACT,CAAC;gBACD,sCAAsC;gBACtC,IACE,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBACxD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EACzC,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAoC,CAAC;gBAE9D,YAAY;gBACZ,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;oBAC7D,IACE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;wBACzD,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAC9C,CAAC;wBACD,OAAO;oBACT,CAAC;oBAED,IACE,uBAAuB;wBACvB,IAAI,CAAC,WAAW,CAAC,QAAQ,KAAK,GAAG;wBACjC,uBAAuB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAC/D,CAAC;wBACD,OAAO;oBACT,CAAC;gBACH,CAAC;gBACD,IACE,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBACxD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBACzD,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;wBAC7C,CAAC,uBAAuB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC,EACjE,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,IACE,uBAAuB;oBACvB,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACzD,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,QAAQ,CACzC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAC1B;oBACD,uBAAuB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;oBAC3D,uBAAuB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAC5D,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,IAAI,CAAC,EAAE;oBACb,SAAS,EAAE,YAAY;iBACxB,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}