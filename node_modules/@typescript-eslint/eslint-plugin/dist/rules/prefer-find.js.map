{"version": 3, "file": "prefer-find.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-find.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,sDAAwC;AAGxC,kCAMiB;AAEjB,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,aAAa;IACnB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,0HAA0H;YAC5H,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,+CAA+C;YAC3D,oBAAoB,EAAE,4CAA4C;SACnE;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;QAClB,cAAc,EAAE,IAAI;KACrB;IAED,cAAc,EAAE,EAAE;IAElB,MAAM,CAAC,OAAO;QACZ,MAAM,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACxE,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAOlD,SAAS,2BAA2B,CAClC,UAA+B;YAE/B,IAAI,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE,CAAC;gBAC1D,6EAA6E;gBAC7E,MAAM,cAAc,GAAG,IAAA,iBAAU,EAC/B,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC7B,sEAAsE,CACvE,CAAC;gBACF,OAAO,2BAA2B,CAAC,cAAc,CAAC,CAAC;YACrD,CAAC;YAED,IAAI,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;gBACvD,OAAO,2BAA2B,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC5D,CAAC;YAED,6EAA6E;YAC7E,IAAI,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB,EAAE,CAAC;gBAC7D,sDAAsD;gBACtD,MAAM,gBAAgB,GAAG,2BAA2B,CAClD,UAAU,CAAC,UAAU,CACtB,CAAC;gBACF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAClC,OAAO,EAAE,CAAC;gBACZ,CAAC;gBAED,MAAM,eAAe,GAAG,2BAA2B,CACjD,UAAU,CAAC,SAAS,CACrB,CAAC;gBACF,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACjC,OAAO,EAAE,CAAC;gBACZ,CAAC;gBAED,gEAAgE;gBAChE,OAAO,CAAC,GAAG,gBAAgB,EAAE,GAAG,eAAe,CAAC,CAAC;YACnD,CAAC;YAED,kEAAkE;YAClE,IACE,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;gBACjD,CAAC,UAAU,CAAC,QAAQ,EACpB,CAAC;gBACD,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;gBACjC,4EAA4E;gBAC5E,qCAAqC;gBACrC,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;oBACpD,MAAM,wBAAwB,GAAG,MAAM,CAAC,QAAQ,CAAC;oBACjD,IAAI,2BAA2B,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,CAAC;wBAC/D,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;wBAEnC,MAAM,kBAAkB,GAAG,IAAA,mCAA4B,EACrD,QAAQ,EACR,MAAM,CAAC,MAAM,CACd,CAAC;wBAEF,wDAAwD;wBACxD,gDAAgD;wBAChD,IAAI,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC;4BACnC,OAAO;gCACL;oCACE,wBAAwB;oCACxB,UAAU;iCACX;6BACF,CAAC;wBACJ,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,2BAA2B;YAC3B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED;;WAEG;QACH,SAAS,UAAU,CAAC,IAAU;YAC5B,IAAI,6BAA6B,GAAG,KAAK,CAAC;YAC1C,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrD,IACE,OAAO,CAAC,mBAAmB,CAAC,SAAS,CAAC;oBACtC,OAAO,CAAC,wBAAwB,CAAC,SAAS,CAAC,EAC3C,CAAC;oBACD,SAAS;gBACX,CAAC;gBAED,sDAAsD;gBACtD,2DAA2D;gBAC3D,MAAM,4BAA4B,GAAG,OAAO;qBACzC,qBAAqB,CAAC,SAAS,CAAC;qBAChC,KAAK,CACJ,gBAAgB,CAAC,EAAE,CACjB,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC;oBACrC,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,CACxC,CAAC;gBAEJ,IAAI,CAAC,4BAA4B,EAAE,CAAC;oBAClC,oDAAoD;oBACpD,wBAAwB;oBACxB,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,6BAA6B,GAAG,IAAI,CAAC;YACvC,CAAC;YAED,OAAO,6BAA6B,CAAC;QACvC,CAAC;QAED,SAAS,gCAAgC,CACvC,IAA6B;YAE7B,0CAA0C;YAC1C,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChC,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC/C,CAAC,MAAM,CAAC,QAAQ;gBAChB,2BAA2B,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,EACtD,CAAC;gBACD,MAAM,UAAU,GAAG,IAAA,qBAAc,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;gBAClE,IAAI,UAAU,IAAI,IAAI,IAAI,wBAAwB,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;oBACrE,OAAO,MAAM,CAAC,MAAM,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED;;;WAGG;QACH,SAAS,wBAAwB,CAAC,KAAc;YAC9C,0EAA0E;YAC1E,mBAAmB;YACnB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAE/B,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;QAED,SAAS,oBAAoB,CAC3B,IAA2C;YAE3C,MAAM,QAAQ,GAAG,IAAA,qBAAc,EAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAC5D,gFAAgF;YAChF,OAAO,CACL,CAAC,IAAI,CAAC,QAAQ;gBACd,QAAQ,IAAI,IAAI;gBAChB,6BAA6B,CAAC,QAAQ,CAAC,KAAK,CAAC,CAC9C,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,6BAA6B,CAAC,KAAc;YACnD,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAC/B,CAAC;QAED,SAAS,qCAAqC,CAC5C,KAAyB,EACzB,SAA8B,EAC9B,2BAAgD;YAEhD,MAAM,wBAAwB,GAAG,IAAA,iBAAU;YACzC,iDAAiD;YACjD,2DAA2D;YAC3D,OAAO,CAAC,UAAU,CAAC,aAAa,CAC9B,SAAS,EACT,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,GAAG,CACpD,EACD,yCAAyC,CAC1C,CAAC;YACF,OAAO,KAAK,CAAC,WAAW,CAAC;gBACvB,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;gBACjC,2BAA2B,CAAC,KAAK,CAAC,CAAC,CAAC;aACrC,CAAC,CAAC;QACL,CAAC;QAED,SAAS,kCAAkC,CACzC,KAAyB,EACzB,gBAAsC;YAEtC,OAAO,KAAK,CAAC,WAAW,CACtB,gBAAgB,CAAC,UAAU,EAC3B,gBAAgB,CAAC,wBAAwB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAC9D,CAAC;QACJ,CAAC;QAED,OAAO;YACL,uEAAuE;YACvE,cAAc,CAAC,IAAI;gBACjB,MAAM,MAAM,GAAG,gCAAgC,CAAC,IAAI,CAAC,CAAC;gBACtD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,iBAAiB,GAAG,2BAA2B,CAAC,MAAM,CAAC,CAAC;oBAC9D,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACnC,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,YAAY;4BACvB,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,sBAAsB;oCACjC,GAAG,EAAE,CAAC,KAAK,EAAsB,EAAE;wCACjC,OAAO;4CACL,GAAG,iBAAiB,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAC1C,kCAAkC,CAChC,KAAK,EACL,gBAAgB,CACjB,CACF;4CACD,sCAAsC;4CACtC,qCAAqC,CACnC,KAAK,EACL,MAAM,EACN,IAAI,CACL;yCACF,CAAC;oCACJ,CAAC;iCACF;6BACF;yBACF,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,oEAAoE;YACpE,EAAE;YACF,uEAAuE;YACvE,sEAAsE;YACtE,iCAAiC,CAC/B,IAA2C;gBAE3C,IAAI,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC3B,MAAM,iBAAiB,GAAG,2BAA2B,CAAC,MAAM,CAAC,CAAC;oBAC9D,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACnC,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,YAAY;4BACvB,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,sBAAsB;oCACjC,GAAG,EAAE,CAAC,KAAK,EAAsB,EAAE;wCACjC,OAAO;4CACL,GAAG,iBAAiB,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAC1C,kCAAkC,CAChC,KAAK,EACL,gBAAgB,CACjB,CACF;4CACD,sBAAsB;4CACtB,qCAAqC,CACnC,KAAK,EACL,MAAM,EACN,IAAI,CACL;yCACF,CAAC;oCACJ,CAAC;iCACF;6BACF;yBACF,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH;;;;GAIG;AACH,SAAS,2BAA2B,CAClC,gBAE4C,EAC5C,KAAa,EACb,KAA+B;IAE/B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QAC/B,qBAAqB;QACrB,OAAO,gBAAgB,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC;IAClD,CAAC;IAED,yBAAyB;IACzB,MAAM,iBAAiB,GAAG,IAAA,qBAAc,EAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC3E,OAAO,iBAAiB,IAAI,IAAI,IAAI,KAAK,KAAK,iBAAiB,CAAC,KAAK,CAAC;AACxE,CAAC"}