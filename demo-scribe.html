<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ERP智能助手 - Scribe风格界面</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    :root {
      /* Scribe风格配色方案 */
      --primary-color: #6366f1;
      --primary-hover: #5855eb;
      --primary-light: #e0e7ff;
      --primary-dark: #4f46e5;
      --secondary-color: #64748b;
      --background-color: #f8fafc;
      --surface-color: #ffffff;
      --text-primary: #1e293b;
      --text-secondary: #64748b;
      --text-muted: #94a3b8;
      --border-color: #e2e8f0;
      --border-light: #f1f5f9;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --error-color: #ef4444;
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
      --radius-sm: 6px;
      --radius-md: 8px;
      --radius-lg: 12px;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: var(--background-color);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: var(--text-primary);
      line-height: 1.5;
    }
    
    .demo-container {
      display: grid;
      grid-template-columns: 320px 1fr;
      gap: 24px;
      max-width: 1200px;
      width: 100%;
    }
    
    .popup-demo {
      background: var(--surface-color);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      overflow: hidden;
      height: fit-content;
      border: 1px solid var(--border-light);
    }
    
    .options-demo {
      background: var(--surface-color);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      overflow: hidden;
      max-height: 80vh;
      overflow-y: auto;
      border: 1px solid var(--border-light);
    }
    
    /* Popup样式 - Scribe风格 */
    .popup-header {
      background: var(--surface-color);
      color: var(--text-primary);
      padding: 24px;
      text-align: center;
      border-bottom: 1px solid var(--border-light);
    }
    
    .popup-header .logo {
      width: 48px;
      height: 48px;
      background: var(--primary-color);
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
      font-size: 24px;
      color: white;
    }
    
    .popup-header h1 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
      color: var(--text-primary);
    }
    
    .popup-header p {
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .popup-content {
      padding: 24px;
    }
    
    .status-section {
      margin-bottom: 24px;
    }
    
    .status-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 0;
      border-bottom: 1px solid var(--border-light);
    }
    
    .status-item:last-child {
      border-bottom: none;
    }
    
    .status-label {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-primary);
    }
    
    .status-value {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 13px;
      color: var(--text-secondary);
    }
    
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--success-color);
    }
    
    .status-dot.inactive {
      background: var(--text-muted);
    }
    
    .btn {
      width: 100%;
      padding: 12px 16px;
      border: none;
      border-radius: var(--radius-md);
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    
    .btn-primary {
      background: var(--primary-color);
      color: white;
    }
    
    .btn-primary:hover {
      background: var(--primary-hover);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
    
    .btn-secondary {
      background: var(--surface-color);
      color: var(--text-primary);
      border: 1px solid var(--border-color);
    }
    
    .btn-secondary:hover {
      background: var(--border-light);
      border-color: var(--primary-color);
    }
    
    /* Options样式 - Scribe风格 */
    .options-header {
      background: var(--surface-color);
      color: var(--text-primary);
      padding: 32px;
      text-align: center;
      border-bottom: 1px solid var(--border-light);
    }
    
    .options-header .logo {
      width: 56px;
      height: 56px;
      background: var(--primary-color);
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 16px;
      font-size: 28px;
      color: white;
    }
    
    .options-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      color: var(--text-primary);
    }
    
    .options-header p {
      font-size: 16px;
      color: var(--text-secondary);
    }
    
    .tabs {
      display: flex;
      background: var(--border-light);
      border-bottom: 1px solid var(--border-color);
    }
    
    .tab {
      flex: 1;
      padding: 16px 12px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: var(--text-secondary);
      border-bottom: 2px solid transparent;
      transition: all 0.2s ease;
      background: transparent;
    }
    
    .tab:hover {
      background: var(--surface-color);
      color: var(--text-primary);
    }
    
    .tab.active {
      background: var(--surface-color);
      color: var(--primary-color);
      border-bottom-color: var(--primary-color);
    }
    
    .tab-content {
      padding: 32px;
      display: none;
    }
    
    .tab-content.active {
      display: block;
    }
    
    .form-section {
      margin-bottom: 32px;
    }
    
    .form-section:last-child {
      margin-bottom: 0;
    }
    
    .form-section h3 {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-primary);
      font-size: 14px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid var(--border-color);
      border-radius: var(--radius-md);
      font-size: 14px;
      transition: all 0.2s ease;
      background: var(--surface-color);
      color: var(--text-primary);
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px var(--primary-light);
    }
    
    .checkbox-group {
      display: flex;
      align-items: center;
      gap: 12px;
    }
    
    .checkbox-group input[type="checkbox"] {
      width: 18px;
      height: 18px;
      margin: 0;
      accent-color: var(--primary-color);
    }
    
    .help-text {
      font-size: 13px;
      color: var(--text-muted);
      margin-top: 6px;
    }
    
    .model-selector {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-top: 16px;
    }
    
    .model-card {
      border: 2px solid var(--border-color);
      border-radius: var(--radius-md);
      padding: 20px;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: center;
      background: var(--surface-color);
    }
    
    .model-card:hover {
      border-color: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }
    
    .model-card.selected {
      border-color: var(--primary-color);
      background: var(--primary-light);
    }
    
    .model-name {
      font-weight: 600;
      margin-bottom: 6px;
      color: var(--text-primary);
      font-size: 14px;
    }
    
    .model-desc {
      font-size: 13px;
      color: var(--text-secondary);
    }
    
    .alert {
      padding: 16px;
      border-radius: var(--radius-md);
      margin-bottom: 24px;
      font-size: 14px;
      border: 1px solid;
    }
    
    .alert-info {
      background: #eff6ff;
      color: #1e40af;
      border-color: #bfdbfe;
    }
    
    .save-section {
      background: var(--border-light);
      padding: 24px 32px;
      border-top: 1px solid var(--border-color);
      display: flex;
      gap: 12px;
    }
    
    .save-section .btn {
      width: auto;
      padding: 12px 24px;
      margin: 0;
    }
    
    @media (max-width: 768px) {
      .demo-container {
        grid-template-columns: 1fr;
        gap: 20px;
      }
      
      .popup-demo {
        order: 2;
      }
      
      .options-demo {
        order: 1;
        max-height: none;
      }
      
      .tabs {
        flex-wrap: wrap;
      }
      
      .tab {
        flex: none;
        min-width: 120px;
      }
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <!-- Popup界面演示 - Scribe风格 -->
    <div class="popup-demo">
      <div class="popup-header">
        <div class="logo">🤖</div>
        <h1>ERP智能助手</h1>
        <p>您的专属AI操作助手</p>
      </div>
      
      <div class="popup-content">
        <div class="status-section">
          <div class="status-item">
            <span class="status-label">系统状态</span>
            <div class="status-value">
              <div class="status-dot"></div>
              <span>运行中</span>
            </div>
          </div>
          
          <div class="status-item">
            <span class="status-label">AI模型</span>
            <div class="status-value">
              <div class="status-dot"></div>
              <span>Llama 3.2 3B</span>
            </div>
          </div>
          
          <div class="status-item">
            <span class="status-label">录制状态</span>
            <div class="status-value">
              <div class="status-dot inactive"></div>
              <span>未录制</span>
            </div>
          </div>
          
          <div class="status-item">
            <span class="status-label">智能引导</span>
            <div class="status-value">
              <div class="status-dot"></div>
              <span>已启用</span>
            </div>
          </div>
        </div>
        
        <button class="btn btn-primary">
          <span>🔴</span>
          开始录制
        </button>
        <button class="btn btn-secondary">
          <span>✨</span>
          智能引导
        </button>
        <button class="btn btn-secondary">
          <span>💬</span>
          AI助手
        </button>
        <button class="btn btn-secondary">
          <span>⚙️</span>
          设置
        </button>
      </div>
    </div>

    <!-- Options界面演示 - Scribe风格 -->
    <div class="options-demo">
      <div class="options-header">
        <div class="logo">⚙️</div>
        <h1>ERP智能助手设置</h1>
        <p>配置您的AI助手，获得最佳的使用体验</p>
      </div>

      <div class="tabs">
        <div class="tab active" onclick="switchTab('general')">常规设置</div>
        <div class="tab" onclick="switchTab('ai')">AI模型</div>
        <div class="tab" onclick="switchTab('privacy')">隐私安全</div>
        <div class="tab" onclick="switchTab('guidance')">智能引导</div>
        <div class="tab" onclick="switchTab('advanced')">高级选项</div>
      </div>

      <div class="tab-content active" id="general">
        <div class="form-section">
          <h3>🌐 基础配置</h3>

          <div class="form-group">
            <label for="language">界面语言</label>
            <select id="language">
              <option value="zh-CN">简体中文</option>
              <option value="en-US">English</option>
            </select>
          </div>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="autoStart" checked>
              <label for="autoStart">自动启动智能引导</label>
            </div>
            <div class="help-text">在支持的ERP页面自动显示操作提示</div>
          </div>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="notifications" checked>
              <label for="notifications">启用通知</label>
            </div>
            <div class="help-text">接收重要提醒和更新通知</div>
          </div>
        </div>

        <div class="form-section">
          <h3>📹 录制设置</h3>

          <div class="form-group">
            <label for="recordingQuality">录制质量</label>
            <select id="recordingQuality">
              <option value="high">高质量（推荐）</option>
              <option value="medium">中等质量</option>
              <option value="low">低质量（节省空间）</option>
            </select>
          </div>
        </div>
      </div>

      <div class="tab-content" id="ai">
        <div class="alert alert-info">
          <strong>💡 提示：</strong>更改AI模型设置可能影响响应速度和准确性。建议使用默认配置。
        </div>

        <div class="form-section">
          <h3>🏠 本地模型</h3>
          <div class="model-selector">
            <div class="model-card selected">
              <div class="model-name">Llama 3.2 3B</div>
              <div class="model-desc">快速响应，隐私保护</div>
            </div>
            <div class="model-card">
              <div class="model-name">Phi-3 Mini</div>
              <div class="model-desc">轻量级，低资源消耗</div>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3>☁️ 云端模型</h3>
          <div class="model-selector">
            <div class="model-card selected">
              <div class="model-name">GPT-4o</div>
              <div class="model-desc">高质量生成，复杂推理</div>
            </div>
            <div class="model-card">
              <div class="model-name">Claude 3.5 Sonnet</div>
              <div class="model-desc">优秀的理解能力</div>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3>🔑 API配置</h3>

          <div class="form-group">
            <label for="apiKey">API密钥</label>
            <input type="password" id="apiKey" placeholder="输入您的API密钥">
            <div class="help-text">用于访问云端AI服务，密钥将安全存储在本地</div>
          </div>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="enableRAG" checked>
              <label for="enableRAG">启用RAG检索增强</label>
            </div>
            <div class="help-text">使用检索增强生成技术提高回答准确性</div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="privacy">
        <div class="form-section">
          <h3>🔒 隐私保护</h3>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="localFirst" checked>
              <label for="localFirst">优先本地处理</label>
            </div>
            <div class="help-text">敏感数据优先在本地处理，提高隐私保护</div>
          </div>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="dataAnonymization" checked>
              <label for="dataAnonymization">数据脱敏</label>
            </div>
            <div class="help-text">自动识别并脱敏敏感信息</div>
          </div>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="encryptStorage">
              <label for="encryptStorage">加密本地存储</label>
            </div>
            <div class="help-text">使用加密技术保护本地存储的数据</div>
          </div>
        </div>

        <div class="form-section">
          <h3>🗂️ 数据管理</h3>

          <div class="form-group">
            <label for="dataRetention">数据保留期限</label>
            <select id="dataRetention">
              <option value="7">7天</option>
              <option value="30" selected>30天</option>
              <option value="90">90天</option>
              <option value="365">1年</option>
              <option value="0">永久保留</option>
            </select>
          </div>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="shareUsageData">
              <label for="shareUsageData">共享使用数据</label>
            </div>
            <div class="help-text">匿名共享使用统计以帮助改进产品</div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="guidance">
        <div class="form-section">
          <h3>🎯 引导配置</h3>

          <div class="form-group">
            <label for="guidanceLevel">引导详细程度</label>
            <select id="guidanceLevel">
              <option value="minimal">简洁模式</option>
              <option value="standard" selected>标准模式</option>
              <option value="detailed">详细模式</option>
            </select>
            <div class="help-text">选择适合您经验水平的引导详细程度</div>
          </div>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="showHints" checked>
              <label for="showHints">显示操作提示</label>
            </div>
            <div class="help-text">在页面上显示智能操作提示</div>
          </div>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="adaptiveGuidance" checked>
              <label for="adaptiveGuidance">自适应引导</label>
            </div>
            <div class="help-text">根据您的操作习惯自动调整引导策略</div>
          </div>
        </div>

        <div class="form-section">
          <h3>🤖 AI助手</h3>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="enableChatAssistant" checked>
              <label for="enableChatAssistant">启用聊天助手</label>
            </div>
            <div class="help-text">提供智能对话帮助和操作建议</div>
          </div>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="enableErrorPrevention" checked>
              <label for="enableErrorPrevention">启用错误预防</label>
            </div>
            <div class="help-text">主动检测并预防常见操作错误</div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="advanced">
        <div class="form-section">
          <h3>🔧 高级选项</h3>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="debugMode">
              <label for="debugMode">调试模式</label>
            </div>
            <div class="help-text">启用详细日志记录，用于问题诊断</div>
          </div>

          <div class="form-group">
            <label for="maxMemoryUsage">最大内存使用 (MB)</label>
            <input type="number" id="maxMemoryUsage" value="512" min="256" max="2048">
            <div class="help-text">限制插件的内存使用量</div>
          </div>

          <div class="form-group">
            <label for="customPrompt">自定义提示词</label>
            <textarea id="customPrompt" rows="4" placeholder="输入自定义的AI提示词模板..."></textarea>
            <div class="help-text">高级用户可以自定义AI的行为模式</div>
          </div>
        </div>

        <div class="form-section">
          <h3>🧪 实验性功能</h3>

          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="experimentalFeatures">
              <label for="experimentalFeatures">启用实验性功能</label>
            </div>
            <div class="help-text">启用正在开发中的实验性功能（可能不稳定）</div>
          </div>
        </div>
      </div>

      <div class="save-section">
        <button class="btn btn-primary">💾 保存设置</button>
        <button class="btn btn-secondary">↩️ 取消</button>
        <button class="btn btn-secondary">🔄 重置</button>
      </div>
    </div>
  </div>

  <script>
    function switchTab(tabName) {
      // 隐藏所有标签内容
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
      });

      // 移除所有标签的活动状态
      document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
      });

      // 显示选中的标签内容
      document.getElementById(tabName).classList.add('active');

      // 激活选中的标签
      event.target.classList.add('active');
    }

    // 模型卡片选择
    document.querySelectorAll('.model-card').forEach(card => {
      card.addEventListener('click', function() {
        // 移除同组其他卡片的选中状态
        const group = this.parentElement;
        group.querySelectorAll('.model-card').forEach(c => c.classList.remove('selected'));

        // 选中当前卡片
        this.classList.add('selected');
      });
    });

    // 添加按钮交互效果
    document.querySelectorAll('.btn').forEach(btn => {
      btn.addEventListener('click', function() {
        this.style.transform = 'scale(0.98)';
        setTimeout(() => {
          this.style.transform = '';
        }, 150);
      });
    });

    // 添加输入框焦点效果
    document.querySelectorAll('input, select, textarea').forEach(input => {
      input.addEventListener('focus', function() {
        this.parentElement.style.transform = 'translateY(-1px)';
      });

      input.addEventListener('blur', function() {
        this.parentElement.style.transform = '';
      });
    });
  </script>
</body>
</html>
