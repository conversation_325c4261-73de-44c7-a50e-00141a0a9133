// ERP智能助手Chrome插件 - 数据同步客户端

import { io, Socket } from 'socket.io-client';
import { EventEmitter } from 'events';
import { StorageService } from './storage';
import { ApiService } from './api';

// 同步状态枚举
export enum SyncStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  SYNCING = 'syncing',
  ERROR = 'error',
}

// 数据变更记录接口
export interface DataChange {
  id: string;
  entityType: string;
  entityId: string;
  operation: 'create' | 'update' | 'delete' | 'batch';
  data: any;
  previousData?: any;
  timestamp: number;
  version: number;
  checksum: string;
  metadata: {
    source: 'client' | 'server';
    offline?: boolean;
    [key: string]: any;
  };
}

// 离线操作接口
export interface OfflineOperation {
  id: string;
  type: 'create' | 'update' | 'delete' | 'batch';
  entityType: string;
  entityId: string;
  data: any;
  previousData?: any;
  timestamp: number;
  version: number;
  dependencies: string[];
  metadata: {
    queuedAt: number;
    retryCount: number;
    priority: 'low' | 'medium' | 'high';
  };
}

// 同步配置接口
export interface SyncConfig {
  serverUrl: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  syncInterval: number;
  batchSize: number;
  offlineQueueSize: number;
  conflictResolution: 'client' | 'server' | 'manual';
}

// 冲突信息接口
export interface ConflictInfo {
  id: string;
  entityType: string;
  entityId: string;
  clientData: any;
  serverData: any;
  conflictType: 'version' | 'concurrent' | 'deleted';
  timestamp: number;
}

export class SyncClient extends EventEmitter {
  private socket: Socket | null = null;
  private config: SyncConfig;
  private status: SyncStatus = SyncStatus.DISCONNECTED;
  private clientId: string;
  private lastSyncTimestamp: number = 0;
  private pendingChanges: DataChange[] = [];
  private offlineQueue: OfflineOperation[] = [];
  private syncInterval?: number;
  private reconnectAttempts = 0;
  private isOnline = navigator.onLine;

  constructor(config: Partial<SyncConfig> = {}) {
    super();
    
    this.config = {
      serverUrl: 'http://localhost:3000',
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      syncInterval: 30000,
      batchSize: 50,
      offlineQueueSize: 1000,
      conflictResolution: 'manual',
      ...config,
    };

    this.clientId = this.generateClientId();
    this.setupNetworkListeners();
    this.loadOfflineData();
  }

  /**
   * 初始化同步客户端
   */
  async initialize(): Promise<void> {
    try {
      console.log('Initializing sync client...');
      
      // 加载上次同步时间戳
      this.lastSyncTimestamp = await StorageService.get('lastSyncTimestamp', 0);
      
      // 连接到服务器
      await this.connect();
      
      // 启动定期同步
      this.startPeriodicSync();
      
      console.log('Sync client initialized successfully');
    } catch (error) {
      console.error('Failed to initialize sync client:', error);
      throw error;
    }
  }

  /**
   * 连接到服务器
   */
  async connect(): Promise<void> {
    if (this.socket?.connected) {
      return;
    }

    this.status = SyncStatus.CONNECTING;
    this.emit('statusChanged', this.status);

    try {
      const token = await StorageService.get('authToken');
      if (!token) {
        throw new Error('No authentication token available');
      }

      this.socket = io(this.config.serverUrl, {
        auth: { token },
        query: { 
          clientId: this.clientId,
          version: chrome.runtime.getManifest().version,
          platform: 'chrome-extension',
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
      });

      this.setupSocketListeners();

      // 等待连接建立
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.socket!.once('connect', () => {
          clearTimeout(timeout);
          resolve();
        });

        this.socket!.once('connect_error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      this.status = SyncStatus.CONNECTED;
      this.reconnectAttempts = 0;
      this.emit('statusChanged', this.status);
      this.emit('connected');

      // 连接成功后立即同步
      await this.performSync();

    } catch (error) {
      this.status = SyncStatus.ERROR;
      this.emit('statusChanged', this.status);
      this.emit('error', error);
      
      // 自动重连
      this.scheduleReconnect();
      throw error;
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = undefined;
    }

    this.status = SyncStatus.DISCONNECTED;
    this.emit('statusChanged', this.status);
    this.emit('disconnected');
  }

  /**
   * 添加数据变更
   */
  async addChange(change: Omit<DataChange, 'id' | 'timestamp' | 'checksum'>): Promise<string> {
    const fullChange: DataChange = {
      ...change,
      id: this.generateChangeId(),
      timestamp: Date.now(),
      checksum: this.calculateChecksum(change.data),
    };

    // 如果离线，添加到离线队列
    if (!this.isOnline || this.status !== SyncStatus.CONNECTED) {
      await this.addToOfflineQueue(fullChange);
    } else {
      this.pendingChanges.push(fullChange);
    }

    // 保存到本地存储
    await this.savePendingChanges();

    this.emit('changeAdded', fullChange);
    return fullChange.id;
  }

  /**
   * 批量添加数据变更
   */
  async addBatchChanges(changes: Array<Omit<DataChange, 'id' | 'timestamp' | 'checksum'>>): Promise<string[]> {
    const changeIds: string[] = [];
    
    for (const change of changes) {
      const changeId = await this.addChange(change);
      changeIds.push(changeId);
    }

    return changeIds;
  }

  /**
   * 执行同步
   */
  async performSync(): Promise<void> {
    if (this.status === SyncStatus.SYNCING || !this.socket?.connected) {
      return;
    }

    this.status = SyncStatus.SYNCING;
    this.emit('statusChanged', this.status);

    try {
      // 处理离线队列
      if (this.offlineQueue.length > 0) {
        await this.syncOfflineQueue();
      }

      // 同步待处理的变更
      if (this.pendingChanges.length > 0) {
        await this.syncPendingChanges();
      }

      // 获取服务器端增量变更
      await this.fetchIncrementalChanges();

      // 更新同步时间戳
      this.lastSyncTimestamp = Date.now();
      await StorageService.set('lastSyncTimestamp', this.lastSyncTimestamp);

      this.status = SyncStatus.CONNECTED;
      this.emit('statusChanged', this.status);
      this.emit('syncCompleted');

    } catch (error) {
      this.status = SyncStatus.ERROR;
      this.emit('statusChanged', this.status);
      this.emit('syncError', error);
      throw error;
    }
  }

  /**
   * 解决冲突
   */
  async resolveConflict(
    conflictId: string, 
    resolution: 'client' | 'server' | 'merge' | 'manual',
    mergedData?: any
  ): Promise<void> {
    try {
      const response = await ApiService.post('/sync/conflicts/resolve', {
        conflictId,
        resolution,
        mergedData,
      });

      this.emit('conflictResolved', { conflictId, resolution });
      
    } catch (error) {
      console.error('Failed to resolve conflict:', error);
      throw error;
    }
  }

  /**
   * 获取同步状态
   */
  getStatus(): {
    status: SyncStatus;
    isOnline: boolean;
    lastSyncTime: number;
    pendingChanges: number;
    offlineQueue: number;
  } {
    return {
      status: this.status,
      isOnline: this.isOnline,
      lastSyncTime: this.lastSyncTimestamp,
      pendingChanges: this.pendingChanges.length,
      offlineQueue: this.offlineQueue.length,
    };
  }

  // 私有方法

  private setupSocketListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to sync server');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from sync server:', reason);
      this.status = SyncStatus.DISCONNECTED;
      this.emit('statusChanged', this.status);
      
      if (reason === 'io server disconnect') {
        // 服务器主动断开，需要重新连接
        this.scheduleReconnect();
      }
    });

    this.socket.on('message', (message) => {
      this.handleServerMessage(message);
    });

    this.socket.on('data_change', (data) => {
      this.handleServerDataChange(data);
    });

    this.socket.on('sync_response', (response) => {
      this.handleSyncResponse(response);
    });

    this.socket.on('conflict_detected', (conflict) => {
      this.handleConflict(conflict);
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.scheduleReconnect();
    });
  }

  private setupNetworkListeners(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.emit('networkStatusChanged', true);
      
      // 网络恢复时自动连接和同步
      if (this.status === SyncStatus.DISCONNECTED) {
        this.connect().catch(console.error);
      }
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.emit('networkStatusChanged', false);
    });
  }

  private async loadOfflineData(): Promise<void> {
    try {
      this.pendingChanges = await StorageService.get('pendingChanges', []);
      this.offlineQueue = await StorageService.get('offlineQueue', []);
    } catch (error) {
      console.error('Failed to load offline data:', error);
    }
  }

  private async savePendingChanges(): Promise<void> {
    try {
      await StorageService.set('pendingChanges', this.pendingChanges);
    } catch (error) {
      console.error('Failed to save pending changes:', error);
    }
  }

  private async saveOfflineQueue(): Promise<void> {
    try {
      await StorageService.set('offlineQueue', this.offlineQueue);
    } catch (error) {
      console.error('Failed to save offline queue:', error);
    }
  }

  private async addToOfflineQueue(change: DataChange): Promise<void> {
    const operation: OfflineOperation = {
      id: change.id,
      type: change.operation,
      entityType: change.entityType,
      entityId: change.entityId,
      data: change.data,
      previousData: change.previousData,
      timestamp: change.timestamp,
      version: change.version,
      dependencies: [],
      metadata: {
        queuedAt: Date.now(),
        retryCount: 0,
        priority: 'medium',
      },
    };

    // 检查队列大小限制
    if (this.offlineQueue.length >= this.config.offlineQueueSize) {
      // 移除最旧的操作
      this.offlineQueue.shift();
    }

    this.offlineQueue.push(operation);
    await this.saveOfflineQueue();

    this.emit('offlineOperationQueued', operation);
  }

  private async syncOfflineQueue(): Promise<void> {
    if (this.offlineQueue.length === 0) return;

    try {
      const response = await ApiService.post('/sync/offline/queue', {
        clientId: this.clientId,
        operations: this.offlineQueue,
      });

      // 清空离线队列
      this.offlineQueue = [];
      await this.saveOfflineQueue();

      this.emit('offlineQueueSynced', response.data);
      
    } catch (error) {
      console.error('Failed to sync offline queue:', error);
      throw error;
    }
  }

  private async syncPendingChanges(): Promise<void> {
    if (this.pendingChanges.length === 0) return;

    // 分批处理
    const batches = this.chunkArray(this.pendingChanges, this.config.batchSize);

    for (const batch of batches) {
      try {
        const response = await ApiService.post('/sync/request', {
          clientId: this.clientId,
          changes: batch,
          lastSyncTimestamp: this.lastSyncTimestamp,
          priority: 'medium',
        });

        // 处理服务器响应
        if (response.data.incrementalChanges?.length > 0) {
          await this.applyServerChanges(response.data.incrementalChanges);
        }

        // 移除已同步的变更
        this.pendingChanges = this.pendingChanges.filter(
          change => !batch.some(batchChange => batchChange.id === change.id)
        );

      } catch (error) {
        console.error('Failed to sync batch:', error);
        throw error;
      }
    }

    await this.savePendingChanges();
  }

  private async fetchIncrementalChanges(): Promise<void> {
    try {
      const response = await ApiService.post('/sync/incremental', {
        clientId: this.clientId,
        lastSyncTimestamp: this.lastSyncTimestamp,
      });

      if (response.data.changes?.length > 0) {
        await this.applyServerChanges(response.data.changes);
      }

    } catch (error) {
      console.error('Failed to fetch incremental changes:', error);
      throw error;
    }
  }

  private async applyServerChanges(changes: DataChange[]): Promise<void> {
    for (const change of changes) {
      try {
        // 应用服务器端变更到本地存储
        await this.applyChange(change);
        this.emit('serverChangeApplied', change);
      } catch (error) {
        console.error('Failed to apply server change:', error, change);
      }
    }
  }

  private async applyChange(change: DataChange): Promise<void> {
    // 根据实体类型和操作应用变更
    // 这里需要根据具体的业务逻辑实现
    console.log('Applying change:', change);
  }

  private handleServerMessage(message: any): void {
    console.log('Received server message:', message);
    this.emit('serverMessage', message);
  }

  private handleServerDataChange(data: any): void {
    console.log('Received server data change:', data);
    this.emit('serverDataChange', data);
  }

  private handleSyncResponse(response: any): void {
    console.log('Received sync response:', response);
    this.emit('syncResponse', response);
  }

  private handleConflict(conflict: ConflictInfo): void {
    console.log('Conflict detected:', conflict);
    this.emit('conflictDetected', conflict);
  }

  private startPeriodicSync(): void {
    this.syncInterval = window.setInterval(() => {
      if (this.status === SyncStatus.CONNECTED && this.isOnline) {
        this.performSync().catch(console.error);
      }
    }, this.config.syncInterval);
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error('Max reconnect attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);

    setTimeout(() => {
      if (this.status !== SyncStatus.CONNECTED) {
        this.connect().catch(console.error);
      }
    }, delay);
  }

  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateChangeId(): string {
    return `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateChecksum(data: any): string {
    // 简单的校验和计算
    return btoa(JSON.stringify(data)).slice(0, 16);
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 销毁同步客户端
   */
  destroy(): void {
    this.disconnect();
    this.removeAllListeners();
    
    window.removeEventListener('online', this.setupNetworkListeners);
    window.removeEventListener('offline', this.setupNetworkListeners);
  }
}

// 导出单例实例
export const syncClient = new SyncClient();
