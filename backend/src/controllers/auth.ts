// ERP智能助手后端服务 - 认证控制器

import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { databaseService } from '@/services/database';
import { redisService } from '@/services/redis';
import { 
  AuthenticationError, 
  ConflictError, 
  NotFoundError,
  ValidationError 
} from '@/middleware/error-handler';
import { JWTService, User, UserRole } from '@/middleware/auth';

export class AuthController {
  private logger: Logger;

  constructor() {
    this.logger = new Logger('AuthController');
  }

  /**
   * 用户注册
   */
  async register(req: Request, res: Response): Promise<void> {
    const { email, password, username, firstName, lastName, role = UserRole.USER } = req.body;

    try {
      // 检查用户是否已存在
      const existingUser = await this.findUserByEmail(email);
      if (existingUser) {
        throw new ConflictError('User already exists with this email');
      }

      // 检查用户名是否已存在
      const existingUsername = await this.findUserByUsername(username);
      if (existingUsername) {
        throw new ConflictError('Username already taken');
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(password, config.encryption.bcryptRounds);

      // 创建用户
      const userId = uuidv4();
      const now = new Date();

      const user: User = {
        id: userId,
        email,
        username,
        role: role as UserRole,
        permissions: this.getDefaultPermissions(role as UserRole),
        isActive: true,
        createdAt: now,
        updatedAt: now,
      };

      // 保存用户到数据库
      await this.createUser(user, hashedPassword, firstName, lastName);

      // 生成JWT令牌
      const accessToken = JWTService.generateAccessToken(user);
      const refreshToken = JWTService.generateRefreshToken(user);

      // 存储刷新令牌
      await this.storeRefreshToken(userId, refreshToken);

      // 记录登录日志
      this.logger.info('User registered successfully', {
        userId,
        email,
        username,
        role,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      // 返回响应
      res.status(201).json({
        user: this.sanitizeUser(user),
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: config.jwt.expiresIn,
        },
      });
    } catch (error) {
      this.logger.error('User registration failed:', error, {
        email,
        username,
        ip: req.ip,
      });
      throw error;
    }
  }

  /**
   * 用户登录
   */
  async login(req: Request, res: Response): Promise<void> {
    const { email, password, rememberMe = false } = req.body;

    try {
      // 查找用户
      const userRecord = await this.findUserByEmailWithPassword(email);
      if (!userRecord) {
        throw new AuthenticationError('Invalid email or password');
      }

      // 验证密码
      const isPasswordValid = await bcrypt.compare(password, userRecord.password);
      if (!isPasswordValid) {
        throw new AuthenticationError('Invalid email or password');
      }

      // 检查用户是否激活
      if (!userRecord.isActive) {
        throw new AuthenticationError('Account is deactivated');
      }

      const user: User = {
        id: userRecord.id,
        email: userRecord.email,
        username: userRecord.username,
        role: userRecord.role as UserRole,
        permissions: userRecord.permissions || this.getDefaultPermissions(userRecord.role as UserRole),
        isActive: userRecord.isActive,
        lastLoginAt: userRecord.lastLoginAt,
        createdAt: userRecord.createdAt,
        updatedAt: userRecord.updatedAt,
      };

      // 生成JWT令牌
      const accessToken = JWTService.generateAccessToken(user);
      const refreshToken = JWTService.generateRefreshToken(user);

      // 存储刷新令牌
      await this.storeRefreshToken(user.id, refreshToken);

      // 更新最后登录时间
      await this.updateLastLogin(user.id);

      // 记录登录日志
      this.logger.info('User logged in successfully', {
        userId: user.id,
        email,
        role: user.role,
        rememberMe,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      // 返回响应
      res.json({
        user: this.sanitizeUser(user),
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: config.jwt.expiresIn,
        },
      });
    } catch (error) {
      this.logger.error('User login failed:', error, {
        email,
        ip: req.ip,
      });
      throw error;
    }
  }

  /**
   * 刷新令牌
   */
  async refresh(req: Request, res: Response): Promise<void> {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw new ValidationError('Refresh token is required');
    }

    try {
      // 验证刷新令牌
      const payload = JWTService.verifyRefreshToken(refreshToken);

      // 检查令牌是否在存储中
      const storedToken = await this.getStoredRefreshToken(payload.userId);
      if (!storedToken || storedToken !== refreshToken) {
        throw new AuthenticationError('Invalid refresh token');
      }

      // 获取用户信息
      const userRecord = await this.findUserById(payload.userId);
      if (!userRecord || !userRecord.isActive) {
        throw new AuthenticationError('User not found or inactive');
      }

      const user: User = {
        id: userRecord.id,
        email: userRecord.email,
        username: userRecord.username,
        role: userRecord.role as UserRole,
        permissions: userRecord.permissions || this.getDefaultPermissions(userRecord.role as UserRole),
        isActive: userRecord.isActive,
        lastLoginAt: userRecord.lastLoginAt,
        createdAt: userRecord.createdAt,
        updatedAt: userRecord.updatedAt,
      };

      // 生成新的令牌
      const newAccessToken = JWTService.generateAccessToken(user);
      const newRefreshToken = JWTService.generateRefreshToken(user);

      // 更新存储的刷新令牌
      await this.storeRefreshToken(user.id, newRefreshToken);

      // 记录令牌刷新日志
      this.logger.debug('Token refreshed successfully', {
        userId: user.id,
        ip: req.ip,
      });

      // 返回响应
      res.json({
        tokens: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          expiresIn: config.jwt.expiresIn,
        },
      });
    } catch (error) {
      this.logger.error('Token refresh failed:', error, {
        ip: req.ip,
      });
      throw error;
    }
  }

  /**
   * 用户登出
   */
  async logout(req: Request, res: Response): Promise<void> {
    const user = req.user!;

    try {
      // 删除存储的刷新令牌
      await this.removeRefreshToken(user.id);

      // 记录登出日志
      this.logger.info('User logged out successfully', {
        userId: user.id,
        email: user.email,
        ip: req.ip,
      });

      res.json({
        message: 'Logged out successfully',
      });
    } catch (error) {
      this.logger.error('User logout failed:', error, {
        userId: user.id,
        ip: req.ip,
      });
      throw error;
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(req: Request, res: Response): Promise<void> {
    const user = req.user!;

    res.json({
      user: this.sanitizeUser(user),
    });
  }

  /**
   * 忘记密码
   */
  async forgotPassword(req: Request, res: Response): Promise<void> {
    const { email } = req.body;

    try {
      const user = await this.findUserByEmail(email);
      if (!user) {
        // 为了安全，即使用户不存在也返回成功消息
        res.json({
          message: 'If an account with that email exists, a password reset email has been sent',
        });
        return;
      }

      // 生成重置令牌
      const resetToken = uuidv4();
      const resetExpires = new Date(Date.now() + 3600000); // 1小时后过期

      // 存储重置令牌
      await this.storePasswordResetToken(user.id, resetToken, resetExpires);

      // 这里应该发送重置邮件
      // await this.sendPasswordResetEmail(user.email, resetToken);

      this.logger.info('Password reset requested', {
        userId: user.id,
        email,
        ip: req.ip,
      });

      res.json({
        message: 'If an account with that email exists, a password reset email has been sent',
      });
    } catch (error) {
      this.logger.error('Password reset request failed:', error, {
        email,
        ip: req.ip,
      });
      throw error;
    }
  }

  /**
   * 修改密码
   */
  async changePassword(req: Request, res: Response): Promise<void> {
    const { currentPassword, newPassword } = req.body;
    const user = req.user!;

    try {
      // 获取用户当前密码
      const userRecord = await this.findUserByIdWithPassword(user.id);
      if (!userRecord) {
        throw new NotFoundError('User');
      }

      // 验证当前密码
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, userRecord.password);
      if (!isCurrentPasswordValid) {
        throw new AuthenticationError('Current password is incorrect');
      }

      // 加密新密码
      const hashedNewPassword = await bcrypt.hash(newPassword, config.encryption.bcryptRounds);

      // 更新密码
      await this.updateUserPassword(user.id, hashedNewPassword);

      // 删除所有刷新令牌（强制重新登录）
      await this.removeRefreshToken(user.id);

      this.logger.info('Password changed successfully', {
        userId: user.id,
        email: user.email,
        ip: req.ip,
      });

      res.json({
        message: 'Password changed successfully',
      });
    } catch (error) {
      this.logger.error('Password change failed:', error, {
        userId: user.id,
        ip: req.ip,
      });
      throw error;
    }
  }

  // 私有辅助方法

  private async findUserByEmail(email: string): Promise<any> {
    const result = await databaseService.query(
      'SELECT id, email, username, role, permissions, is_active, last_login_at, created_at, updated_at FROM users WHERE email = $1',
      [email]
    );
    return result.rows[0] || null;
  }

  private async findUserByUsername(username: string): Promise<any> {
    const result = await databaseService.query(
      'SELECT id FROM users WHERE username = $1',
      [username]
    );
    return result.rows[0] || null;
  }

  private async findUserByEmailWithPassword(email: string): Promise<any> {
    const result = await databaseService.query(
      'SELECT * FROM users WHERE email = $1',
      [email]
    );
    return result.rows[0] || null;
  }

  private async findUserById(id: string): Promise<any> {
    const result = await databaseService.query(
      'SELECT id, email, username, role, permissions, is_active, last_login_at, created_at, updated_at FROM users WHERE id = $1',
      [id]
    );
    return result.rows[0] || null;
  }

  private async findUserByIdWithPassword(id: string): Promise<any> {
    const result = await databaseService.query(
      'SELECT * FROM users WHERE id = $1',
      [id]
    );
    return result.rows[0] || null;
  }

  private async createUser(user: User, hashedPassword: string, firstName: string, lastName: string): Promise<void> {
    await databaseService.query(
      `INSERT INTO users (id, email, username, password, first_name, last_name, role, permissions, is_active, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
      [
        user.id,
        user.email,
        user.username,
        hashedPassword,
        firstName,
        lastName,
        user.role,
        JSON.stringify(user.permissions),
        user.isActive,
        user.createdAt,
        user.updatedAt,
      ]
    );
  }

  private async updateLastLogin(userId: string): Promise<void> {
    await databaseService.query(
      'UPDATE users SET last_login_at = NOW() WHERE id = $1',
      [userId]
    );
  }

  private async updateUserPassword(userId: string, hashedPassword: string): Promise<void> {
    await databaseService.query(
      'UPDATE users SET password = $1, updated_at = NOW() WHERE id = $2',
      [hashedPassword, userId]
    );
  }

  private async storeRefreshToken(userId: string, refreshToken: string): Promise<void> {
    const key = `refresh_token:${userId}`;
    const ttl = 30 * 24 * 60 * 60; // 30天
    await redisService.set(key, refreshToken, { ttl });
  }

  private async getStoredRefreshToken(userId: string): Promise<string | null> {
    const key = `refresh_token:${userId}`;
    return await redisService.get(key);
  }

  private async removeRefreshToken(userId: string): Promise<void> {
    const key = `refresh_token:${userId}`;
    await redisService.del(key);
  }

  private async storePasswordResetToken(userId: string, token: string, expires: Date): Promise<void> {
    const key = `password_reset:${userId}`;
    const ttl = Math.floor((expires.getTime() - Date.now()) / 1000);
    await redisService.set(key, token, { ttl });
  }

  private getDefaultPermissions(role: UserRole): string[] {
    switch (role) {
      case UserRole.ADMIN:
        return ['read', 'write', 'delete', 'admin'];
      case UserRole.USER:
        return ['read', 'write'];
      case UserRole.VIEWER:
        return ['read'];
      default:
        return ['read'];
    }
  }

  private sanitizeUser(user: User): Omit<User, 'permissions'> & { permissions?: string[] } {
    const { permissions, ...sanitizedUser } = user;
    return sanitizedUser;
  }
}
