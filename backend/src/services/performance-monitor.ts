// ERP智能助手后端服务 - 性能监控服务

import { EventEmitter } from 'events';
import os from 'os';
import process from 'process';
import { performance } from 'perf_hooks';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { databaseService } from '@/services/database';
import { redisService } from '@/services/redis';

// 性能指标类型
export enum MetricType {
  RESPONSE_TIME = 'response_time',
  THROUGHPUT = 'throughput',
  ERROR_RATE = 'error_rate',
  CPU_USAGE = 'cpu_usage',
  MEMORY_USAGE = 'memory_usage',
  DATABASE_QUERY_TIME = 'database_query_time',
  CACHE_HIT_RATE = 'cache_hit_rate',
  ACTIVE_CONNECTIONS = 'active_connections',
  QUEUE_SIZE = 'queue_size',
  DISK_USAGE = 'disk_usage',
}

// 性能指标接口
export interface PerformanceMetric {
  id: string;
  type: MetricType;
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  tags: Record<string, string>;
  metadata: Record<string, any>;
}

// 性能阈值接口
export interface PerformanceThreshold {
  metricType: MetricType;
  warningThreshold: number;
  criticalThreshold: number;
  enabled: boolean;
}

// 性能告警接口
export interface PerformanceAlert {
  id: string;
  metricType: MetricType;
  currentValue: number;
  threshold: number;
  severity: 'warning' | 'critical';
  message: string;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
}

// 性能统计接口
export interface PerformanceStats {
  systemMetrics: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    uptime: number;
  };
  applicationMetrics: {
    responseTime: {
      avg: number;
      p95: number;
      p99: number;
    };
    throughput: number;
    errorRate: number;
    activeConnections: number;
  };
  databaseMetrics: {
    queryTime: {
      avg: number;
      p95: number;
      p99: number;
    };
    connectionPool: {
      active: number;
      idle: number;
      total: number;
    };
  };
  cacheMetrics: {
    hitRate: number;
    missRate: number;
    evictionRate: number;
  };
}

// 请求跟踪接口
export interface RequestTrace {
  id: string;
  method: string;
  url: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  statusCode?: number;
  userId?: string;
  userAgent?: string;
  ipAddress?: string;
  error?: string;
  metadata: Record<string, any>;
}

export class PerformanceMonitorService extends EventEmitter {
  private logger: Logger;
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private thresholds: Map<MetricType, PerformanceThreshold> = new Map();
  private alerts: Map<string, PerformanceAlert> = new Map();
  private activeTraces: Map<string, RequestTrace> = new Map();
  private metricsBuffer: PerformanceMetric[] = [];
  private monitoringInterval?: NodeJS.Timeout;
  private flushInterval?: NodeJS.Timeout;
  private startTime = Date.now();

  constructor() {
    super();
    this.logger = new Logger('PerformanceMonitorService');
    this.setupDefaultThresholds();
  }

  /**
   * 初始化性能监控服务
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing performance monitor service...');

      // 启动系统监控
      this.startSystemMonitoring();

      // 启动指标刷新
      this.startMetricsFlush();

      // 加载历史告警
      await this.loadActiveAlerts();

      this.logger.info('Performance monitor service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize performance monitor service:', error);
      throw error;
    }
  }

  /**
   * 记录性能指标
   */
  recordMetric(
    type: MetricType,
    name: string,
    value: number,
    unit: string = '',
    tags: Record<string, string> = {},
    metadata: Record<string, any> = {}
  ): void {
    const metric: PerformanceMetric = {
      id: this.generateMetricId(),
      type,
      name,
      value,
      unit,
      timestamp: new Date(),
      tags,
      metadata,
    };

    // 添加到缓冲区
    this.metricsBuffer.push(metric);

    // 添加到内存存储
    if (!this.metrics.has(type)) {
      this.metrics.set(type, []);
    }
    
    const typeMetrics = this.metrics.get(type)!;
    typeMetrics.push(metric);

    // 保持最近1000个指标
    if (typeMetrics.length > 1000) {
      typeMetrics.shift();
    }

    // 检查阈值
    this.checkThreshold(metric);

    // 发出事件
    this.emit('metricRecorded', metric);
  }

  /**
   * 开始请求跟踪
   */
  startTrace(
    method: string,
    url: string,
    userId?: string,
    userAgent?: string,
    ipAddress?: string,
    metadata: Record<string, any> = {}
  ): string {
    const trace: RequestTrace = {
      id: this.generateTraceId(),
      method,
      url,
      startTime: performance.now(),
      userId,
      userAgent,
      ipAddress,
      metadata,
    };

    this.activeTraces.set(trace.id, trace);
    return trace.id;
  }

  /**
   * 结束请求跟踪
   */
  endTrace(traceId: string, statusCode: number, error?: string): void {
    const trace = this.activeTraces.get(traceId);
    if (!trace) return;

    trace.endTime = performance.now();
    trace.duration = trace.endTime - trace.startTime;
    trace.statusCode = statusCode;
    trace.error = error;

    // 记录响应时间指标
    this.recordMetric(
      MetricType.RESPONSE_TIME,
      'http_request_duration',
      trace.duration,
      'ms',
      {
        method: trace.method,
        status: statusCode.toString(),
        endpoint: this.normalizeUrl(trace.url),
      },
      {
        url: trace.url,
        userId: trace.userId,
        error: trace.error,
      }
    );

    // 记录错误率指标
    if (statusCode >= 400) {
      this.recordMetric(
        MetricType.ERROR_RATE,
        'http_error_count',
        1,
        'count',
        {
          method: trace.method,
          status: statusCode.toString(),
          endpoint: this.normalizeUrl(trace.url),
        }
      );
    }

    // 清理跟踪
    this.activeTraces.delete(traceId);

    // 发出事件
    this.emit('traceCompleted', trace);
  }

  /**
   * 记录数据库查询性能
   */
  recordDatabaseQuery(query: string, duration: number, error?: string): void {
    this.recordMetric(
      MetricType.DATABASE_QUERY_TIME,
      'database_query_duration',
      duration,
      'ms',
      {
        operation: this.extractQueryOperation(query),
        status: error ? 'error' : 'success',
      },
      {
        query: query.substring(0, 100), // 截断长查询
        error,
      }
    );
  }

  /**
   * 记录缓存性能
   */
  recordCacheOperation(operation: 'hit' | 'miss' | 'set' | 'delete', key: string, duration?: number): void {
    this.recordMetric(
      MetricType.CACHE_HIT_RATE,
      `cache_${operation}`,
      1,
      'count',
      { operation },
      { key: key.substring(0, 50), duration }
    );
  }

  /**
   * 获取性能统计
   */
  async getPerformanceStats(): Promise<PerformanceStats> {
    try {
      // 系统指标
      const systemMetrics = {
        cpuUsage: await this.getCpuUsage(),
        memoryUsage: this.getMemoryUsage(),
        diskUsage: await this.getDiskUsage(),
        uptime: (Date.now() - this.startTime) / 1000,
      };

      // 应用指标
      const responseTimeMetrics = this.getMetricStats(MetricType.RESPONSE_TIME);
      const applicationMetrics = {
        responseTime: {
          avg: responseTimeMetrics.avg,
          p95: responseTimeMetrics.p95,
          p99: responseTimeMetrics.p99,
        },
        throughput: this.calculateThroughput(),
        errorRate: this.calculateErrorRate(),
        activeConnections: this.activeTraces.size,
      };

      // 数据库指标
      const dbQueryMetrics = this.getMetricStats(MetricType.DATABASE_QUERY_TIME);
      const databaseMetrics = {
        queryTime: {
          avg: dbQueryMetrics.avg,
          p95: dbQueryMetrics.p95,
          p99: dbQueryMetrics.p99,
        },
        connectionPool: await this.getDatabasePoolStats(),
      };

      // 缓存指标
      const cacheMetrics = await this.getCacheStats();

      return {
        systemMetrics,
        applicationMetrics,
        databaseMetrics,
        cacheMetrics,
      };
    } catch (error) {
      this.logger.error('Failed to get performance stats:', error);
      throw error;
    }
  }

  /**
   * 获取活跃告警
   */
  getActiveAlerts(): PerformanceAlert[] {
    return Array.from(this.alerts.values()).filter(alert => !alert.resolved);
  }

  /**
   * 解决告警
   */
  async resolveAlert(alertId: string): Promise<void> {
    const alert = this.alerts.get(alertId);
    if (!alert) return;

    alert.resolved = true;
    alert.resolvedAt = new Date();

    // 持久化到数据库
    await this.persistAlert(alert);

    this.emit('alertResolved', alert);
  }

  // 私有方法

  private setupDefaultThresholds(): void {
    const defaultThresholds: PerformanceThreshold[] = [
      {
        metricType: MetricType.RESPONSE_TIME,
        warningThreshold: 1000, // 1秒
        criticalThreshold: 5000, // 5秒
        enabled: true,
      },
      {
        metricType: MetricType.CPU_USAGE,
        warningThreshold: 70, // 70%
        criticalThreshold: 90, // 90%
        enabled: true,
      },
      {
        metricType: MetricType.MEMORY_USAGE,
        warningThreshold: 80, // 80%
        criticalThreshold: 95, // 95%
        enabled: true,
      },
      {
        metricType: MetricType.ERROR_RATE,
        warningThreshold: 5, // 5%
        criticalThreshold: 10, // 10%
        enabled: true,
      },
      {
        metricType: MetricType.DATABASE_QUERY_TIME,
        warningThreshold: 500, // 500ms
        criticalThreshold: 2000, // 2秒
        enabled: true,
      },
    ];

    for (const threshold of defaultThresholds) {
      this.thresholds.set(threshold.metricType, threshold);
    }
  }

  private startSystemMonitoring(): void {
    this.monitoringInterval = setInterval(async () => {
      try {
        // CPU使用率
        const cpuUsage = await this.getCpuUsage();
        this.recordMetric(MetricType.CPU_USAGE, 'system_cpu_usage', cpuUsage, '%');

        // 内存使用率
        const memoryUsage = this.getMemoryUsage();
        this.recordMetric(MetricType.MEMORY_USAGE, 'system_memory_usage', memoryUsage, '%');

        // 活跃连接数
        this.recordMetric(
          MetricType.ACTIVE_CONNECTIONS,
          'active_connections',
          this.activeTraces.size,
          'count'
        );

        // 队列大小
        this.recordMetric(
          MetricType.QUEUE_SIZE,
          'metrics_buffer_size',
          this.metricsBuffer.length,
          'count'
        );

      } catch (error) {
        this.logger.error('System monitoring error:', error);
      }
    }, 10000); // 每10秒监控一次
  }

  private startMetricsFlush(): void {
    this.flushInterval = setInterval(async () => {
      await this.flushMetrics();
    }, 60000); // 每分钟刷新一次
  }

  private async flushMetrics(): Promise<void> {
    if (this.metricsBuffer.length === 0) return;

    try {
      const metrics = [...this.metricsBuffer];
      this.metricsBuffer = [];

      // 批量写入数据库
      await this.persistMetrics(metrics);

      // 写入Redis用于实时查询
      await this.cacheMetrics(metrics);

    } catch (error) {
      this.logger.error('Failed to flush metrics:', error);
      // 将指标放回缓冲区
      this.metricsBuffer.unshift(...this.metricsBuffer);
    }
  }

  private async persistMetrics(metrics: PerformanceMetric[]): Promise<void> {
    if (metrics.length === 0) return;

    const values = metrics.map(metric => 
      `('${metric.id}', '${metric.type}', '${metric.name}', ${metric.value}, '${metric.unit}', '${metric.timestamp.toISOString()}', '${JSON.stringify(metric.tags)}', '${JSON.stringify(metric.metadata)}')`
    ).join(',');

    await databaseService.query(`
      INSERT INTO performance_metrics (id, type, name, value, unit, timestamp, tags, metadata)
      VALUES ${values}
    `);
  }

  private async cacheMetrics(metrics: PerformanceMetric[]): Promise<void> {
    const pipeline = redisService.pipeline();

    for (const metric of metrics) {
      const key = `metrics:${metric.type}:${Date.now()}`;
      pipeline.setex(key, 3600, JSON.stringify(metric)); // 1小时过期
    }

    await pipeline.exec();
  }

  private checkThreshold(metric: PerformanceMetric): void {
    const threshold = this.thresholds.get(metric.type);
    if (!threshold || !threshold.enabled) return;

    let severity: 'warning' | 'critical' | null = null;

    if (metric.value >= threshold.criticalThreshold) {
      severity = 'critical';
    } else if (metric.value >= threshold.warningThreshold) {
      severity = 'warning';
    }

    if (severity) {
      this.createAlert(metric, threshold, severity);
    }
  }

  private createAlert(
    metric: PerformanceMetric,
    threshold: PerformanceThreshold,
    severity: 'warning' | 'critical'
  ): void {
    const alertId = this.generateAlertId();
    const alert: PerformanceAlert = {
      id: alertId,
      metricType: metric.type,
      currentValue: metric.value,
      threshold: severity === 'critical' ? threshold.criticalThreshold : threshold.warningThreshold,
      severity,
      message: `${metric.name} is ${metric.value}${metric.unit}, exceeding ${severity} threshold of ${alert.threshold}${metric.unit}`,
      timestamp: new Date(),
      resolved: false,
    };

    this.alerts.set(alertId, alert);

    // 持久化告警
    this.persistAlert(alert).catch(error => {
      this.logger.error('Failed to persist alert:', error);
    });

    // 发出事件
    this.emit('alertCreated', alert);

    this.logger.warn('Performance alert created:', {
      alertId,
      metricType: metric.type,
      currentValue: metric.value,
      threshold: alert.threshold,
      severity,
    });
  }

  private async persistAlert(alert: PerformanceAlert): Promise<void> {
    await databaseService.query(`
      INSERT INTO performance_alerts (
        id, metric_type, current_value, threshold, severity, message,
        timestamp, resolved, resolved_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      ON CONFLICT (id) DO UPDATE SET
        resolved = EXCLUDED.resolved,
        resolved_at = EXCLUDED.resolved_at
    `, [
      alert.id,
      alert.metricType,
      alert.currentValue,
      alert.threshold,
      alert.severity,
      alert.message,
      alert.timestamp,
      alert.resolved,
      alert.resolvedAt,
    ]);
  }

  private async loadActiveAlerts(): Promise<void> {
    try {
      const result = await databaseService.query(`
        SELECT * FROM performance_alerts 
        WHERE resolved = false 
        ORDER BY timestamp DESC
      `);

      for (const row of result.rows) {
        const alert: PerformanceAlert = {
          id: row.id,
          metricType: row.metric_type,
          currentValue: row.current_value,
          threshold: row.threshold,
          severity: row.severity,
          message: row.message,
          timestamp: row.timestamp,
          resolved: row.resolved,
          resolvedAt: row.resolved_at,
        };

        this.alerts.set(alert.id, alert);
      }

      this.logger.info('Loaded active alerts', { count: this.alerts.size });
    } catch (error) {
      this.logger.error('Failed to load active alerts:', error);
    }
  }

  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      const startTime = Date.now();

      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const endTime = Date.now();
        const totalTime = (endTime - startTime) * 1000; // 转换为微秒

        const cpuPercent = ((endUsage.user + endUsage.system) / totalTime) * 100;
        resolve(Math.min(cpuPercent, 100));
      }, 100);
    });
  }

  private getMemoryUsage(): number {
    const memUsage = process.memoryUsage();
    const totalMemory = os.totalmem();
    return (memUsage.rss / totalMemory) * 100;
  }

  private async getDiskUsage(): Promise<number> {
    // 简化实现，实际应用中可以使用更精确的磁盘使用率检测
    return 0;
  }

  private getMetricStats(type: MetricType): { avg: number; p95: number; p99: number } {
    const metrics = this.metrics.get(type) || [];
    if (metrics.length === 0) {
      return { avg: 0, p95: 0, p99: 0 };
    }

    const values = metrics.map(m => m.value).sort((a, b) => a - b);
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const p95Index = Math.floor(values.length * 0.95);
    const p99Index = Math.floor(values.length * 0.99);

    return {
      avg,
      p95: values[p95Index] || 0,
      p99: values[p99Index] || 0,
    };
  }

  private calculateThroughput(): number {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    
    const responseTimeMetrics = this.metrics.get(MetricType.RESPONSE_TIME) || [];
    const recentRequests = responseTimeMetrics.filter(
      m => m.timestamp.getTime() > oneMinuteAgo
    );

    return recentRequests.length; // 每分钟请求数
  }

  private calculateErrorRate(): number {
    const responseTimeMetrics = this.metrics.get(MetricType.RESPONSE_TIME) || [];
    const errorMetrics = this.metrics.get(MetricType.ERROR_RATE) || [];
    
    if (responseTimeMetrics.length === 0) return 0;
    
    const totalRequests = responseTimeMetrics.length;
    const errorRequests = errorMetrics.length;
    
    return (errorRequests / totalRequests) * 100;
  }

  private async getDatabasePoolStats(): Promise<{ active: number; idle: number; total: number }> {
    // 这里应该从数据库连接池获取实际统计信息
    return { active: 5, idle: 15, total: 20 };
  }

  private async getCacheStats(): Promise<{ hitRate: number; missRate: number; evictionRate: number }> {
    const cacheMetrics = this.metrics.get(MetricType.CACHE_HIT_RATE) || [];
    
    const hits = cacheMetrics.filter(m => m.tags.operation === 'hit').length;
    const misses = cacheMetrics.filter(m => m.tags.operation === 'miss').length;
    const total = hits + misses;
    
    if (total === 0) {
      return { hitRate: 0, missRate: 0, evictionRate: 0 };
    }
    
    return {
      hitRate: (hits / total) * 100,
      missRate: (misses / total) * 100,
      evictionRate: 0, // 简化实现
    };
  }

  private normalizeUrl(url: string): string {
    // 移除查询参数和动态路径段
    return url.replace(/\?.*$/, '').replace(/\/\d+/g, '/:id');
  }

  private extractQueryOperation(query: string): string {
    const match = query.trim().match(/^(SELECT|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER)/i);
    return match ? match[1].toUpperCase() : 'UNKNOWN';
  }

  private generateMetricId(): string {
    return `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateTraceId(): string {
    return `trace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 关闭性能监控服务
   */
  async close(): Promise<void> {
    this.logger.info('Closing performance monitor service...');

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }

    // 刷新剩余指标
    await this.flushMetrics();

    this.metrics.clear();
    this.alerts.clear();
    this.activeTraces.clear();

    this.logger.info('Performance monitor service closed');
  }
}

// 导出单例实例
export const performanceMonitorService = new PerformanceMonitorService();
