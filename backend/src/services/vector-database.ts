// ERP智能助手后端服务 - 向量数据库服务增强版

import { ChromaApi, OpenAIEmbeddingFunction, Collection, IEmbeddingFunction } from 'chromadb';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { ExternalServiceError } from '@/middleware/error-handler';
import { databaseService } from '@/services/database';
import { redisService } from '@/services/redis';

// 向量文档接口
export interface VectorDocument {
  id: string;
  content: string;
  metadata: {
    type: 'operation' | 'documentation' | 'error' | 'guidance';
    source: string;
    timestamp: number;
    userId?: string;
    sessionId?: string;
    tags?: string[];
    language?: string;
    version?: number;
    [key: string]: any;
  };
  embedding?: number[];
}

// 搜索结果接口
export interface SearchResult {
  id: string;
  content: string;
  metadata: VectorDocument['metadata'];
  score: number;
  distance: number;
  relevanceScore?: number;
}

// 搜索选项接口
export interface SearchOptions {
  limit?: number;
  threshold?: number;
  filter?: { [key: string]: any };
  includeMetadata?: boolean;
  includeDocuments?: boolean;
  rerank?: boolean;
  expandQuery?: boolean;
}

// 向量统计接口
export interface VectorStats {
  totalDocuments: number;
  totalSearches: number;
  averageSearchTime: number;
  cacheHitRate: number;
  collectionStats: {
    [collectionName: string]: {
      documentCount: number;
      lastUpdated: number;
      averageEmbeddingTime: number;
      indexSize: number;
    };
  };
}

// 嵌入函数工厂
class EmbeddingFunctionFactory {
  static createOpenAIEmbedding(apiKey: string, model: string = 'text-embedding-ada-002'): IEmbeddingFunction {
    return new OpenAIEmbeddingFunction({
      openai_api_key: apiKey,
      openai_model: model,
    });
  }

  // 可以扩展支持其他嵌入模型
  static createCustomEmbedding(modelName: string): IEmbeddingFunction {
    // 这里可以实现自定义嵌入函数
    throw new Error(`Custom embedding model ${modelName} not implemented`);
  }
}

export class VectorDatabaseService {
  private logger: Logger;
  private client: ChromaApi | null = null;
  private embeddingFunction: IEmbeddingFunction | null = null;
  private collections = new Map<string, Collection>();
  private stats: VectorStats;
  private searchCache = new Map<string, { result: SearchResult[]; timestamp: number }>();
  private cacheTimeout = 300000; // 5分钟缓存

  constructor() {
    this.logger = new Logger('VectorDatabaseService');
    this.stats = {
      totalDocuments: 0,
      totalSearches: 0,
      averageSearchTime: 0,
      cacheHitRate: 0,
      collectionStats: {},
    };
  }

  /**
   * 初始化向量数据库服务
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing vector database service...');

      // 创建ChromaDB客户端
      this.client = new ChromaApi({
        path: `http://${config.vector.host}:${config.vector.port}`,
      });

      // 创建嵌入函数
      if (config.ai.openai.apiKey) {
        this.embeddingFunction = EmbeddingFunctionFactory.createOpenAIEmbedding(
          config.ai.openai.apiKey,
          'text-embedding-ada-002'
        );
      } else {
        this.logger.warn('No OpenAI API key provided, vector search will be limited');
      }

      // 测试连接
      await this.testConnection();

      // 初始化默认集合
      await this.initializeCollections();

      // 加载统计信息
      await this.loadStats();

      this.logger.info('Vector database service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize vector database service:', error);
      throw new ExternalServiceError('ChromaDB', 'Initialization failed', error);
    }
  }

  /**
   * 测试连接
   */
  private async testConnection(): Promise<void> {
    if (!this.client) {
      throw new ExternalServiceError('ChromaDB', 'Client not initialized');
    }

    try {
      const version = await this.client.version();
      this.logger.info('ChromaDB connection test successful', { version });
    } catch (error) {
      throw new ExternalServiceError('ChromaDB', 'Connection test failed', error);
    }
  }

  /**
   * 初始化集合
   */
  private async initializeCollections(): Promise<void> {
    const defaultCollections = [
      {
        name: 'operations',
        description: '用户操作记录向量集合',
        metadata: { type: 'operation', created_at: new Date().toISOString() }
      },
      {
        name: 'documentation',
        description: '文档和指南向量集合',
        metadata: { type: 'documentation', created_at: new Date().toISOString() }
      },
      {
        name: 'errors',
        description: '错误和问题向量集合',
        metadata: { type: 'error', created_at: new Date().toISOString() }
      },
      {
        name: 'guidance',
        description: '智能引导内容向量集合',
        metadata: { type: 'guidance', created_at: new Date().toISOString() }
      },
    ];

    for (const collectionConfig of defaultCollections) {
      await this.getOrCreateCollection(collectionConfig.name, collectionConfig.metadata);
    }
  }

  /**
   * 获取或创建集合
   */
  async getOrCreateCollection(name: string, metadata?: any): Promise<Collection> {
    if (!this.client) {
      throw new ExternalServiceError('ChromaDB', 'Client not initialized');
    }

    try {
      // 检查缓存
      if (this.collections.has(name)) {
        return this.collections.get(name)!;
      }

      let collection: Collection;

      try {
        // 尝试获取现有集合
        collection = await this.client.getCollection({
          name,
          embeddingFunction: this.embeddingFunction,
        });
        this.logger.debug('Retrieved existing collection', { name });
      } catch (error) {
        // 集合不存在，创建新集合
        collection = await this.client.createCollection({
          name,
          embeddingFunction: this.embeddingFunction,
          metadata: {
            description: `ERP Assistant ${name} collection`,
            created_at: new Date().toISOString(),
            ...metadata,
          },
        });
        this.logger.info('Created new collection', { name });
      }

      // 缓存集合
      this.collections.set(name, collection);

      // 更新统计信息
      const count = await collection.count();
      this.stats.collectionStats[name] = {
        documentCount: count,
        lastUpdated: Date.now(),
        averageEmbeddingTime: 0,
        indexSize: 0,
      };

      return collection;
    } catch (error) {
      this.logger.error('Failed to get or create collection:', error, { name });
      throw new ExternalServiceError('ChromaDB', `Failed to get or create collection: ${name}`, error);
    }
  }

  /**
   * 添加文档到向量数据库
   */
  async addDocuments(
    collectionName: string,
    documents: VectorDocument[]
  ): Promise<void> {
    if (documents.length === 0) {
      return;
    }

    const startTime = Date.now();

    try {
      const collection = await this.getOrCreateCollection(collectionName);

      // 准备数据
      const ids = documents.map(doc => doc.id);
      const contents = documents.map(doc => doc.content);
      const metadatas = documents.map(doc => doc.metadata);
      const embeddings = documents.some(doc => doc.embedding) 
        ? documents.map(doc => doc.embedding || [])
        : undefined;

      // 添加到ChromaDB
      await collection.add({
        ids,
        documents: contents,
        metadatas,
        embeddings,
      });

      // 同步到PostgreSQL
      await this.syncToPostgreSQL(collectionName, documents);

      // 更新统计信息
      this.stats.totalDocuments += documents.length;
      const embeddingTime = Date.now() - startTime;
      this.updateCollectionStats(collectionName, documents.length, embeddingTime);

      this.logger.info('Documents added to vector database', {
        collectionName,
        count: documents.length,
        embeddingTime: `${embeddingTime}ms`,
      });
    } catch (error) {
      this.logger.error('Failed to add documents to vector database:', error, {
        collectionName,
        documentCount: documents.length,
      });
      throw new ExternalServiceError('ChromaDB', 'Failed to add documents', error);
    }
  }

  /**
   * 语义搜索文档
   */
  async searchDocuments(
    collectionName: string,
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(collectionName, query, options);

    try {
      // 检查缓存
      const cachedResult = this.getFromCache(cacheKey);
      if (cachedResult) {
        this.stats.totalSearches++;
        this.updateCacheHitRate(true);
        return cachedResult;
      }

      const collection = await this.getOrCreateCollection(collectionName);

      const {
        limit = 10,
        threshold = 0.7,
        filter,
        includeMetadata = true,
        includeDocuments = true,
        rerank = false,
        expandQuery = false,
      } = options;

      // 查询扩展
      let searchQuery = query;
      if (expandQuery) {
        searchQuery = await this.expandQuery(query);
      }

      // 执行向量搜索
      const results = await collection.query({
        queryTexts: [searchQuery],
        nResults: limit * 2, // 获取更多结果用于重排序
        where: filter,
        include: [
          ...(includeMetadata ? ['metadatas'] : []),
          ...(includeDocuments ? ['documents'] : []),
          'distances',
        ],
      });

      // 处理搜索结果
      let searchResults: SearchResult[] = [];

      if (results.ids && results.ids[0]) {
        for (let i = 0; i < results.ids[0].length; i++) {
          const id = results.ids[0][i];
          const content = results.documents?.[0]?.[i] || '';
          const metadata = results.metadatas?.[0]?.[i] || {};
          const distance = results.distances?.[0]?.[i] || 0;
          const score = 1 - distance; // 转换为相似度分数

          // 应用阈值过滤
          if (score >= threshold) {
            searchResults.push({
              id,
              content,
              metadata: metadata as VectorDocument['metadata'],
              score,
              distance,
            });
          }
        }
      }

      // 重排序
      if (rerank && searchResults.length > 0) {
        searchResults = await this.rerankResults(query, searchResults);
      }

      // 限制结果数量
      searchResults = searchResults.slice(0, limit);

      // 计算相关性分数
      searchResults = this.calculateRelevanceScores(query, searchResults);

      // 缓存结果
      this.setCache(cacheKey, searchResults);

      // 更新统计信息
      const searchTime = Date.now() - startTime;
      this.stats.totalSearches++;
      this.stats.averageSearchTime = 
        (this.stats.averageSearchTime * (this.stats.totalSearches - 1) + searchTime) / 
        this.stats.totalSearches;
      this.updateCacheHitRate(false);

      this.logger.debug('Vector search completed', {
        collectionName,
        query: query.substring(0, 100),
        resultCount: searchResults.length,
        searchTime: `${searchTime}ms`,
      });

      return searchResults;
    } catch (error) {
      this.logger.error('Failed to search documents:', error, {
        collectionName,
        query: query.substring(0, 100),
      });
      throw new ExternalServiceError('ChromaDB', 'Failed to search documents', error);
    }
  }

  /**
   * 更新文档
   */
  async updateDocuments(
    collectionName: string,
    documents: VectorDocument[]
  ): Promise<void> {
    if (documents.length === 0) {
      return;
    }

    try {
      const collection = await this.getOrCreateCollection(collectionName);

      const ids = documents.map(doc => doc.id);
      const contents = documents.map(doc => doc.content);
      const metadatas = documents.map(doc => doc.metadata);
      const embeddings = documents.some(doc => doc.embedding) 
        ? documents.map(doc => doc.embedding || [])
        : undefined;

      await collection.update({
        ids,
        documents: contents,
        metadatas,
        embeddings,
      });

      // 同步到PostgreSQL
      await this.syncToPostgreSQL(collectionName, documents, 'update');

      // 清除相关缓存
      this.clearRelatedCache(collectionName);

      // 更新统计信息
      this.stats.collectionStats[collectionName] = {
        ...this.stats.collectionStats[collectionName],
        documentCount: await collection.count(),
        lastUpdated: Date.now(),
      };

      this.logger.info('Documents updated in vector database', {
        collectionName,
        count: documents.length,
      });
    } catch (error) {
      this.logger.error('Failed to update documents:', error, {
        collectionName,
        documentCount: documents.length,
      });
      throw new ExternalServiceError('ChromaDB', 'Failed to update documents', error);
    }
  }

  /**
   * 删除文档
   */
  async deleteDocuments(
    collectionName: string,
    ids: string[]
  ): Promise<void> {
    if (ids.length === 0) {
      return;
    }

    try {
      const collection = await this.getOrCreateCollection(collectionName);

      await collection.delete({
        ids,
      });

      // 从PostgreSQL删除
      await this.deleteFromPostgreSQL(collectionName, ids);

      // 清除相关缓存
      this.clearRelatedCache(collectionName);

      // 更新统计信息
      this.stats.collectionStats[collectionName] = {
        ...this.stats.collectionStats[collectionName],
        documentCount: await collection.count(),
        lastUpdated: Date.now(),
      };

      this.logger.info('Documents deleted from vector database', {
        collectionName,
        count: ids.length,
      });
    } catch (error) {
      this.logger.error('Failed to delete documents:', error, {
        collectionName,
        documentCount: ids.length,
      });
      throw new ExternalServiceError('ChromaDB', 'Failed to delete documents', error);
    }
  }

  // 私有辅助方法

  private async syncToPostgreSQL(
    collectionName: string,
    documents: VectorDocument[],
    operation: 'insert' | 'update' = 'insert'
  ): Promise<void> {
    try {
      for (const doc of documents) {
        const data = {
          id: doc.id,
          collection_name: collectionName,
          document_type: doc.metadata.type,
          title: doc.metadata.title || doc.content.substring(0, 100),
          content: doc.content,
          content_hash: this.calculateHash(doc.content),
          embedding_model: 'text-embedding-ada-002',
          embedding_dimension: doc.embedding?.length || 1536,
          source_id: doc.metadata.source,
          source_type: doc.metadata.type,
          tags: doc.metadata.tags || [],
          language: doc.metadata.language || 'en',
          version: doc.metadata.version || 1,
          is_active: true,
          metadata: doc.metadata,
        };

        if (operation === 'insert') {
          await databaseService.query(
            `INSERT INTO vector_documents (
              id, collection_name, document_type, title, content, content_hash,
              embedding_model, embedding_dimension, source_id, source_type,
              tags, language, version, is_active, metadata, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, NOW(), NOW())
            ON CONFLICT (id) DO UPDATE SET
              content = EXCLUDED.content,
              content_hash = EXCLUDED.content_hash,
              metadata = EXCLUDED.metadata,
              updated_at = NOW()`,
            [
              data.id, data.collection_name, data.document_type, data.title,
              data.content, data.content_hash, data.embedding_model, data.embedding_dimension,
              data.source_id, data.source_type, data.tags, data.language,
              data.version, data.is_active, JSON.stringify(data.metadata)
            ]
          );
        } else {
          await databaseService.query(
            `UPDATE vector_documents SET
              content = $2, content_hash = $3, metadata = $4, updated_at = NOW()
            WHERE id = $1`,
            [data.id, data.content, data.content_hash, JSON.stringify(data.metadata)]
          );
        }
      }
    } catch (error) {
      this.logger.error('Failed to sync to PostgreSQL:', error);
      // 不抛出错误，因为ChromaDB操作已经成功
    }
  }

  private async deleteFromPostgreSQL(collectionName: string, ids: string[]): Promise<void> {
    try {
      await databaseService.query(
        'UPDATE vector_documents SET is_active = false, updated_at = NOW() WHERE id = ANY($1)',
        [ids]
      );
    } catch (error) {
      this.logger.error('Failed to delete from PostgreSQL:', error);
    }
  }

  private calculateHash(content: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  private generateCacheKey(collectionName: string, query: string, options: SearchOptions): string {
    const optionsStr = JSON.stringify(options);
    return `search:${collectionName}:${this.calculateHash(query + optionsStr)}`;
  }

  private getFromCache(key: string): SearchResult[] | null {
    const cached = this.searchCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.result;
    }
    return null;
  }

  private setCache(key: string, result: SearchResult[]): void {
    this.searchCache.set(key, { result, timestamp: Date.now() });
    
    // 清理过期缓存
    if (this.searchCache.size > 1000) {
      this.cleanupCache();
    }
  }

  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, value] of this.searchCache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        this.searchCache.delete(key);
      }
    }
  }

  private clearRelatedCache(collectionName: string): void {
    for (const key of this.searchCache.keys()) {
      if (key.includes(collectionName)) {
        this.searchCache.delete(key);
      }
    }
  }

  private updateCacheHitRate(hit: boolean): void {
    const totalSearches = this.stats.totalSearches;
    const currentHitRate = this.stats.cacheHitRate;
    
    if (hit) {
      this.stats.cacheHitRate = (currentHitRate * (totalSearches - 1) + 1) / totalSearches;
    } else {
      this.stats.cacheHitRate = (currentHitRate * (totalSearches - 1)) / totalSearches;
    }
  }

  private updateCollectionStats(collectionName: string, documentCount: number, embeddingTime: number): void {
    const stats = this.stats.collectionStats[collectionName];
    if (stats) {
      stats.documentCount += documentCount;
      stats.lastUpdated = Date.now();
      stats.averageEmbeddingTime = (stats.averageEmbeddingTime + embeddingTime) / 2;
    }
  }

  private async expandQuery(query: string): Promise<string> {
    // 简单的查询扩展实现
    // 实际应用中可以使用更复杂的查询扩展算法
    const synonyms = await this.getSynonyms(query);
    return [query, ...synonyms].join(' ');
  }

  private async getSynonyms(query: string): Promise<string[]> {
    // 简化实现，实际应用中可以使用同义词词典或AI模型
    return [];
  }

  private async rerankResults(query: string, results: SearchResult[]): Promise<SearchResult[]> {
    // 简单的重排序实现
    // 实际应用中可以使用更复杂的重排序算法
    return results.sort((a, b) => b.score - a.score);
  }

  private calculateRelevanceScores(query: string, results: SearchResult[]): SearchResult[] {
    return results.map(result => ({
      ...result,
      relevanceScore: this.calculateRelevance(query, result),
    }));
  }

  private calculateRelevance(query: string, result: SearchResult): number {
    // 简单的相关性计算
    // 实际应用中可以使用更复杂的相关性算法
    let relevance = result.score;
    
    // 基于元数据的相关性调整
    if (result.metadata.type === 'documentation') {
      relevance *= 1.1; // 文档类型权重稍高
    }
    
    // 基于时间的相关性调整
    const daysSinceCreation = (Date.now() - result.metadata.timestamp) / (1000 * 60 * 60 * 24);
    if (daysSinceCreation < 30) {
      relevance *= 1.05; // 最近的内容权重稍高
    }
    
    return Math.min(relevance, 1.0);
  }

  private async loadStats(): Promise<void> {
    try {
      const cachedStats = await redisService.get<VectorStats>('vector_stats');
      if (cachedStats) {
        this.stats = { ...this.stats, ...cachedStats };
      }
    } catch (error) {
      this.logger.debug('Failed to load cached stats:', error);
    }
  }

  private async saveStats(): Promise<void> {
    try {
      await redisService.set('vector_stats', this.stats, { ttl: 3600 });
    } catch (error) {
      this.logger.debug('Failed to save stats:', error);
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): VectorStats {
    return { ...this.stats };
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      responseTime: number;
      collections: number;
      totalDocuments: number;
      cacheSize: number;
    };
  }> {
    const startTime = Date.now();

    try {
      if (!this.client) {
        return {
          status: 'unhealthy',
          details: {
            connected: false,
            responseTime: Date.now() - startTime,
            collections: 0,
            totalDocuments: 0,
            cacheSize: 0,
          },
        };
      }

      await this.client.version();
      const collections = await this.client.listCollections();

      return {
        status: 'healthy',
        details: {
          connected: true,
          responseTime: Date.now() - startTime,
          collections: collections.length,
          totalDocuments: this.stats.totalDocuments,
          cacheSize: this.searchCache.size,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          responseTime: Date.now() - startTime,
          collections: 0,
          totalDocuments: 0,
          cacheSize: 0,
        },
      };
    }
  }

  /**
   * 关闭连接
   */
  async close(): Promise<void> {
    this.logger.info('Closing vector database connections...');
    
    // 保存统计信息
    await this.saveStats();
    
    // 清理缓存
    this.searchCache.clear();
    this.collections.clear();
    
    this.client = null;
    this.embeddingFunction = null;
    
    this.logger.info('Vector database connections closed');
  }
}

// 导出单例实例
export const vectorDatabaseService = new VectorDatabaseService();
