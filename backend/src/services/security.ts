// ERP智能助手后端服务 - 安全服务

import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { Request, Response, NextFunction } from 'express';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { databaseService } from '@/services/database';
import { redisService } from '@/services/redis';

// 安全事件类型
export enum SecurityEventType {
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILURE = 'login_failure',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  PERMISSION_DENIED = 'permission_denied',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  DATA_BREACH_ATTEMPT = 'data_breach_attempt',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  TOKEN_EXPIRED = 'token_expired',
  INVALID_TOKEN = 'invalid_token',
  PASSWORD_CHANGE = 'password_change',
  ACCOUNT_LOCKED = 'account_locked',
  ACCOUNT_UNLOCKED = 'account_unlocked',
}

// 安全事件接口
export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  userId?: string;
  ipAddress: string;
  userAgent: string;
  resource?: string;
  action?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  details: Record<string, any>;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
}

// 权限接口
export interface Permission {
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

// 角色接口
export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 用户会话接口
export interface UserSession {
  id: string;
  userId: string;
  token: string;
  refreshToken: string;
  ipAddress: string;
  userAgent: string;
  expiresAt: Date;
  isActive: boolean;
  lastActivity: Date;
  metadata: Record<string, any>;
}

// 安全配置接口
export interface SecurityConfig {
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    maxAge: number; // 密码最大有效期（天）
    historyCount: number; // 密码历史记录数量
  };
  sessionPolicy: {
    maxSessions: number; // 用户最大并发会话数
    sessionTimeout: number; // 会话超时时间（秒）
    refreshTokenExpiry: number; // 刷新令牌过期时间（秒）
  };
  lockoutPolicy: {
    maxAttempts: number; // 最大失败尝试次数
    lockoutDuration: number; // 锁定持续时间（秒）
    resetTime: number; // 失败计数重置时间（秒）
  };
  encryptionConfig: {
    algorithm: string;
    keyLength: number;
    ivLength: number;
  };
}

export class SecurityService {
  private logger: Logger;
  private securityConfig: SecurityConfig;
  private encryptionKey: Buffer;

  constructor() {
    this.logger = new Logger('SecurityService');
    
    this.securityConfig = {
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        maxAge: 90,
        historyCount: 5,
      },
      sessionPolicy: {
        maxSessions: 5,
        sessionTimeout: 3600, // 1小时
        refreshTokenExpiry: 604800, // 7天
      },
      lockoutPolicy: {
        maxAttempts: 5,
        lockoutDuration: 900, // 15分钟
        resetTime: 3600, // 1小时
      },
      encryptionConfig: {
        algorithm: 'aes-256-gcm',
        keyLength: 32,
        ivLength: 16,
      },
    };

    // 初始化加密密钥
    this.encryptionKey = this.deriveEncryptionKey(config.security.encryptionSecret);
  }

  /**
   * 初始化安全服务
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing security service...');

      // 创建默认角色
      await this.createDefaultRoles();

      // 清理过期会话
      await this.cleanupExpiredSessions();

      // 启动定期清理任务
      this.startPeriodicCleanup();

      this.logger.info('Security service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize security service:', error);
      throw error;
    }
  }

  /**
   * 用户认证
   */
  async authenticateUser(email: string, password: string, ipAddress: string, userAgent: string): Promise<{
    success: boolean;
    user?: any;
    tokens?: { accessToken: string; refreshToken: string };
    error?: string;
  }> {
    try {
      // 检查账户锁定状态
      const lockoutStatus = await this.checkAccountLockout(email);
      if (lockoutStatus.isLocked) {
        await this.logSecurityEvent({
          type: SecurityEventType.LOGIN_FAILURE,
          ipAddress,
          userAgent,
          severity: 'medium',
          details: { email, reason: 'account_locked', lockoutUntil: lockoutStatus.lockoutUntil },
        });
        
        return {
          success: false,
          error: `Account is locked until ${lockoutStatus.lockoutUntil?.toISOString()}`,
        };
      }

      // 获取用户信息
      const user = await this.getUserByEmail(email);
      if (!user) {
        await this.recordFailedAttempt(email, ipAddress);
        await this.logSecurityEvent({
          type: SecurityEventType.LOGIN_FAILURE,
          ipAddress,
          userAgent,
          severity: 'medium',
          details: { email, reason: 'user_not_found' },
        });
        
        return { success: false, error: 'Invalid credentials' };
      }

      // 验证密码
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        await this.recordFailedAttempt(email, ipAddress);
        await this.logSecurityEvent({
          type: SecurityEventType.LOGIN_FAILURE,
          userId: user.id,
          ipAddress,
          userAgent,
          severity: 'medium',
          details: { email, reason: 'invalid_password' },
        });
        
        return { success: false, error: 'Invalid credentials' };
      }

      // 检查用户状态
      if (!user.is_active) {
        await this.logSecurityEvent({
          type: SecurityEventType.LOGIN_FAILURE,
          userId: user.id,
          ipAddress,
          userAgent,
          severity: 'medium',
          details: { email, reason: 'account_inactive' },
        });
        
        return { success: false, error: 'Account is inactive' };
      }

      // 清除失败尝试记录
      await this.clearFailedAttempts(email);

      // 检查并清理过期会话
      await this.cleanupUserSessions(user.id);

      // 生成访问令牌和刷新令牌
      const tokens = await this.generateTokens(user);

      // 创建用户会话
      await this.createUserSession(user.id, tokens, ipAddress, userAgent);

      // 记录成功登录事件
      await this.logSecurityEvent({
        type: SecurityEventType.LOGIN_SUCCESS,
        userId: user.id,
        ipAddress,
        userAgent,
        severity: 'low',
        details: { email },
      });

      // 更新最后登录时间
      await this.updateLastLogin(user.id);

      return {
        success: true,
        user: this.sanitizeUser(user),
        tokens,
      };

    } catch (error) {
      this.logger.error('Authentication failed:', error);
      
      await this.logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        ipAddress,
        userAgent,
        severity: 'high',
        details: { email, error: error.message },
      });
      
      return { success: false, error: 'Authentication failed' };
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshAccessToken(refreshToken: string, ipAddress: string, userAgent: string): Promise<{
    success: boolean;
    accessToken?: string;
    error?: string;
  }> {
    try {
      // 验证刷新令牌
      const decoded = jwt.verify(refreshToken, config.jwt.refreshSecret) as any;
      
      // 获取用户会话
      const session = await this.getUserSession(decoded.sessionId);
      if (!session || !session.isActive || session.refreshToken !== refreshToken) {
        await this.logSecurityEvent({
          type: SecurityEventType.INVALID_TOKEN,
          userId: decoded.userId,
          ipAddress,
          userAgent,
          severity: 'medium',
          details: { tokenType: 'refresh', reason: 'invalid_session' },
        });
        
        return { success: false, error: 'Invalid refresh token' };
      }

      // 检查会话是否过期
      if (session.expiresAt < new Date()) {
        await this.deactivateSession(session.id);
        
        await this.logSecurityEvent({
          type: SecurityEventType.TOKEN_EXPIRED,
          userId: decoded.userId,
          ipAddress,
          userAgent,
          severity: 'low',
          details: { tokenType: 'refresh' },
        });
        
        return { success: false, error: 'Refresh token expired' };
      }

      // 获取用户信息
      const user = await this.getUserById(decoded.userId);
      if (!user || !user.is_active) {
        return { success: false, error: 'User not found or inactive' };
      }

      // 生成新的访问令牌
      const accessToken = this.generateAccessToken(user, session.id);

      // 更新会话活动时间
      await this.updateSessionActivity(session.id);

      return { success: true, accessToken };

    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        await this.logSecurityEvent({
          type: SecurityEventType.TOKEN_EXPIRED,
          ipAddress,
          userAgent,
          severity: 'low',
          details: { tokenType: 'refresh' },
        });
        
        return { success: false, error: 'Refresh token expired' };
      }

      this.logger.error('Token refresh failed:', error);
      
      await this.logSecurityEvent({
        type: SecurityEventType.INVALID_TOKEN,
        ipAddress,
        userAgent,
        severity: 'medium',
        details: { tokenType: 'refresh', error: error.message },
      });
      
      return { success: false, error: 'Invalid refresh token' };
    }
  }

  /**
   * 验证访问令牌
   */
  async validateAccessToken(token: string): Promise<{
    valid: boolean;
    user?: any;
    session?: UserSession;
    error?: string;
  }> {
    try {
      const decoded = jwt.verify(token, config.jwt.secret) as any;
      
      // 获取用户信息
      const user = await this.getUserById(decoded.userId);
      if (!user || !user.is_active) {
        return { valid: false, error: 'User not found or inactive' };
      }

      // 获取会话信息
      const session = await this.getUserSession(decoded.sessionId);
      if (!session || !session.isActive) {
        return { valid: false, error: 'Session not found or inactive' };
      }

      // 检查会话是否过期
      if (session.expiresAt < new Date()) {
        await this.deactivateSession(session.id);
        return { valid: false, error: 'Session expired' };
      }

      // 更新会话活动时间
      await this.updateSessionActivity(session.id);

      return {
        valid: true,
        user: this.sanitizeUser(user),
        session,
      };

    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return { valid: false, error: 'Token expired' };
      }
      
      return { valid: false, error: 'Invalid token' };
    }
  }

  /**
   * 检查用户权限
   */
  async checkPermission(userId: string, resource: string, action: string): Promise<boolean> {
    try {
      // 获取用户角色
      const userRoles = await this.getUserRoles(userId);
      
      // 检查每个角色的权限
      for (const role of userRoles) {
        const hasPermission = role.permissions.some(permission => 
          permission.resource === resource && permission.action === action
        );
        
        if (hasPermission) {
          return true;
        }
      }

      return false;
    } catch (error) {
      this.logger.error('Permission check failed:', error);
      return false;
    }
  }

  /**
   * 数据加密
   */
  encrypt(data: string): { encrypted: string; iv: string; tag: string } {
    const iv = crypto.randomBytes(this.securityConfig.encryptionConfig.ivLength);
    const cipher = crypto.createCipher(this.securityConfig.encryptionConfig.algorithm, this.encryptionKey);
    cipher.setAAD(Buffer.from('erp-assistant'));
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
    };
  }

  /**
   * 数据解密
   */
  decrypt(encrypted: string, iv: string, tag: string): string {
    const decipher = crypto.createDecipher(this.securityConfig.encryptionConfig.algorithm, this.encryptionKey);
    decipher.setAAD(Buffer.from('erp-assistant'));
    decipher.setAuthTag(Buffer.from(tag, 'hex'));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  /**
   * 密码哈希
   */
  async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * 验证密码强度
   */
  validatePasswordStrength(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const policy = this.securityConfig.passwordPolicy;

    if (password.length < policy.minLength) {
      errors.push(`Password must be at least ${policy.minLength} characters long`);
    }

    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (policy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (policy.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (policy.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * 记录安全事件
   */
  async logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp' | 'resolved'>): Promise<void> {
    try {
      const securityEvent: SecurityEvent = {
        ...event,
        id: this.generateEventId(),
        timestamp: new Date(),
        resolved: false,
      };

      await databaseService.query(`
        INSERT INTO security_events (
          id, type, user_id, ip_address, user_agent, resource, action,
          severity, details, timestamp, resolved
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      `, [
        securityEvent.id,
        securityEvent.type,
        securityEvent.userId,
        securityEvent.ipAddress,
        securityEvent.userAgent,
        securityEvent.resource,
        securityEvent.action,
        securityEvent.severity,
        JSON.stringify(securityEvent.details),
        securityEvent.timestamp,
        securityEvent.resolved,
      ]);

      // 高严重性事件立即告警
      if (event.severity === 'critical' || event.severity === 'high') {
        await this.triggerSecurityAlert(securityEvent);
      }

    } catch (error) {
      this.logger.error('Failed to log security event:', error);
    }
  }

  // 私有方法

  private deriveEncryptionKey(secret: string): Buffer {
    return crypto.pbkdf2Sync(secret, 'erp-assistant-salt', 100000, 32, 'sha256');
  }

  private async createDefaultRoles(): Promise<void> {
    const defaultRoles = [
      {
        id: 'admin',
        name: 'Administrator',
        description: 'Full system access',
        permissions: [
          { resource: '*', action: '*' },
        ],
      },
      {
        id: 'user',
        name: 'User',
        description: 'Standard user access',
        permissions: [
          { resource: 'recordings', action: 'read' },
          { resource: 'recordings', action: 'create' },
          { resource: 'recordings', action: 'update' },
          { resource: 'ai-analysis', action: 'read' },
          { resource: 'ai-analysis', action: 'create' },
        ],
      },
      {
        id: 'viewer',
        name: 'Viewer',
        description: 'Read-only access',
        permissions: [
          { resource: 'recordings', action: 'read' },
          { resource: 'ai-analysis', action: 'read' },
        ],
      },
    ];

    for (const role of defaultRoles) {
      try {
        await databaseService.query(`
          INSERT INTO roles (id, name, description, permissions, is_active, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
          ON CONFLICT (id) DO NOTHING
        `, [
          role.id,
          role.name,
          role.description,
          JSON.stringify(role.permissions),
          true,
        ]);
      } catch (error) {
        this.logger.debug('Role already exists:', role.id);
      }
    }
  }

  private async getUserByEmail(email: string): Promise<any> {
    const result = await databaseService.query(
      'SELECT * FROM users WHERE email = $1',
      [email]
    );
    return result.rows[0];
  }

  private async getUserById(userId: string): Promise<any> {
    const result = await databaseService.query(
      'SELECT * FROM users WHERE id = $1',
      [userId]
    );
    return result.rows[0];
  }

  private async getUserRoles(userId: string): Promise<Role[]> {
    const result = await databaseService.query(`
      SELECT r.* FROM roles r
      JOIN user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = $1 AND r.is_active = true
    `, [userId]);

    return result.rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      permissions: row.permissions,
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    }));
  }

  private generateAccessToken(user: any, sessionId: string): string {
    return jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role,
        sessionId,
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
  }

  private generateRefreshToken(userId: string, sessionId: string): string {
    return jwt.sign(
      { userId, sessionId },
      config.jwt.refreshSecret,
      { expiresIn: '7d' }
    );
  }

  private async generateTokens(user: any): Promise<{ accessToken: string; refreshToken: string }> {
    const sessionId = this.generateSessionId();
    const accessToken = this.generateAccessToken(user, sessionId);
    const refreshToken = this.generateRefreshToken(user.id, sessionId);
    
    return { accessToken, refreshToken };
  }

  private async createUserSession(
    userId: string,
    tokens: { accessToken: string; refreshToken: string },
    ipAddress: string,
    userAgent: string
  ): Promise<void> {
    const sessionId = this.extractSessionId(tokens.accessToken);
    const expiresAt = new Date(Date.now() + this.securityConfig.sessionPolicy.sessionTimeout * 1000);

    await databaseService.query(`
      INSERT INTO user_sessions (
        id, user_id, token, refresh_token, ip_address, user_agent,
        expires_at, is_active, last_activity, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), $9)
    `, [
      sessionId,
      userId,
      tokens.accessToken,
      tokens.refreshToken,
      ipAddress,
      userAgent,
      expiresAt,
      true,
      JSON.stringify({}),
    ]);
  }

  private async checkAccountLockout(email: string): Promise<{ isLocked: boolean; lockoutUntil?: Date }> {
    const key = `lockout:${email}`;
    const lockoutData = await redisService.get<{ until: number }>(key);
    
    if (lockoutData && lockoutData.until > Date.now()) {
      return { isLocked: true, lockoutUntil: new Date(lockoutData.until) };
    }
    
    return { isLocked: false };
  }

  private async recordFailedAttempt(email: string, ipAddress: string): Promise<void> {
    const key = `failed_attempts:${email}`;
    const attempts = await redisService.incr(key);
    
    if (attempts === 1) {
      await redisService.expire(key, this.securityConfig.lockoutPolicy.resetTime);
    }
    
    if (attempts >= this.securityConfig.lockoutPolicy.maxAttempts) {
      const lockoutUntil = Date.now() + this.securityConfig.lockoutPolicy.lockoutDuration * 1000;
      await redisService.set(`lockout:${email}`, { until: lockoutUntil }, {
        ttl: this.securityConfig.lockoutPolicy.lockoutDuration,
      });
      
      await this.logSecurityEvent({
        type: SecurityEventType.ACCOUNT_LOCKED,
        ipAddress,
        userAgent: '',
        severity: 'high',
        details: { email, attempts, lockoutUntil: new Date(lockoutUntil) },
      });
    }
  }

  private async clearFailedAttempts(email: string): Promise<void> {
    await redisService.del(`failed_attempts:${email}`);
  }

  private sanitizeUser(user: any): any {
    const { password, ...sanitized } = user;
    return sanitized;
  }

  private generateEventId(): string {
    return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private extractSessionId(token: string): string {
    try {
      const decoded = jwt.decode(token) as any;
      return decoded.sessionId;
    } catch {
      return this.generateSessionId();
    }
  }

  private async getUserSession(sessionId: string): Promise<UserSession | null> {
    const result = await databaseService.query(
      'SELECT * FROM user_sessions WHERE id = $1',
      [sessionId]
    );
    
    if (result.rows.length === 0) return null;
    
    const row = result.rows[0];
    return {
      id: row.id,
      userId: row.user_id,
      token: row.token,
      refreshToken: row.refresh_token,
      ipAddress: row.ip_address,
      userAgent: row.user_agent,
      expiresAt: row.expires_at,
      isActive: row.is_active,
      lastActivity: row.last_activity,
      metadata: row.metadata,
    };
  }

  private async deactivateSession(sessionId: string): Promise<void> {
    await databaseService.query(
      'UPDATE user_sessions SET is_active = false WHERE id = $1',
      [sessionId]
    );
  }

  private async updateSessionActivity(sessionId: string): Promise<void> {
    await databaseService.query(
      'UPDATE user_sessions SET last_activity = NOW() WHERE id = $1',
      [sessionId]
    );
  }

  private async updateLastLogin(userId: string): Promise<void> {
    await databaseService.query(
      'UPDATE users SET last_login_at = NOW() WHERE id = $1',
      [userId]
    );
  }

  private async cleanupExpiredSessions(): Promise<void> {
    await databaseService.query(
      'UPDATE user_sessions SET is_active = false WHERE expires_at < NOW()'
    );
  }

  private async cleanupUserSessions(userId: string): Promise<void> {
    // 获取用户活跃会话数量
    const result = await databaseService.query(
      'SELECT COUNT(*) as count FROM user_sessions WHERE user_id = $1 AND is_active = true',
      [userId]
    );
    
    const activeSessionCount = parseInt(result.rows[0].count);
    
    // 如果超过最大会话数，删除最旧的会话
    if (activeSessionCount >= this.securityConfig.sessionPolicy.maxSessions) {
      await databaseService.query(`
        UPDATE user_sessions SET is_active = false 
        WHERE user_id = $1 AND is_active = true
        AND id NOT IN (
          SELECT id FROM user_sessions 
          WHERE user_id = $1 AND is_active = true
          ORDER BY last_activity DESC 
          LIMIT $2
        )
      `, [userId, this.securityConfig.sessionPolicy.maxSessions - 1]);
    }
  }

  private startPeriodicCleanup(): void {
    // 每小时清理一次过期会话
    setInterval(async () => {
      try {
        await this.cleanupExpiredSessions();
      } catch (error) {
        this.logger.error('Failed to cleanup expired sessions:', error);
      }
    }, 60 * 60 * 1000);
  }

  private async triggerSecurityAlert(event: SecurityEvent): Promise<void> {
    // 这里可以集成告警系统，如邮件、短信、Slack等
    this.logger.warn('Security alert triggered:', {
      eventId: event.id,
      type: event.type,
      severity: event.severity,
      details: event.details,
    });
  }

  /**
   * 获取安全统计信息
   */
  async getSecurityStats(): Promise<{
    activeSessions: number;
    recentEvents: SecurityEvent[];
    topThreats: Array<{ type: string; count: number }>;
    lockoutCount: number;
  }> {
    try {
      // 活跃会话数
      const activeSessionsResult = await databaseService.query(
        'SELECT COUNT(*) as count FROM user_sessions WHERE is_active = true'
      );
      const activeSessions = parseInt(activeSessionsResult.rows[0].count);

      // 最近24小时的安全事件
      const recentEventsResult = await databaseService.query(`
        SELECT * FROM security_events 
        WHERE timestamp > NOW() - INTERVAL '24 hours'
        ORDER BY timestamp DESC 
        LIMIT 50
      `);
      const recentEvents = recentEventsResult.rows;

      // 威胁类型统计
      const threatStatsResult = await databaseService.query(`
        SELECT type, COUNT(*) as count 
        FROM security_events 
        WHERE timestamp > NOW() - INTERVAL '7 days'
        AND severity IN ('high', 'critical')
        GROUP BY type 
        ORDER BY count DESC 
        LIMIT 10
      `);
      const topThreats = threatStatsResult.rows;

      // 当前锁定账户数
      const lockoutKeys = await redisService.keys('lockout:*');
      const lockoutCount = lockoutKeys.length;

      return {
        activeSessions,
        recentEvents,
        topThreats,
        lockoutCount,
      };
    } catch (error) {
      this.logger.error('Failed to get security stats:', error);
      throw error;
    }
  }

  /**
   * 关闭安全服务
   */
  async close(): Promise<void> {
    this.logger.info('Closing security service...');
    // 清理资源
    this.logger.info('Security service closed');
  }
}

// 导出单例实例
export const securityService = new SecurityService();
