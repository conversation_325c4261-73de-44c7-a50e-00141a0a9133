// ERP智能助手后端服务 - 向量化管道服务

import { OpenAI } from 'openai';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { vectorDatabaseService, VectorDocument } from '@/services/vector-database';
import { databaseService } from '@/services/database';
import { redisService } from '@/services/redis';
import { ExternalServiceError } from '@/middleware/error-handler';

// 向量化任务接口
export interface VectorizationTask {
  id: string;
  type: 'operation' | 'documentation' | 'error' | 'guidance';
  sourceId: string;
  content: string;
  metadata: Record<string, any>;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: Date;
  processedAt?: Date;
  error?: string;
}

// 文档处理器接口
export interface DocumentProcessor {
  canProcess(type: string): boolean;
  process(content: string, metadata: Record<string, any>): Promise<ProcessedDocument>;
}

// 处理后的文档接口
export interface ProcessedDocument {
  title: string;
  content: string;
  chunks: string[];
  metadata: Record<string, any>;
  language: string;
  quality: number; // 0-1之间的质量分数
}

// 向量化统计接口
export interface VectorizationStats {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageProcessingTime: number;
  tokensUsed: number;
  costIncurred: number; // 美分
  queueSize: number;
}

// 操作记录处理器
class OperationProcessor implements DocumentProcessor {
  canProcess(type: string): boolean {
    return type === 'operation';
  }

  async process(content: string, metadata: Record<string, any>): Promise<ProcessedDocument> {
    // 解析操作记录
    const operation = JSON.parse(content);
    
    // 生成描述性文本
    const description = this.generateOperationDescription(operation);
    
    // 分块处理
    const chunks = this.chunkOperationContent(description, operation);
    
    return {
      title: `操作: ${operation.type || 'Unknown'} - ${operation.element_text || 'Element'}`,
      content: description,
      chunks,
      metadata: {
        ...metadata,
        operation_type: operation.type,
        element_selector: operation.element_selector,
        page_url: operation.page_url,
      },
      language: 'zh',
      quality: this.assessOperationQuality(operation),
    };
  }

  private generateOperationDescription(operation: any): string {
    const parts = [];
    
    if (operation.type) {
      parts.push(`执行了${operation.type}操作`);
    }
    
    if (operation.element_text) {
      parts.push(`在元素"${operation.element_text}"上`);
    }
    
    if (operation.element_selector) {
      parts.push(`选择器: ${operation.element_selector}`);
    }
    
    if (operation.input_value) {
      parts.push(`输入值: ${operation.input_value}`);
    }
    
    if (operation.page_url) {
      parts.push(`页面: ${operation.page_url}`);
    }
    
    return parts.join(', ');
  }

  private chunkOperationContent(description: string, operation: any): string[] {
    const chunks = [description];
    
    // 添加技术细节块
    if (operation.element_attributes) {
      chunks.push(`元素属性: ${JSON.stringify(operation.element_attributes)}`);
    }
    
    if (operation.performance_metrics) {
      chunks.push(`性能指标: ${JSON.stringify(operation.performance_metrics)}`);
    }
    
    return chunks;
  }

  private assessOperationQuality(operation: any): number {
    let quality = 0.5; // 基础质量
    
    if (operation.element_text) quality += 0.2;
    if (operation.element_selector) quality += 0.1;
    if (operation.input_value) quality += 0.1;
    if (operation.performance_metrics) quality += 0.1;
    
    return Math.min(quality, 1.0);
  }
}

// 文档处理器
class DocumentationProcessor implements DocumentProcessor {
  canProcess(type: string): boolean {
    return type === 'documentation';
  }

  async process(content: string, metadata: Record<string, any>): Promise<ProcessedDocument> {
    // 提取标题
    const title = this.extractTitle(content);
    
    // 清理内容
    const cleanContent = this.cleanContent(content);
    
    // 分块处理
    const chunks = this.chunkContent(cleanContent);
    
    return {
      title,
      content: cleanContent,
      chunks,
      metadata: {
        ...metadata,
        word_count: cleanContent.split(' ').length,
        has_code: this.hasCodeBlocks(content),
      },
      language: this.detectLanguage(content),
      quality: this.assessDocumentQuality(cleanContent),
    };
  }

  private extractTitle(content: string): string {
    // 尝试从Markdown标题提取
    const titleMatch = content.match(/^#\s+(.+)$/m);
    if (titleMatch) {
      return titleMatch[1];
    }
    
    // 使用前50个字符作为标题
    return content.substring(0, 50).replace(/\n/g, ' ').trim();
  }

  private cleanContent(content: string): string {
    return content
      .replace(/```[\s\S]*?```/g, '[代码块]') // 替换代码块
      .replace(/!\[.*?\]\(.*?\)/g, '[图片]') // 替换图片
      .replace(/\[.*?\]\(.*?\)/g, '$1') // 简化链接
      .replace(/\s+/g, ' ') // 合并空白字符
      .trim();
  }

  private chunkContent(content: string, maxChunkSize: number = 500): string[] {
    const sentences = content.split(/[.!?。！？]/);
    const chunks: string[] = [];
    let currentChunk = '';
    
    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > maxChunkSize && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = sentence;
      } else {
        currentChunk += sentence + '.';
      }
    }
    
    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }
    
    return chunks;
  }

  private hasCodeBlocks(content: string): boolean {
    return /```[\s\S]*?```/.test(content);
  }

  private detectLanguage(content: string): string {
    // 简单的语言检测
    const chineseChars = (content.match(/[\u4e00-\u9fff]/g) || []).length;
    const totalChars = content.length;
    
    return chineseChars / totalChars > 0.3 ? 'zh' : 'en';
  }

  private assessDocumentQuality(content: string): number {
    let quality = 0.3; // 基础质量
    
    const wordCount = content.split(' ').length;
    if (wordCount > 50) quality += 0.2;
    if (wordCount > 200) quality += 0.2;
    
    // 检查结构化程度
    if (content.includes('\n')) quality += 0.1;
    if (/^#/.test(content)) quality += 0.1; // 有标题
    if (content.includes('```')) quality += 0.1; // 有代码示例
    
    return Math.min(quality, 1.0);
  }
}

// 错误处理器
class ErrorProcessor implements DocumentProcessor {
  canProcess(type: string): boolean {
    return type === 'error';
  }

  async process(content: string, metadata: Record<string, any>): Promise<ProcessedDocument> {
    const error = JSON.parse(content);
    
    const description = this.generateErrorDescription(error);
    const chunks = this.chunkErrorContent(description, error);
    
    return {
      title: `错误: ${error.message || 'Unknown Error'}`,
      content: description,
      chunks,
      metadata: {
        ...metadata,
        error_type: error.name || 'Error',
        stack_trace_length: error.stack?.length || 0,
      },
      language: 'zh',
      quality: this.assessErrorQuality(error),
    };
  }

  private generateErrorDescription(error: any): string {
    const parts = [];
    
    if (error.name) {
      parts.push(`错误类型: ${error.name}`);
    }
    
    if (error.message) {
      parts.push(`错误信息: ${error.message}`);
    }
    
    if (error.code) {
      parts.push(`错误代码: ${error.code}`);
    }
    
    return parts.join(', ');
  }

  private chunkErrorContent(description: string, error: any): string[] {
    const chunks = [description];
    
    if (error.stack) {
      // 分割堆栈跟踪
      const stackLines = error.stack.split('\n').slice(0, 10); // 只取前10行
      chunks.push(`堆栈跟踪: ${stackLines.join(' -> ')}`);
    }
    
    return chunks;
  }

  private assessErrorQuality(error: any): number {
    let quality = 0.4; // 基础质量
    
    if (error.message) quality += 0.2;
    if (error.stack) quality += 0.2;
    if (error.code) quality += 0.1;
    if (error.name) quality += 0.1;
    
    return Math.min(quality, 1.0);
  }
}

export class VectorizationPipelineService {
  private logger: Logger;
  private openai: OpenAI | null = null;
  private processors: DocumentProcessor[] = [];
  private taskQueue: VectorizationTask[] = [];
  private isProcessing = false;
  private stats: VectorizationStats;

  constructor() {
    this.logger = new Logger('VectorizationPipelineService');
    this.stats = {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      averageProcessingTime: 0,
      tokensUsed: 0,
      costIncurred: 0,
      queueSize: 0,
    };

    // 注册处理器
    this.processors = [
      new OperationProcessor(),
      new DocumentationProcessor(),
      new ErrorProcessor(),
    ];
  }

  /**
   * 初始化向量化管道
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing vectorization pipeline...');

      // 初始化OpenAI客户端
      if (config.ai.openai.apiKey) {
        this.openai = new OpenAI({
          apiKey: config.ai.openai.apiKey,
        });
      }

      // 加载待处理任务
      await this.loadPendingTasks();

      // 启动处理循环
      this.startProcessingLoop();

      this.logger.info('Vectorization pipeline initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize vectorization pipeline:', error);
      throw error;
    }
  }

  /**
   * 添加向量化任务
   */
  async addTask(
    type: VectorizationTask['type'],
    sourceId: string,
    content: string,
    metadata: Record<string, any> = {},
    priority: VectorizationTask['priority'] = 'medium'
  ): Promise<string> {
    const task: VectorizationTask = {
      id: this.generateTaskId(),
      type,
      sourceId,
      content,
      metadata,
      priority,
      status: 'pending',
      createdAt: new Date(),
    };

    // 添加到队列
    this.taskQueue.push(task);
    this.sortTaskQueue();

    // 保存到Redis
    await this.saveTaskToRedis(task);

    // 更新统计
    this.stats.totalTasks++;
    this.stats.queueSize = this.taskQueue.length;

    this.logger.debug('Added vectorization task', {
      taskId: task.id,
      type,
      priority,
      queueSize: this.taskQueue.length,
    });

    return task.id;
  }

  /**
   * 批量添加任务
   */
  async addBatchTasks(
    tasks: Array<{
      type: VectorizationTask['type'];
      sourceId: string;
      content: string;
      metadata?: Record<string, any>;
      priority?: VectorizationTask['priority'];
    }>
  ): Promise<string[]> {
    const taskIds: string[] = [];

    for (const taskData of tasks) {
      const taskId = await this.addTask(
        taskData.type,
        taskData.sourceId,
        taskData.content,
        taskData.metadata || {},
        taskData.priority || 'medium'
      );
      taskIds.push(taskId);
    }

    this.logger.info('Added batch vectorization tasks', {
      count: tasks.length,
      queueSize: this.taskQueue.length,
    });

    return taskIds;
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<VectorizationTask | null> {
    // 先从内存队列查找
    const memoryTask = this.taskQueue.find(t => t.id === taskId);
    if (memoryTask) {
      return memoryTask;
    }

    // 从Redis查找
    try {
      const task = await redisService.get<VectorizationTask>(`vectorization_task:${taskId}`);
      return task;
    } catch (error) {
      this.logger.error('Failed to get task status:', error);
      return null;
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): {
    queueSize: number;
    processing: boolean;
    stats: VectorizationStats;
  } {
    return {
      queueSize: this.taskQueue.length,
      processing: this.isProcessing,
      stats: { ...this.stats },
    };
  }

  // 私有方法

  private async loadPendingTasks(): Promise<void> {
    try {
      const keys = await redisService.keys('vectorization_task:*');
      
      for (const key of keys) {
        const task = await redisService.get<VectorizationTask>(key);
        if (task && task.status === 'pending') {
          this.taskQueue.push(task);
        }
      }

      this.sortTaskQueue();
      this.stats.queueSize = this.taskQueue.length;

      this.logger.info('Loaded pending vectorization tasks', {
        count: this.taskQueue.length,
      });
    } catch (error) {
      this.logger.error('Failed to load pending tasks:', error);
    }
  }

  private startProcessingLoop(): void {
    setInterval(async () => {
      if (!this.isProcessing && this.taskQueue.length > 0) {
        await this.processNextTask();
      }
    }, 1000); // 每秒检查一次
  }

  private async processNextTask(): Promise<void> {
    if (this.taskQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const task = this.taskQueue.shift()!;
    const startTime = Date.now();

    try {
      this.logger.debug('Processing vectorization task', {
        taskId: task.id,
        type: task.type,
      });

      // 更新任务状态
      task.status = 'processing';
      task.processedAt = new Date();
      await this.saveTaskToRedis(task);

      // 处理文档
      const processor = this.processors.find(p => p.canProcess(task.type));
      if (!processor) {
        throw new Error(`No processor found for type: ${task.type}`);
      }

      const processedDoc = await processor.process(task.content, task.metadata);

      // 生成向量
      const vectors = await this.generateVectors(processedDoc);

      // 保存到向量数据库
      await this.saveToVectorDatabase(task, processedDoc, vectors);

      // 更新任务状态
      task.status = 'completed';
      await this.saveTaskToRedis(task);

      // 更新统计
      const processingTime = Date.now() - startTime;
      this.stats.completedTasks++;
      this.stats.averageProcessingTime = 
        (this.stats.averageProcessingTime * (this.stats.completedTasks - 1) + processingTime) / 
        this.stats.completedTasks;
      this.stats.queueSize = this.taskQueue.length;

      this.logger.info('Vectorization task completed', {
        taskId: task.id,
        type: task.type,
        processingTime: `${processingTime}ms`,
      });

    } catch (error) {
      this.logger.error('Vectorization task failed:', error, {
        taskId: task.id,
        type: task.type,
      });

      // 更新任务状态
      task.status = 'failed';
      task.error = error.message;
      await this.saveTaskToRedis(task);

      // 更新统计
      this.stats.failedTasks++;
      this.stats.queueSize = this.taskQueue.length;

    } finally {
      this.isProcessing = false;
    }
  }

  private async generateVectors(doc: ProcessedDocument): Promise<number[][]> {
    if (!this.openai) {
      throw new ExternalServiceError('OpenAI', 'OpenAI client not initialized');
    }

    try {
      const vectors: number[][] = [];
      
      // 为每个块生成向量
      for (const chunk of doc.chunks) {
        const response = await this.openai.embeddings.create({
          model: 'text-embedding-ada-002',
          input: chunk,
        });

        vectors.push(response.data[0].embedding);
        
        // 更新统计
        this.stats.tokensUsed += response.usage.total_tokens;
        this.stats.costIncurred += this.calculateCost(response.usage.total_tokens);
      }

      return vectors;
    } catch (error) {
      throw new ExternalServiceError('OpenAI', 'Failed to generate embeddings', error);
    }
  }

  private async saveToVectorDatabase(
    task: VectorizationTask,
    doc: ProcessedDocument,
    vectors: number[][]
  ): Promise<void> {
    const documents: VectorDocument[] = doc.chunks.map((chunk, index) => ({
      id: `${task.id}_chunk_${index}`,
      content: chunk,
      metadata: {
        type: task.type,
        source: task.sourceId,
        timestamp: Date.now(),
        title: doc.title,
        language: doc.language,
        quality: doc.quality,
        chunk_index: index,
        total_chunks: doc.chunks.length,
        ...doc.metadata,
      },
      embedding: vectors[index],
    }));

    const collectionName = this.getCollectionName(task.type);
    await vectorDatabaseService.addDocuments(collectionName, documents);
  }

  private getCollectionName(type: VectorizationTask['type']): string {
    const collectionMap = {
      operation: 'operations',
      documentation: 'documentation',
      error: 'errors',
      guidance: 'guidance',
    };
    return collectionMap[type];
  }

  private calculateCost(tokens: number): number {
    // text-embedding-ada-002 定价: $0.0001 / 1K tokens
    return (tokens / 1000) * 0.01; // 美分
  }

  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private sortTaskQueue(): void {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    this.taskQueue.sort((a, b) => {
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return a.createdAt.getTime() - b.createdAt.getTime();
    });
  }

  private async saveTaskToRedis(task: VectorizationTask): Promise<void> {
    try {
      const key = `vectorization_task:${task.id}`;
      const ttl = 7 * 24 * 60 * 60; // 7天
      await redisService.set(key, task, { ttl });
    } catch (error) {
      this.logger.error('Failed to save task to Redis:', error);
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): VectorizationStats {
    return { ...this.stats };
  }

  /**
   * 清理已完成的任务
   */
  async cleanupCompletedTasks(): Promise<number> {
    try {
      const keys = await redisService.keys('vectorization_task:*');
      let cleanedCount = 0;

      for (const key of keys) {
        const task = await redisService.get<VectorizationTask>(key);
        if (task && (task.status === 'completed' || task.status === 'failed')) {
          const daysSinceProcessed = task.processedAt 
            ? (Date.now() - task.processedAt.getTime()) / (1000 * 60 * 60 * 24)
            : 0;
          
          if (daysSinceProcessed > 7) { // 清理7天前的任务
            await redisService.del(key);
            cleanedCount++;
          }
        }
      }

      this.logger.info('Cleaned up completed vectorization tasks', {
        count: cleanedCount,
      });

      return cleanedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup completed tasks:', error);
      return 0;
    }
  }

  /**
   * 关闭服务
   */
  async close(): Promise<void> {
    this.logger.info('Closing vectorization pipeline...');
    this.isProcessing = false;
    this.taskQueue = [];
    this.logger.info('Vectorization pipeline closed');
  }
}

// 导出单例实例
export const vectorizationPipelineService = new VectorizationPipelineService();
