// ERP智能助手后端服务 - 系统健康检查服务

import { EventEmitter } from 'events';
import os from 'os';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { databaseService } from '@/services/database';
import { redisService } from '@/services/redis';
import { vectorDatabaseService } from '@/services/vector-database';
import { webSocketService } from '@/services/websocket';
import { performanceMonitorService } from '@/services/performance-monitor';

// 健康状态枚举
export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  CRITICAL = 'critical',
}

// 健康检查项接口
export interface HealthCheckItem {
  name: string;
  status: HealthStatus;
  responseTime: number;
  message?: string;
  details?: Record<string, any>;
  lastChecked: Date;
  error?: string;
}

// 系统健康报告接口
export interface SystemHealthReport {
  overall: HealthStatus;
  timestamp: Date;
  uptime: number;
  version: string;
  environment: string;
  checks: HealthCheckItem[];
  systemMetrics: {
    cpu: {
      usage: number;
      loadAverage: number[];
    };
    memory: {
      total: number;
      used: number;
      free: number;
      usage: number;
    };
    disk: {
      total: number;
      used: number;
      free: number;
      usage: number;
    };
    network: {
      connections: number;
      bytesIn: number;
      bytesOut: number;
    };
  };
  services: {
    [serviceName: string]: {
      status: HealthStatus;
      responseTime: number;
      details: Record<string, any>;
    };
  };
}

// 健康检查配置接口
export interface HealthCheckConfig {
  interval: number; // 检查间隔（毫秒）
  timeout: number; // 超时时间（毫秒）
  retries: number; // 重试次数
  thresholds: {
    responseTime: {
      warning: number;
      critical: number;
    };
    cpu: {
      warning: number;
      critical: number;
    };
    memory: {
      warning: number;
      critical: number;
    };
    disk: {
      warning: number;
      critical: number;
    };
  };
}

export class HealthCheckService extends EventEmitter {
  private logger: Logger;
  private config: HealthCheckConfig;
  private healthChecks: Map<string, HealthCheckItem> = new Map();
  private checkInterval?: NodeJS.Timeout;
  private startTime = Date.now();
  private lastReport?: SystemHealthReport;

  constructor() {
    super();
    this.logger = new Logger('HealthCheckService');
    
    this.config = {
      interval: 30000, // 30秒
      timeout: 5000, // 5秒
      retries: 3,
      thresholds: {
        responseTime: {
          warning: 1000, // 1秒
          critical: 5000, // 5秒
        },
        cpu: {
          warning: 70, // 70%
          critical: 90, // 90%
        },
        memory: {
          warning: 80, // 80%
          critical: 95, // 95%
        },
        disk: {
          warning: 85, // 85%
          critical: 95, // 95%
        },
      },
    };
  }

  /**
   * 初始化健康检查服务
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing health check service...');

      // 注册健康检查项
      this.registerHealthChecks();

      // 执行初始健康检查
      await this.performHealthCheck();

      // 启动定期健康检查
      this.startPeriodicHealthCheck();

      this.logger.info('Health check service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize health check service:', error);
      throw error;
    }
  }

  /**
   * 执行完整的健康检查
   */
  async performHealthCheck(): Promise<SystemHealthReport> {
    const startTime = Date.now();
    
    try {
      this.logger.debug('Performing system health check...');

      // 并行执行所有健康检查
      const checkPromises = Array.from(this.healthChecks.keys()).map(async (checkName) => {
        try {
          await this.executeHealthCheck(checkName);
        } catch (error) {
          this.logger.error(`Health check failed: ${checkName}`, error);
        }
      });

      await Promise.allSettled(checkPromises);

      // 收集系统指标
      const systemMetrics = await this.collectSystemMetrics();

      // 收集服务状态
      const services = await this.collectServiceStatus();

      // 生成健康报告
      const report: SystemHealthReport = {
        overall: this.calculateOverallHealth(),
        timestamp: new Date(),
        uptime: (Date.now() - this.startTime) / 1000,
        version: process.env.npm_package_version || '1.0.0',
        environment: config.env,
        checks: Array.from(this.healthChecks.values()),
        systemMetrics,
        services,
      };

      this.lastReport = report;

      // 发出健康检查完成事件
      this.emit('healthCheckCompleted', report);

      // 检查是否需要告警
      await this.checkAlerts(report);

      const duration = Date.now() - startTime;
      this.logger.debug('Health check completed', {
        overall: report.overall,
        duration: `${duration}ms`,
        checksCount: report.checks.length,
      });

      return report;
    } catch (error) {
      this.logger.error('Health check failed:', error);
      throw error;
    }
  }

  /**
   * 获取最新的健康报告
   */
  getLatestHealthReport(): SystemHealthReport | null {
    return this.lastReport || null;
  }

  /**
   * 获取特定服务的健康状态
   */
  getServiceHealth(serviceName: string): HealthCheckItem | null {
    return this.healthChecks.get(serviceName) || null;
  }

  /**
   * 添加自定义健康检查
   */
  addHealthCheck(
    name: string,
    checkFunction: () => Promise<{ status: HealthStatus; message?: string; details?: any }>
  ): void {
    const healthCheck: HealthCheckItem = {
      name,
      status: HealthStatus.HEALTHY,
      responseTime: 0,
      lastChecked: new Date(),
    };

    this.healthChecks.set(name, healthCheck);

    // 存储检查函数
    (healthCheck as any).checkFunction = checkFunction;

    this.logger.info('Custom health check added', { name });
  }

  // 私有方法

  private registerHealthChecks(): void {
    // 数据库健康检查
    this.addHealthCheck('database', async () => {
      const result = await databaseService.query('SELECT 1 as health_check');
      return {
        status: result.rows.length > 0 ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
        message: 'Database connection successful',
        details: { rowCount: result.rows.length },
      };
    });

    // Redis健康检查
    this.addHealthCheck('redis', async () => {
      await redisService.ping();
      const info = await redisService.info();
      return {
        status: HealthStatus.HEALTHY,
        message: 'Redis connection successful',
        details: { info },
      };
    });

    // 向量数据库健康检查
    this.addHealthCheck('vector_database', async () => {
      const health = await vectorDatabaseService.healthCheck();
      return {
        status: health.status === 'healthy' ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
        message: `Vector database ${health.status}`,
        details: health.details,
      };
    });

    // WebSocket服务健康检查
    this.addHealthCheck('websocket', async () => {
      const stats = webSocketService.getStats();
      return {
        status: HealthStatus.HEALTHY,
        message: 'WebSocket service operational',
        details: {
          activeConnections: stats.activeConnections,
          totalMessages: stats.totalMessages,
        },
      };
    });

    // 性能监控服务健康检查
    this.addHealthCheck('performance_monitor', async () => {
      const stats = await performanceMonitorService.getPerformanceStats();
      const cpuUsage = stats.systemMetrics.cpuUsage;
      const memoryUsage = stats.systemMetrics.memoryUsage;
      
      let status = HealthStatus.HEALTHY;
      if (cpuUsage > this.config.thresholds.cpu.critical || 
          memoryUsage > this.config.thresholds.memory.critical) {
        status = HealthStatus.CRITICAL;
      } else if (cpuUsage > this.config.thresholds.cpu.warning || 
                 memoryUsage > this.config.thresholds.memory.warning) {
        status = HealthStatus.DEGRADED;
      }

      return {
        status,
        message: 'Performance monitoring active',
        details: {
          cpuUsage,
          memoryUsage,
          responseTime: stats.applicationMetrics.responseTime.avg,
        },
      };
    });

    // 磁盘空间健康检查
    this.addHealthCheck('disk_space', async () => {
      const stats = await this.getDiskStats();
      const usage = (stats.used / stats.total) * 100;
      
      let status = HealthStatus.HEALTHY;
      if (usage > this.config.thresholds.disk.critical) {
        status = HealthStatus.CRITICAL;
      } else if (usage > this.config.thresholds.disk.warning) {
        status = HealthStatus.DEGRADED;
      }

      return {
        status,
        message: `Disk usage: ${usage.toFixed(1)}%`,
        details: stats,
      };
    });

    // 内存健康检查
    this.addHealthCheck('memory', async () => {
      const memInfo = process.memoryUsage();
      const totalMem = os.totalmem();
      const usage = (memInfo.rss / totalMem) * 100;
      
      let status = HealthStatus.HEALTHY;
      if (usage > this.config.thresholds.memory.critical) {
        status = HealthStatus.CRITICAL;
      } else if (usage > this.config.thresholds.memory.warning) {
        status = HealthStatus.DEGRADED;
      }

      return {
        status,
        message: `Memory usage: ${usage.toFixed(1)}%`,
        details: {
          rss: memInfo.rss,
          heapTotal: memInfo.heapTotal,
          heapUsed: memInfo.heapUsed,
          external: memInfo.external,
          usage,
        },
      };
    });
  }

  private async executeHealthCheck(checkName: string): Promise<void> {
    const healthCheck = this.healthChecks.get(checkName);
    if (!healthCheck || !(healthCheck as any).checkFunction) return;

    const startTime = Date.now();
    let retries = 0;

    while (retries <= this.config.retries) {
      try {
        const checkFunction = (healthCheck as any).checkFunction;
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Health check timeout')), this.config.timeout);
        });

        const result = await Promise.race([
          checkFunction(),
          timeoutPromise,
        ]) as { status: HealthStatus; message?: string; details?: any };

        const responseTime = Date.now() - startTime;

        // 根据响应时间调整状态
        let finalStatus = result.status;
        if (responseTime > this.config.thresholds.responseTime.critical) {
          finalStatus = HealthStatus.CRITICAL;
        } else if (responseTime > this.config.thresholds.responseTime.warning) {
          finalStatus = HealthStatus.DEGRADED;
        }

        // 更新健康检查项
        healthCheck.status = finalStatus;
        healthCheck.responseTime = responseTime;
        healthCheck.message = result.message;
        healthCheck.details = result.details;
        healthCheck.lastChecked = new Date();
        healthCheck.error = undefined;

        break; // 成功，退出重试循环

      } catch (error) {
        retries++;
        
        if (retries > this.config.retries) {
          // 所有重试都失败了
          healthCheck.status = HealthStatus.UNHEALTHY;
          healthCheck.responseTime = Date.now() - startTime;
          healthCheck.message = 'Health check failed';
          healthCheck.error = error.message;
          healthCheck.lastChecked = new Date();
          
          this.logger.error(`Health check failed after ${this.config.retries} retries: ${checkName}`, error);
        } else {
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }
  }

  private async collectSystemMetrics(): Promise<SystemHealthReport['systemMetrics']> {
    const memInfo = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;

    const diskStats = await this.getDiskStats();

    return {
      cpu: {
        usage: await this.getCpuUsage(),
        loadAverage: os.loadavg(),
      },
      memory: {
        total: totalMem,
        used: usedMem,
        free: freeMem,
        usage: (usedMem / totalMem) * 100,
      },
      disk: {
        total: diskStats.total,
        used: diskStats.used,
        free: diskStats.free,
        usage: (diskStats.used / diskStats.total) * 100,
      },
      network: {
        connections: 0, // 简化实现
        bytesIn: 0,
        bytesOut: 0,
      },
    };
  }

  private async collectServiceStatus(): Promise<SystemHealthReport['services']> {
    const services: SystemHealthReport['services'] = {};

    for (const [name, healthCheck] of this.healthChecks) {
      services[name] = {
        status: healthCheck.status,
        responseTime: healthCheck.responseTime,
        details: healthCheck.details || {},
      };
    }

    return services;
  }

  private calculateOverallHealth(): HealthStatus {
    const statuses = Array.from(this.healthChecks.values()).map(check => check.status);
    
    if (statuses.includes(HealthStatus.CRITICAL)) {
      return HealthStatus.CRITICAL;
    }
    
    if (statuses.includes(HealthStatus.UNHEALTHY)) {
      return HealthStatus.UNHEALTHY;
    }
    
    if (statuses.includes(HealthStatus.DEGRADED)) {
      return HealthStatus.DEGRADED;
    }
    
    return HealthStatus.HEALTHY;
  }

  private async checkAlerts(report: SystemHealthReport): Promise<void> {
    // 检查是否需要发送告警
    if (report.overall === HealthStatus.CRITICAL || report.overall === HealthStatus.UNHEALTHY) {
      this.emit('healthAlert', {
        severity: report.overall === HealthStatus.CRITICAL ? 'critical' : 'high',
        message: `System health is ${report.overall}`,
        report,
      });
    }

    // 检查特定服务的告警
    for (const check of report.checks) {
      if (check.status === HealthStatus.CRITICAL || check.status === HealthStatus.UNHEALTHY) {
        this.emit('serviceAlert', {
          serviceName: check.name,
          status: check.status,
          message: check.message,
          error: check.error,
        });
      }
    }
  }

  private startPeriodicHealthCheck(): void {
    this.checkInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        this.logger.error('Periodic health check failed:', error);
      }
    }, this.config.interval);

    this.logger.info('Periodic health check started', {
      interval: `${this.config.interval}ms`,
    });
  }

  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      const startTime = Date.now();

      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const endTime = Date.now();
        const totalTime = (endTime - startTime) * 1000; // 转换为微秒

        const cpuPercent = ((endUsage.user + endUsage.system) / totalTime) * 100;
        resolve(Math.min(cpuPercent, 100));
      }, 100);
    });
  }

  private async getDiskStats(): Promise<{ total: number; used: number; free: number }> {
    // 简化实现，实际应用中应该使用更精确的磁盘统计
    return {
      total: 1000000000000, // 1TB
      used: 500000000000,   // 500GB
      free: 500000000000,   // 500GB
    };
  }

  /**
   * 关闭健康检查服务
   */
  async close(): Promise<void> {
    this.logger.info('Closing health check service...');

    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.healthChecks.clear();

    this.logger.info('Health check service closed');
  }
}

// 导出单例实例
export const healthCheckService = new HealthCheckService();
