// ERP智能助手后端服务 - Redis服务

import { createClient, RedisClientType } from 'redis';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { DatabaseError } from '@/middleware/error-handler';

// Redis客户端实例
let redisClient: RedisClientType | null = null;
let subscriberClient: RedisClientType | null = null;

// 缓存统计
interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
  hitRate: number;
}

export class RedisService {
  private logger: Logger;
  private stats: CacheStats;
  private isConnected = false;

  constructor() {
    this.logger = new Logger('RedisService');
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      hitRate: 0,
    };
  }

  /**
   * 初始化Redis连接
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing Redis connection...');

      // 创建主客户端
      redisClient = createClient({
        socket: {
          host: config.redis.host,
          port: config.redis.port,
        },
        password: config.redis.password,
        database: config.redis.db,
        retryDelayOnFailover: 100,
        retryDelayOnClusterDown: 300,
        maxRetriesPerRequest: 3,
      });

      // 创建订阅客户端
      subscriberClient = createClient({
        socket: {
          host: config.redis.host,
          port: config.redis.port,
        },
        password: config.redis.password,
        database: config.redis.db,
      });

      // 设置错误处理
      redisClient.on('error', (err) => {
        this.logger.error('Redis client error:', err);
        this.isConnected = false;
        this.stats.errors++;
      });

      redisClient.on('connect', () => {
        this.logger.info('Redis client connected');
        this.isConnected = true;
      });

      redisClient.on('disconnect', () => {
        this.logger.warn('Redis client disconnected');
        this.isConnected = false;
      });

      subscriberClient.on('error', (err) => {
        this.logger.error('Redis subscriber error:', err);
      });

      // 连接客户端
      await redisClient.connect();
      await subscriberClient.connect();

      // 测试连接
      await this.testConnection();

      this.logger.info('Redis service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Redis service:', error);
      throw new DatabaseError('Redis initialization failed', error);
    }
  }

  /**
   * 测试Redis连接
   */
  private async testConnection(): Promise<void> {
    if (!redisClient) {
      throw new DatabaseError('Redis client not initialized');
    }

    try {
      const pong = await redisClient.ping();
      const info = await redisClient.info('server');
      
      this.logger.info('Redis connection test successful', {
        pong,
        version: info.split('\r\n').find(line => line.startsWith('redis_version:'))?.split(':')[1],
      });
    } catch (error) {
      throw new DatabaseError('Redis connection test failed', error);
    }
  }

  /**
   * 获取值
   */
  async get<T = string>(key: string): Promise<T | null> {
    if (!redisClient || !this.isConnected) {
      this.logger.warn('Redis not available, skipping get operation');
      return null;
    }

    try {
      const value = await redisClient.get(key);
      
      if (value === null) {
        this.stats.misses++;
        this.updateHitRate();
        this.logger.debug('Cache miss', { key });
        return null;
      }

      this.stats.hits++;
      this.updateHitRate();
      this.logger.debug('Cache hit', { key });

      // 尝试解析JSON
      try {
        return JSON.parse(value) as T;
      } catch {
        return value as T;
      }
    } catch (error) {
      this.stats.errors++;
      this.logger.error('Redis get error:', error, { key });
      return null;
    }
  }

  /**
   * 设置值
   */
  async set(
    key: string,
    value: any,
    options?: {
      ttl?: number; // 过期时间（秒）
      nx?: boolean; // 仅当键不存在时设置
      xx?: boolean; // 仅当键存在时设置
    }
  ): Promise<boolean> {
    if (!redisClient || !this.isConnected) {
      this.logger.warn('Redis not available, skipping set operation');
      return false;
    }

    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      
      let result: string | null;
      
      if (options?.ttl) {
        if (options.nx) {
          result = await redisClient.setNX(key, serializedValue);
          if (result) {
            await redisClient.expire(key, options.ttl);
          }
        } else if (options.xx) {
          result = await redisClient.setXX(key, serializedValue);
          if (result) {
            await redisClient.expire(key, options.ttl);
          }
        } else {
          result = await redisClient.setEx(key, options.ttl, serializedValue);
        }
      } else {
        if (options?.nx) {
          result = await redisClient.setNX(key, serializedValue);
        } else if (options?.xx) {
          result = await redisClient.setXX(key, serializedValue);
        } else {
          result = await redisClient.set(key, serializedValue);
        }
      }

      const success = result === 'OK' || result === key || result === 1;
      
      if (success) {
        this.stats.sets++;
        this.logger.debug('Cache set', { key, ttl: options?.ttl });
      }

      return success;
    } catch (error) {
      this.stats.errors++;
      this.logger.error('Redis set error:', error, { key });
      return false;
    }
  }

  /**
   * 删除键
   */
  async del(key: string | string[]): Promise<number> {
    if (!redisClient || !this.isConnected) {
      this.logger.warn('Redis not available, skipping delete operation');
      return 0;
    }

    try {
      const keys = Array.isArray(key) ? key : [key];
      const result = await redisClient.del(keys);
      
      this.stats.deletes += result;
      this.logger.debug('Cache delete', { keys, deleted: result });
      
      return result;
    } catch (error) {
      this.stats.errors++;
      this.logger.error('Redis delete error:', error, { key });
      return 0;
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<boolean> {
    if (!redisClient || !this.isConnected) {
      return false;
    }

    try {
      const result = await redisClient.exists(key);
      return result === 1;
    } catch (error) {
      this.stats.errors++;
      this.logger.error('Redis exists error:', error, { key });
      return false;
    }
  }

  /**
   * 设置过期时间
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    if (!redisClient || !this.isConnected) {
      return false;
    }

    try {
      const result = await redisClient.expire(key, seconds);
      return result === 1;
    } catch (error) {
      this.stats.errors++;
      this.logger.error('Redis expire error:', error, { key, seconds });
      return false;
    }
  }

  /**
   * 获取剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    if (!redisClient || !this.isConnected) {
      return -1;
    }

    try {
      return await redisClient.ttl(key);
    } catch (error) {
      this.stats.errors++;
      this.logger.error('Redis TTL error:', error, { key });
      return -1;
    }
  }

  /**
   * 原子递增
   */
  async incr(key: string, increment: number = 1): Promise<number> {
    if (!redisClient || !this.isConnected) {
      return 0;
    }

    try {
      const result = increment === 1 
        ? await redisClient.incr(key)
        : await redisClient.incrBy(key, increment);
      
      this.logger.debug('Cache increment', { key, increment, result });
      return result;
    } catch (error) {
      this.stats.errors++;
      this.logger.error('Redis increment error:', error, { key, increment });
      return 0;
    }
  }

  /**
   * 获取匹配的键
   */
  async keys(pattern: string): Promise<string[]> {
    if (!redisClient || !this.isConnected) {
      return [];
    }

    try {
      return await redisClient.keys(pattern);
    } catch (error) {
      this.stats.errors++;
      this.logger.error('Redis keys error:', error, { pattern });
      return [];
    }
  }

  /**
   * 清空数据库
   */
  async flushDb(): Promise<boolean> {
    if (!redisClient || !this.isConnected) {
      return false;
    }

    try {
      await redisClient.flushDb();
      this.logger.info('Redis database flushed');
      return true;
    } catch (error) {
      this.stats.errors++;
      this.logger.error('Redis flush error:', error);
      return false;
    }
  }

  /**
   * 发布消息
   */
  async publish(channel: string, message: any): Promise<number> {
    if (!redisClient || !this.isConnected) {
      return 0;
    }

    try {
      const serializedMessage = typeof message === 'string' ? message : JSON.stringify(message);
      const result = await redisClient.publish(channel, serializedMessage);
      
      this.logger.debug('Message published', { channel, subscribers: result });
      return result;
    } catch (error) {
      this.stats.errors++;
      this.logger.error('Redis publish error:', error, { channel });
      return 0;
    }
  }

  /**
   * 订阅频道
   */
  async subscribe(channel: string, callback: (message: any) => void): Promise<void> {
    if (!subscriberClient) {
      throw new DatabaseError('Redis subscriber not initialized');
    }

    try {
      await subscriberClient.subscribe(channel, (message) => {
        try {
          const parsedMessage = JSON.parse(message);
          callback(parsedMessage);
        } catch {
          callback(message);
        }
      });

      this.logger.info('Subscribed to channel', { channel });
    } catch (error) {
      this.stats.errors++;
      this.logger.error('Redis subscribe error:', error, { channel });
      throw error;
    }
  }

  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  /**
   * 获取统计信息
   */
  getStats(): CacheStats {
    this.updateHitRate();
    return { ...this.stats };
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      responseTime: number;
      memoryUsage?: string;
    };
  }> {
    const startTime = Date.now();
    
    try {
      if (!redisClient || !this.isConnected) {
        return {
          status: 'unhealthy',
          details: {
            connected: false,
            responseTime: Date.now() - startTime,
          },
        };
      }

      await redisClient.ping();
      const info = await redisClient.info('memory');
      const memoryUsage = info.split('\r\n').find(line => line.startsWith('used_memory_human:'))?.split(':')[1];
      
      return {
        status: 'healthy',
        details: {
          connected: true,
          responseTime: Date.now() - startTime,
          memoryUsage,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          responseTime: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * 关闭Redis连接
   */
  async close(): Promise<void> {
    this.logger.info('Closing Redis connections...');
    
    if (redisClient) {
      await redisClient.quit();
      redisClient = null;
    }
    
    if (subscriberClient) {
      await subscriberClient.quit();
      subscriberClient = null;
    }
    
    this.isConnected = false;
    this.logger.info('Redis connections closed');
  }

  /**
   * 获取Redis客户端（用于高级操作）
   */
  getClient(): RedisClientType | null {
    return redisClient;
  }
}

// 导出单例实例
export const redisService = new RedisService();
