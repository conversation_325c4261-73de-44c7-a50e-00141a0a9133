// ERP智能助手后端服务 - 相似度匹配算法服务

import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { vectorDatabaseService, SearchResult } from '@/services/vector-database';
import { databaseService } from '@/services/database';

// 相似度匹配结果接口
export interface SimilarityMatch {
  id: string;
  sourceId: string;
  targetId: string;
  similarity: number;
  matchType: 'exact' | 'semantic' | 'structural' | 'behavioral';
  confidence: number;
  metadata: {
    algorithm: string;
    features: string[];
    weights: Record<string, number>;
    [key: string]: any;
  };
}

// 匹配配置接口
export interface MatchingConfig {
  algorithm: 'cosine' | 'euclidean' | 'jaccard' | 'hybrid';
  threshold: number;
  maxResults: number;
  weights: {
    semantic: number;
    structural: number;
    behavioral: number;
    temporal: number;
  };
  features: string[];
}

// 特征提取器接口
export interface FeatureExtractor {
  name: string;
  extract(data: any): Promise<number[]>;
  getWeight(): number;
}

// 语义特征提取器
class SemanticFeatureExtractor implements FeatureExtractor {
  name = 'semantic';
  private logger: Logger;

  constructor() {
    this.logger = new Logger('SemanticFeatureExtractor');
  }

  async extract(data: any): Promise<number[]> {
    try {
      // 如果已有嵌入向量，直接使用
      if (data.embedding && Array.isArray(data.embedding)) {
        return data.embedding;
      }

      // 否则从内容生成嵌入向量
      if (data.content) {
        const results = await vectorDatabaseService.searchDocuments(
          'operations',
          data.content,
          { limit: 1, includeDocuments: false }
        );
        
        if (results.length > 0 && results[0].metadata.embedding) {
          return results[0].metadata.embedding;
        }
      }

      // 返回零向量作为默认值
      return new Array(1536).fill(0);
    } catch (error) {
      this.logger.error('Failed to extract semantic features:', error);
      return new Array(1536).fill(0);
    }
  }

  getWeight(): number {
    return 0.4; // 语义特征权重40%
  }
}

// 结构特征提取器
class StructuralFeatureExtractor implements FeatureExtractor {
  name = 'structural';
  private logger: Logger;

  constructor() {
    this.logger = new Logger('StructuralFeatureExtractor');
  }

  async extract(data: any): Promise<number[]> {
    try {
      const features: number[] = [];

      // DOM结构特征
      if (data.element_selector) {
        features.push(...this.extractSelectorFeatures(data.element_selector));
      }

      if (data.element_xpath) {
        features.push(...this.extractXPathFeatures(data.element_xpath));
      }

      // 页面结构特征
      if (data.page_url) {
        features.push(...this.extractUrlFeatures(data.page_url));
      }

      // 元素属性特征
      if (data.element_attributes) {
        features.push(...this.extractAttributeFeatures(data.element_attributes));
      }

      // 标准化到固定长度
      return this.normalizeFeatures(features, 100);
    } catch (error) {
      this.logger.error('Failed to extract structural features:', error);
      return new Array(100).fill(0);
    }
  }

  private extractSelectorFeatures(selector: string): number[] {
    const features: number[] = [];
    
    // 选择器类型特征
    features.push(selector.includes('#') ? 1 : 0); // ID选择器
    features.push(selector.includes('.') ? 1 : 0); // 类选择器
    features.push(selector.includes('[') ? 1 : 0); // 属性选择器
    features.push(selector.includes(':') ? 1 : 0); // 伪选择器
    
    // 选择器复杂度
    features.push(selector.split(' ').length / 10); // 层级深度
    features.push(selector.length / 100); // 选择器长度
    
    return features;
  }

  private extractXPathFeatures(xpath: string): number[] {
    const features: number[] = [];
    
    // XPath特征
    features.push(xpath.includes('//') ? 1 : 0); // 相对路径
    features.push(xpath.includes('@') ? 1 : 0); // 属性查询
    features.push(xpath.includes('[') ? 1 : 0); // 条件查询
    features.push((xpath.match(/\//g) || []).length / 20); // 路径深度
    
    return features;
  }

  private extractUrlFeatures(url: string): number[] {
    const features: number[] = [];
    
    try {
      const urlObj = new URL(url);
      
      // URL结构特征
      features.push(urlObj.pathname.split('/').length / 10); // 路径深度
      features.push(urlObj.search ? 1 : 0); // 是否有查询参数
      features.push(urlObj.hash ? 1 : 0); // 是否有锚点
      features.push(urlObj.pathname.length / 100); // 路径长度
      
    } catch {
      features.push(0, 0, 0, 0);
    }
    
    return features;
  }

  private extractAttributeFeatures(attributes: any): number[] {
    const features: number[] = [];
    
    if (typeof attributes === 'object' && attributes !== null) {
      const keys = Object.keys(attributes);
      
      // 属性数量
      features.push(keys.length / 20);
      
      // 常见属性存在性
      features.push(keys.includes('id') ? 1 : 0);
      features.push(keys.includes('class') ? 1 : 0);
      features.push(keys.includes('type') ? 1 : 0);
      features.push(keys.includes('name') ? 1 : 0);
      features.push(keys.includes('value') ? 1 : 0);
    } else {
      features.push(0, 0, 0, 0, 0, 0);
    }
    
    return features;
  }

  private normalizeFeatures(features: number[], targetLength: number): number[] {
    // 截断或填充到目标长度
    if (features.length > targetLength) {
      return features.slice(0, targetLength);
    } else {
      return [...features, ...new Array(targetLength - features.length).fill(0)];
    }
  }

  getWeight(): number {
    return 0.3; // 结构特征权重30%
  }
}

// 行为特征提取器
class BehavioralFeatureExtractor implements FeatureExtractor {
  name = 'behavioral';
  private logger: Logger;

  constructor() {
    this.logger = new Logger('BehavioralFeatureExtractor');
  }

  async extract(data: any): Promise<number[]> {
    try {
      const features: number[] = [];

      // 操作类型特征
      if (data.type) {
        features.push(...this.extractOperationTypeFeatures(data.type));
      }

      // 时间特征
      if (data.timestamp) {
        features.push(...this.extractTemporalFeatures(data.timestamp));
      }

      // 性能特征
      if (data.performance_metrics) {
        features.push(...this.extractPerformanceFeatures(data.performance_metrics));
      }

      // 用户行为特征
      if (data.user_id) {
        features.push(...await this.extractUserBehaviorFeatures(data.user_id));
      }

      return this.normalizeFeatures(features, 50);
    } catch (error) {
      this.logger.error('Failed to extract behavioral features:', error);
      return new Array(50).fill(0);
    }
  }

  private extractOperationTypeFeatures(type: string): number[] {
    const operationTypes = ['click', 'input', 'submit', 'navigate', 'scroll', 'hover'];
    return operationTypes.map(opType => opType === type ? 1 : 0);
  }

  private extractTemporalFeatures(timestamp: number): number[] {
    const date = new Date(timestamp);
    const features: number[] = [];
    
    // 时间特征
    features.push(date.getHours() / 24); // 小时
    features.push(date.getDay() / 7); // 星期
    features.push(date.getMonth() / 12); // 月份
    
    // 相对时间特征
    const now = Date.now();
    const daysSince = (now - timestamp) / (1000 * 60 * 60 * 24);
    features.push(Math.min(daysSince / 365, 1)); // 距今天数（最大1年）
    
    return features;
  }

  private extractPerformanceFeatures(metrics: any): number[] {
    const features: number[] = [];
    
    if (typeof metrics === 'object' && metrics !== null) {
      // 响应时间特征
      features.push(Math.min((metrics.responseTime || 0) / 5000, 1)); // 最大5秒
      features.push(Math.min((metrics.loadTime || 0) / 10000, 1)); // 最大10秒
      
      // 资源使用特征
      features.push(Math.min((metrics.memoryUsage || 0) / 1000000, 1)); // 最大1MB
      features.push(Math.min((metrics.cpuUsage || 0) / 100, 1)); // 最大100%
    } else {
      features.push(0, 0, 0, 0);
    }
    
    return features;
  }

  private async extractUserBehaviorFeatures(userId: string): Promise<number[]> {
    try {
      // 查询用户历史行为统计
      const result = await databaseService.query(`
        SELECT 
          COUNT(*) as total_operations,
          COUNT(DISTINCT type) as operation_types,
          AVG(EXTRACT(EPOCH FROM (created_at - LAG(created_at) OVER (ORDER BY created_at)))) as avg_interval
        FROM operation_records 
        WHERE session_id IN (
          SELECT id FROM recording_sessions WHERE user_id = $1
        )
      `, [userId]);

      if (result.rows.length > 0) {
        const stats = result.rows[0];
        return [
          Math.min(parseInt(stats.total_operations) / 10000, 1), // 总操作数
          Math.min(parseInt(stats.operation_types) / 20, 1), // 操作类型多样性
          Math.min((parseFloat(stats.avg_interval) || 0) / 60, 1), // 平均间隔时间
        ];
      }
    } catch (error) {
      this.logger.error('Failed to extract user behavior features:', error);
    }
    
    return [0, 0, 0];
  }

  private normalizeFeatures(features: number[], targetLength: number): number[] {
    if (features.length > targetLength) {
      return features.slice(0, targetLength);
    } else {
      return [...features, ...new Array(targetLength - features.length).fill(0)];
    }
  }

  getWeight(): number {
    return 0.2; // 行为特征权重20%
  }
}

// 时间特征提取器
class TemporalFeatureExtractor implements FeatureExtractor {
  name = 'temporal';

  async extract(data: any): Promise<number[]> {
    const features: number[] = [];
    
    if (data.timestamp) {
      const date = new Date(data.timestamp);
      const now = new Date();
      
      // 时间差特征
      const timeDiff = now.getTime() - date.getTime();
      features.push(Math.min(timeDiff / (1000 * 60 * 60 * 24 * 365), 1)); // 年
      features.push(Math.min(timeDiff / (1000 * 60 * 60 * 24 * 30), 1)); // 月
      features.push(Math.min(timeDiff / (1000 * 60 * 60 * 24), 1)); // 天
      
      // 周期性特征
      features.push(Math.sin(2 * Math.PI * date.getHours() / 24)); // 小时周期
      features.push(Math.cos(2 * Math.PI * date.getHours() / 24));
      features.push(Math.sin(2 * Math.PI * date.getDay() / 7)); // 星期周期
      features.push(Math.cos(2 * Math.PI * date.getDay() / 7));
    } else {
      features.push(0, 0, 0, 0, 0, 0, 0);
    }
    
    return features;
  }

  getWeight(): number {
    return 0.1; // 时间特征权重10%
  }
}

export class SimilarityMatcherService {
  private logger: Logger;
  private extractors: FeatureExtractor[] = [];

  constructor() {
    this.logger = new Logger('SimilarityMatcherService');
    
    // 初始化特征提取器
    this.extractors = [
      new SemanticFeatureExtractor(),
      new StructuralFeatureExtractor(),
      new BehavioralFeatureExtractor(),
      new TemporalFeatureExtractor(),
    ];
  }

  /**
   * 查找相似项目
   */
  async findSimilar(
    sourceData: any,
    config: Partial<MatchingConfig> = {}
  ): Promise<SimilarityMatch[]> {
    const matchingConfig: MatchingConfig = {
      algorithm: 'hybrid',
      threshold: 0.7,
      maxResults: 10,
      weights: {
        semantic: 0.4,
        structural: 0.3,
        behavioral: 0.2,
        temporal: 0.1,
      },
      features: ['semantic', 'structural', 'behavioral', 'temporal'],
      ...config,
    };

    try {
      this.logger.debug('Finding similar items', {
        algorithm: matchingConfig.algorithm,
        threshold: matchingConfig.threshold,
      });

      // 提取源数据特征
      const sourceFeatures = await this.extractFeatures(sourceData, matchingConfig.features);

      // 获取候选项目
      const candidates = await this.getCandidates(sourceData);

      // 计算相似度
      const matches: SimilarityMatch[] = [];
      
      for (const candidate of candidates) {
        const candidateFeatures = await this.extractFeatures(candidate, matchingConfig.features);
        
        const similarity = this.calculateSimilarity(
          sourceFeatures,
          candidateFeatures,
          matchingConfig
        );

        if (similarity >= matchingConfig.threshold) {
          matches.push({
            id: this.generateMatchId(),
            sourceId: sourceData.id,
            targetId: candidate.id,
            similarity,
            matchType: this.determineMatchType(similarity),
            confidence: this.calculateConfidence(similarity, sourceFeatures, candidateFeatures),
            metadata: {
              algorithm: matchingConfig.algorithm,
              features: matchingConfig.features,
              weights: matchingConfig.weights,
              sourceType: sourceData.type,
              targetType: candidate.type,
            },
          });
        }
      }

      // 排序并限制结果数量
      matches.sort((a, b) => b.similarity - a.similarity);
      const finalMatches = matches.slice(0, matchingConfig.maxResults);

      this.logger.info('Similarity matching completed', {
        sourceId: sourceData.id,
        candidatesCount: candidates.length,
        matchesCount: finalMatches.length,
      });

      return finalMatches;
    } catch (error) {
      this.logger.error('Similarity matching failed:', error);
      throw error;
    }
  }

  /**
   * 批量相似度匹配
   */
  async findSimilarBatch(
    sourceDataList: any[],
    config: Partial<MatchingConfig> = {}
  ): Promise<Map<string, SimilarityMatch[]>> {
    const results = new Map<string, SimilarityMatch[]>();

    for (const sourceData of sourceDataList) {
      try {
        const matches = await this.findSimilar(sourceData, config);
        results.set(sourceData.id, matches);
      } catch (error) {
        this.logger.error('Batch similarity matching failed for item:', error, {
          sourceId: sourceData.id,
        });
        results.set(sourceData.id, []);
      }
    }

    return results;
  }

  // 私有方法

  private async extractFeatures(data: any, featureTypes: string[]): Promise<Map<string, number[]>> {
    const features = new Map<string, number[]>();

    for (const extractor of this.extractors) {
      if (featureTypes.includes(extractor.name)) {
        try {
          const extractedFeatures = await extractor.extract(data);
          features.set(extractor.name, extractedFeatures);
        } catch (error) {
          this.logger.error(`Failed to extract ${extractor.name} features:`, error);
          features.set(extractor.name, []);
        }
      }
    }

    return features;
  }

  private async getCandidates(sourceData: any): Promise<any[]> {
    try {
      // 从数据库获取候选项目
      const result = await databaseService.query(`
        SELECT * FROM operation_records 
        WHERE id != $1 
        AND type = $2 
        ORDER BY created_at DESC 
        LIMIT 1000
      `, [sourceData.id, sourceData.type]);

      return result.rows;
    } catch (error) {
      this.logger.error('Failed to get candidates:', error);
      return [];
    }
  }

  private calculateSimilarity(
    sourceFeatures: Map<string, number[]>,
    targetFeatures: Map<string, number[]>,
    config: MatchingConfig
  ): number {
    let totalSimilarity = 0;
    let totalWeight = 0;

    for (const [featureType, sourceVector] of sourceFeatures) {
      const targetVector = targetFeatures.get(featureType);
      if (!targetVector || sourceVector.length === 0 || targetVector.length === 0) {
        continue;
      }

      const weight = config.weights[featureType as keyof typeof config.weights] || 0;
      if (weight === 0) continue;

      let similarity = 0;
      
      switch (config.algorithm) {
        case 'cosine':
          similarity = this.cosineSimilarity(sourceVector, targetVector);
          break;
        case 'euclidean':
          similarity = this.euclideanSimilarity(sourceVector, targetVector);
          break;
        case 'jaccard':
          similarity = this.jaccardSimilarity(sourceVector, targetVector);
          break;
        case 'hybrid':
        default:
          similarity = this.hybridSimilarity(sourceVector, targetVector);
          break;
      }

      totalSimilarity += similarity * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? totalSimilarity / totalWeight : 0;
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    const minLength = Math.min(a.length, b.length);
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < minLength; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    const denominator = Math.sqrt(normA) * Math.sqrt(normB);
    return denominator === 0 ? 0 : dotProduct / denominator;
  }

  private euclideanSimilarity(a: number[], b: number[]): number {
    const minLength = Math.min(a.length, b.length);
    let sumSquaredDiff = 0;

    for (let i = 0; i < minLength; i++) {
      const diff = a[i] - b[i];
      sumSquaredDiff += diff * diff;
    }

    const distance = Math.sqrt(sumSquaredDiff);
    return 1 / (1 + distance); // 转换为相似度
  }

  private jaccardSimilarity(a: number[], b: number[]): number {
    const minLength = Math.min(a.length, b.length);
    let intersection = 0;
    let union = 0;

    for (let i = 0; i < minLength; i++) {
      const aVal = a[i] > 0.5 ? 1 : 0;
      const bVal = b[i] > 0.5 ? 1 : 0;
      
      if (aVal === 1 && bVal === 1) {
        intersection++;
      }
      if (aVal === 1 || bVal === 1) {
        union++;
      }
    }

    return union === 0 ? 0 : intersection / union;
  }

  private hybridSimilarity(a: number[], b: number[]): number {
    const cosine = this.cosineSimilarity(a, b);
    const euclidean = this.euclideanSimilarity(a, b);
    const jaccard = this.jaccardSimilarity(a, b);
    
    // 加权组合
    return 0.5 * cosine + 0.3 * euclidean + 0.2 * jaccard;
  }

  private determineMatchType(similarity: number): SimilarityMatch['matchType'] {
    if (similarity >= 0.95) return 'exact';
    if (similarity >= 0.85) return 'semantic';
    if (similarity >= 0.75) return 'structural';
    return 'behavioral';
  }

  private calculateConfidence(
    similarity: number,
    sourceFeatures: Map<string, number[]>,
    targetFeatures: Map<string, number[]>
  ): number {
    let confidence = similarity;

    // 基于特征完整性调整置信度
    const featureCompleteness = Math.min(sourceFeatures.size, targetFeatures.size) / 4; // 4个特征类型
    confidence *= featureCompleteness;

    // 基于特征向量长度调整置信度
    let avgVectorLength = 0;
    let vectorCount = 0;
    
    for (const [, vector] of sourceFeatures) {
      if (vector.length > 0) {
        avgVectorLength += vector.length;
        vectorCount++;
      }
    }
    
    if (vectorCount > 0) {
      avgVectorLength /= vectorCount;
      const lengthFactor = Math.min(avgVectorLength / 100, 1); // 假设理想长度为100
      confidence *= lengthFactor;
    }

    return Math.min(confidence, 1.0);
  }

  private generateMatchId(): string {
    return `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 导出单例实例
export const similarityMatcherService = new SimilarityMatcherService();
