// ERP智能助手后端服务 - 数据库服务

import { Pool, PoolClient, QueryResult } from 'pg';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { DatabaseError } from '@/middleware/error-handler';

// 数据库连接池
let pool: Pool | null = null;

// 查询统计
interface QueryStats {
  totalQueries: number;
  slowQueries: number;
  failedQueries: number;
  averageExecutionTime: number;
  connectionPoolStats: {
    totalConnections: number;
    idleConnections: number;
    waitingClients: number;
  };
}

export class DatabaseService {
  private logger: Logger;
  private stats: QueryStats;
  private slowQueryThreshold = 1000; // 1秒

  constructor() {
    this.logger = new Logger('DatabaseService');
    this.stats = {
      totalQueries: 0,
      slowQueries: 0,
      failedQueries: 0,
      averageExecutionTime: 0,
      connectionPoolStats: {
        totalConnections: 0,
        idleConnections: 0,
        waitingClients: 0,
      },
    };
  }

  /**
   * 初始化数据库连接
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing database connection...');

      // 创建连接池
      pool = new Pool({
        host: config.database.host,
        port: config.database.port,
        database: config.database.database,
        user: config.database.user,
        password: config.database.password,
        ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
        min: config.database.pool.min,
        max: config.database.pool.max,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 10000,
        statement_timeout: 30000,
        query_timeout: 30000,
      });

      // 监听连接池事件
      pool.on('connect', (client) => {
        this.logger.debug('New database client connected');
        this.updateConnectionStats();
      });

      pool.on('remove', (client) => {
        this.logger.debug('Database client removed');
        this.updateConnectionStats();
      });

      pool.on('error', (err, client) => {
        this.logger.error('Database pool error:', err);
      });

      // 测试连接
      await this.testConnection();

      this.logger.info('Database service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize database service:', error);
      throw new DatabaseError('Database initialization failed', error);
    }
  }

  /**
   * 测试数据库连接
   */
  private async testConnection(): Promise<void> {
    const client = await this.getClient();
    try {
      const result = await client.query('SELECT NOW() as current_time, version() as version');
      this.logger.info('Database connection test successful', {
        currentTime: result.rows[0].current_time,
        version: result.rows[0].version.split(' ')[0],
      });
    } finally {
      client.release();
    }
  }

  /**
   * 获取数据库客户端
   */
  async getClient(): Promise<PoolClient> {
    if (!pool) {
      throw new DatabaseError('Database not initialized');
    }

    try {
      const client = await pool.connect();
      return client;
    } catch (error) {
      this.logger.error('Failed to get database client:', error);
      throw new DatabaseError('Failed to get database connection', error);
    }
  }

  /**
   * 执行查询
   */
  async query<T = any>(
    text: string,
    params: any[] = [],
    client?: PoolClient
  ): Promise<QueryResult<T>> {
    const startTime = Date.now();
    const useClient = client || pool;

    if (!useClient) {
      throw new DatabaseError('Database not initialized');
    }

    try {
      this.logger.debug('Executing query', {
        sql: text.replace(/\s+/g, ' ').trim(),
        params: params.length > 0 ? params : undefined,
      });

      const result = await useClient.query<T>(text, params);
      const executionTime = Date.now() - startTime;

      // 更新统计信息
      this.updateQueryStats(executionTime, false);

      // 记录慢查询
      if (executionTime > this.slowQueryThreshold) {
        this.logger.warn('Slow query detected', {
          sql: text.replace(/\s+/g, ' ').trim(),
          executionTime,
          rowCount: result.rowCount,
        });
      }

      this.logger.debug('Query executed successfully', {
        executionTime,
        rowCount: result.rowCount,
      });

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.updateQueryStats(executionTime, true);

      this.logger.error('Query execution failed', {
        sql: text.replace(/\s+/g, ' ').trim(),
        params,
        executionTime,
        error: error.message,
      });

      throw new DatabaseError('Query execution failed', error);
    }
  }

  /**
   * 执行事务
   */
  async transaction<T>(
    callback: (client: PoolClient) => Promise<T>
  ): Promise<T> {
    const client = await this.getClient();

    try {
      await client.query('BEGIN');
      this.logger.debug('Transaction started');

      const result = await callback(client);

      await client.query('COMMIT');
      this.logger.debug('Transaction committed');

      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.warn('Transaction rolled back', { error: error.message });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * 批量插入
   */
  async batchInsert(
    table: string,
    columns: string[],
    values: any[][],
    onConflict?: string
  ): Promise<QueryResult> {
    if (values.length === 0) {
      throw new DatabaseError('No values provided for batch insert');
    }

    const placeholders = values
      .map((_, rowIndex) =>
        `(${columns.map((_, colIndex) => `$${rowIndex * columns.length + colIndex + 1}`).join(', ')})`
      )
      .join(', ');

    const flatValues = values.flat();
    const conflictClause = onConflict ? ` ON CONFLICT ${onConflict}` : '';

    const query = `
      INSERT INTO ${table} (${columns.join(', ')})
      VALUES ${placeholders}
      ${conflictClause}
    `;

    return this.query(query, flatValues);
  }

  /**
   * 分页查询
   */
  async paginate<T = any>(
    baseQuery: string,
    params: any[] = [],
    page: number = 1,
    limit: number = 20,
    countQuery?: string
  ): Promise<{
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const offset = (page - 1) * limit;

    // 执行数据查询
    const dataQuery = `${baseQuery} LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    const dataResult = await this.query<T>(dataQuery, [...params, limit, offset]);

    // 执行计数查询
    const totalCountQuery = countQuery || `SELECT COUNT(*) as count FROM (${baseQuery}) as count_query`;
    const countResult = await this.query<{ count: string }>(totalCountQuery, params);
    const total = parseInt(countResult.rows[0].count, 10);

    const totalPages = Math.ceil(total / limit);

    return {
      data: dataResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * 更新查询统计
   */
  private updateQueryStats(executionTime: number, failed: boolean): void {
    this.stats.totalQueries++;
    
    if (failed) {
      this.stats.failedQueries++;
    }

    if (executionTime > this.slowQueryThreshold) {
      this.stats.slowQueries++;
    }

    // 更新平均执行时间
    this.stats.averageExecutionTime = 
      (this.stats.averageExecutionTime * (this.stats.totalQueries - 1) + executionTime) / 
      this.stats.totalQueries;
  }

  /**
   * 更新连接池统计
   */
  private updateConnectionStats(): void {
    if (pool) {
      this.stats.connectionPoolStats = {
        totalConnections: pool.totalCount,
        idleConnections: pool.idleCount,
        waitingClients: pool.waitingCount,
      };
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): QueryStats {
    this.updateConnectionStats();
    return { ...this.stats };
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      responseTime: number;
      poolStats: typeof this.stats.connectionPoolStats;
    };
  }> {
    const startTime = Date.now();
    
    try {
      await this.query('SELECT 1');
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'healthy',
        details: {
          connected: true,
          responseTime,
          poolStats: this.stats.connectionPoolStats,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          responseTime: Date.now() - startTime,
          poolStats: this.stats.connectionPoolStats,
        },
      };
    }
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (pool) {
      this.logger.info('Closing database connections...');
      await pool.end();
      pool = null;
      this.logger.info('Database connections closed');
    }
  }
}

// 导出单例实例
export const databaseService = new DatabaseService();
