// ERP智能助手后端服务 - 数据同步服务

import { EventEmitter } from 'events';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { databaseService } from '@/services/database';
import { redisService } from '@/services/redis';
import { ExternalServiceError } from '@/middleware/error-handler';

// 同步操作类型
export enum SyncOperation {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  BATCH = 'batch',
}

// 同步状态
export enum SyncStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CONFLICT = 'conflict',
}

// 数据变更记录接口
export interface DataChangeRecord {
  id: string;
  entityType: string;
  entityId: string;
  operation: SyncOperation;
  data: any;
  previousData?: any;
  userId: string;
  sessionId?: string;
  timestamp: number;
  version: number;
  checksum: string;
  metadata: {
    source: 'client' | 'server';
    clientId?: string;
    batchId?: string;
    [key: string]: any;
  };
}

// 同步任务接口
export interface SyncTask {
  id: string;
  userId: string;
  clientId: string;
  changes: DataChangeRecord[];
  status: SyncStatus;
  priority: 'low' | 'medium' | 'high';
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  error?: string;
  retryCount: number;
  maxRetries: number;
}

// 同步结果接口
export interface SyncResult {
  taskId: string;
  status: SyncStatus;
  processedChanges: number;
  failedChanges: number;
  conflicts: DataConflict[];
  appliedChanges: DataChangeRecord[];
  error?: string;
  duration: number;
}

// 数据冲突接口
export interface DataConflict {
  id: string;
  entityType: string;
  entityId: string;
  clientVersion: number;
  serverVersion: number;
  clientData: any;
  serverData: any;
  conflictType: 'version' | 'concurrent' | 'deleted';
  timestamp: number;
  resolved: boolean;
  resolution?: 'client' | 'server' | 'merge' | 'manual';
}

// 同步统计接口
export interface SyncStats {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  conflictTasks: number;
  averageProcessingTime: number;
  totalChangesProcessed: number;
  conflictRate: number;
  clientStats: {
    [clientId: string]: {
      totalTasks: number;
      lastSyncTime: number;
      averageLatency: number;
    };
  };
}

export class DataSyncService extends EventEmitter {
  private logger: Logger;
  private syncTasks = new Map<string, SyncTask>();
  private conflicts = new Map<string, DataConflict>();
  private stats: SyncStats;
  private isProcessing = false;
  private processingInterval?: NodeJS.Timeout;

  constructor() {
    super();
    this.logger = new Logger('DataSyncService');
    this.stats = {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      conflictTasks: 0,
      averageProcessingTime: 0,
      totalChangesProcessed: 0,
      conflictRate: 0,
      clientStats: {},
    };
  }

  /**
   * 初始化数据同步服务
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing data sync service...');

      // 加载待处理的同步任务
      await this.loadPendingTasks();

      // 加载未解决的冲突
      await this.loadUnresolvedConflicts();

      // 启动处理循环
      this.startProcessingLoop();

      // 设置定期清理
      this.setupPeriodicCleanup();

      this.logger.info('Data sync service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize data sync service:', error);
      throw error;
    }
  }

  /**
   * 创建同步任务
   */
  async createSyncTask(
    userId: string,
    clientId: string,
    changes: DataChangeRecord[],
    priority: SyncTask['priority'] = 'medium'
  ): Promise<string> {
    const task: SyncTask = {
      id: this.generateTaskId(),
      userId,
      clientId,
      changes,
      status: SyncStatus.PENDING,
      priority,
      createdAt: new Date(),
      updatedAt: new Date(),
      retryCount: 0,
      maxRetries: 3,
    };

    // 验证变更记录
    await this.validateChanges(changes);

    // 保存任务
    this.syncTasks.set(task.id, task);
    await this.persistTask(task);

    // 更新统计
    this.stats.totalTasks++;
    this.updateClientStats(clientId);

    // 发出事件
    this.emit('taskCreated', task);

    this.logger.debug('Sync task created', {
      taskId: task.id,
      userId,
      clientId,
      changesCount: changes.length,
      priority,
    });

    return task.id;
  }

  /**
   * 处理同步任务
   */
  async processSyncTask(taskId: string): Promise<SyncResult> {
    const task = this.syncTasks.get(taskId);
    if (!task) {
      throw new Error(`Sync task not found: ${taskId}`);
    }

    const startTime = Date.now();

    try {
      this.logger.debug('Processing sync task', {
        taskId,
        changesCount: task.changes.length,
      });

      // 更新任务状态
      task.status = SyncStatus.IN_PROGRESS;
      task.updatedAt = new Date();
      await this.persistTask(task);

      // 处理变更
      const result = await this.processChanges(task);

      // 更新任务状态
      if (result.conflicts.length > 0) {
        task.status = SyncStatus.CONFLICT;
        this.stats.conflictTasks++;
      } else if (result.failedChanges > 0) {
        task.status = SyncStatus.FAILED;
        this.stats.failedTasks++;
      } else {
        task.status = SyncStatus.COMPLETED;
        this.stats.completedTasks++;
      }

      task.completedAt = new Date();
      task.updatedAt = new Date();
      await this.persistTask(task);

      // 更新统计
      const duration = Date.now() - startTime;
      this.stats.averageProcessingTime = 
        (this.stats.averageProcessingTime * (this.stats.completedTasks - 1) + duration) / 
        this.stats.completedTasks;
      this.stats.totalChangesProcessed += result.processedChanges;
      this.stats.conflictRate = this.stats.conflictTasks / this.stats.totalTasks;

      // 发出事件
      this.emit('taskCompleted', task, result);

      this.logger.info('Sync task processed', {
        taskId,
        status: task.status,
        processedChanges: result.processedChanges,
        conflicts: result.conflicts.length,
        duration,
      });

      return result;
    } catch (error) {
      // 处理失败
      task.status = SyncStatus.FAILED;
      task.error = error.message;
      task.retryCount++;
      task.updatedAt = new Date();
      await this.persistTask(task);

      this.stats.failedTasks++;

      this.emit('taskFailed', task, error);

      this.logger.error('Sync task processing failed:', error, { taskId });
      throw error;
    }
  }

  /**
   * 获取客户端增量变更
   */
  async getIncrementalChanges(
    userId: string,
    clientId: string,
    lastSyncTimestamp: number,
    entityTypes?: string[]
  ): Promise<DataChangeRecord[]> {
    try {
      let query = `
        SELECT * FROM data_changes 
        WHERE user_id = $1 
        AND timestamp > $2 
        AND metadata->>'source' = 'server'
        ORDER BY timestamp ASC
      `;
      const params: any[] = [userId, lastSyncTimestamp];

      if (entityTypes && entityTypes.length > 0) {
        query += ` AND entity_type = ANY($${params.length + 1})`;
        params.push(entityTypes);
      }

      const result = await databaseService.query(query, params);

      const changes: DataChangeRecord[] = result.rows.map(row => ({
        id: row.id,
        entityType: row.entity_type,
        entityId: row.entity_id,
        operation: row.operation,
        data: row.data,
        previousData: row.previous_data,
        userId: row.user_id,
        sessionId: row.session_id,
        timestamp: row.timestamp,
        version: row.version,
        checksum: row.checksum,
        metadata: row.metadata,
      }));

      this.logger.debug('Retrieved incremental changes', {
        userId,
        clientId,
        lastSyncTimestamp,
        changesCount: changes.length,
      });

      return changes;
    } catch (error) {
      this.logger.error('Failed to get incremental changes:', error);
      throw error;
    }
  }

  /**
   * 检测数据冲突
   */
  async detectConflicts(changes: DataChangeRecord[]): Promise<DataConflict[]> {
    const conflicts: DataConflict[] = [];

    for (const change of changes) {
      try {
        // 获取服务器端当前版本
        const serverData = await this.getServerData(change.entityType, change.entityId);
        
        if (!serverData) {
          // 服务器端数据不存在
          if (change.operation === SyncOperation.UPDATE || change.operation === SyncOperation.DELETE) {
            conflicts.push({
              id: this.generateConflictId(),
              entityType: change.entityType,
              entityId: change.entityId,
              clientVersion: change.version,
              serverVersion: 0,
              clientData: change.data,
              serverData: null,
              conflictType: 'deleted',
              timestamp: Date.now(),
              resolved: false,
            });
          }
          continue;
        }

        // 版本冲突检测
        if (serverData.version > change.version) {
          conflicts.push({
            id: this.generateConflictId(),
            entityType: change.entityType,
            entityId: change.entityId,
            clientVersion: change.version,
            serverVersion: serverData.version,
            clientData: change.data,
            serverData: serverData.data,
            conflictType: 'version',
            timestamp: Date.now(),
            resolved: false,
          });
        }

        // 并发修改检测
        const recentChanges = await this.getRecentChanges(
          change.entityType,
          change.entityId,
          change.timestamp - 60000 // 1分钟内的变更
        );

        if (recentChanges.length > 0) {
          conflicts.push({
            id: this.generateConflictId(),
            entityType: change.entityType,
            entityId: change.entityId,
            clientVersion: change.version,
            serverVersion: serverData.version,
            clientData: change.data,
            serverData: serverData.data,
            conflictType: 'concurrent',
            timestamp: Date.now(),
            resolved: false,
          });
        }
      } catch (error) {
        this.logger.error('Failed to detect conflict for change:', error, {
          changeId: change.id,
        });
      }
    }

    return conflicts;
  }

  /**
   * 解决数据冲突
   */
  async resolveConflict(
    conflictId: string,
    resolution: DataConflict['resolution'],
    mergedData?: any
  ): Promise<void> {
    const conflict = this.conflicts.get(conflictId);
    if (!conflict) {
      throw new Error(`Conflict not found: ${conflictId}`);
    }

    try {
      let finalData: any;

      switch (resolution) {
        case 'client':
          finalData = conflict.clientData;
          break;
        case 'server':
          finalData = conflict.serverData;
          break;
        case 'merge':
          finalData = mergedData || this.mergeData(conflict.clientData, conflict.serverData);
          break;
        case 'manual':
          finalData = mergedData;
          break;
        default:
          throw new Error(`Invalid resolution type: ${resolution}`);
      }

      // 应用解决方案
      await this.applyResolution(conflict, finalData);

      // 标记冲突已解决
      conflict.resolved = true;
      conflict.resolution = resolution;

      // 保存冲突解决记录
      await this.persistConflict(conflict);

      // 发出事件
      this.emit('conflictResolved', conflict, resolution);

      this.logger.info('Conflict resolved', {
        conflictId,
        resolution,
        entityType: conflict.entityType,
        entityId: conflict.entityId,
      });
    } catch (error) {
      this.logger.error('Failed to resolve conflict:', error, { conflictId });
      throw error;
    }
  }

  // 私有方法

  private async loadPendingTasks(): Promise<void> {
    try {
      const result = await databaseService.query(`
        SELECT * FROM sync_tasks 
        WHERE status IN ('pending', 'in_progress') 
        ORDER BY created_at ASC
      `);

      for (const row of result.rows) {
        const task: SyncTask = {
          id: row.id,
          userId: row.user_id,
          clientId: row.client_id,
          changes: row.changes,
          status: row.status,
          priority: row.priority,
          createdAt: row.created_at,
          updatedAt: row.updated_at,
          completedAt: row.completed_at,
          error: row.error,
          retryCount: row.retry_count,
          maxRetries: row.max_retries,
        };

        this.syncTasks.set(task.id, task);
      }

      this.logger.info('Loaded pending sync tasks', {
        count: this.syncTasks.size,
      });
    } catch (error) {
      this.logger.error('Failed to load pending tasks:', error);
    }
  }

  private async loadUnresolvedConflicts(): Promise<void> {
    try {
      const result = await databaseService.query(`
        SELECT * FROM data_conflicts 
        WHERE resolved = false 
        ORDER BY timestamp ASC
      `);

      for (const row of result.rows) {
        const conflict: DataConflict = {
          id: row.id,
          entityType: row.entity_type,
          entityId: row.entity_id,
          clientVersion: row.client_version,
          serverVersion: row.server_version,
          clientData: row.client_data,
          serverData: row.server_data,
          conflictType: row.conflict_type,
          timestamp: row.timestamp,
          resolved: row.resolved,
          resolution: row.resolution,
        };

        this.conflicts.set(conflict.id, conflict);
      }

      this.logger.info('Loaded unresolved conflicts', {
        count: this.conflicts.size,
      });
    } catch (error) {
      this.logger.error('Failed to load unresolved conflicts:', error);
    }
  }

  private startProcessingLoop(): void {
    this.processingInterval = setInterval(async () => {
      if (!this.isProcessing && this.syncTasks.size > 0) {
        await this.processNextTask();
      }
    }, 1000); // 每秒检查一次

    this.logger.info('Sync processing loop started');
  }

  private async processNextTask(): Promise<void> {
    this.isProcessing = true;

    try {
      // 找到优先级最高的待处理任务
      const pendingTasks = Array.from(this.syncTasks.values())
        .filter(task => task.status === SyncStatus.PENDING)
        .sort((a, b) => {
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
          if (priorityDiff !== 0) return priorityDiff;
          return a.createdAt.getTime() - b.createdAt.getTime();
        });

      if (pendingTasks.length > 0) {
        const task = pendingTasks[0];
        await this.processSyncTask(task.id);
      }
    } catch (error) {
      this.logger.error('Error in processing loop:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async processChanges(task: SyncTask): Promise<SyncResult> {
    const result: SyncResult = {
      taskId: task.id,
      status: SyncStatus.COMPLETED,
      processedChanges: 0,
      failedChanges: 0,
      conflicts: [],
      appliedChanges: [],
      duration: 0,
    };

    const startTime = Date.now();

    // 检测冲突
    const conflicts = await this.detectConflicts(task.changes);
    result.conflicts = conflicts;

    // 保存冲突
    for (const conflict of conflicts) {
      this.conflicts.set(conflict.id, conflict);
      await this.persistConflict(conflict);
    }

    // 处理无冲突的变更
    const conflictEntityIds = new Set(conflicts.map(c => c.entityId));
    const nonConflictChanges = task.changes.filter(
      change => !conflictEntityIds.has(change.entityId)
    );

    for (const change of nonConflictChanges) {
      try {
        await this.applyChange(change);
        result.appliedChanges.push(change);
        result.processedChanges++;
      } catch (error) {
        this.logger.error('Failed to apply change:', error, {
          changeId: change.id,
        });
        result.failedChanges++;
      }
    }

    result.duration = Date.now() - startTime;

    if (result.conflicts.length > 0) {
      result.status = SyncStatus.CONFLICT;
    } else if (result.failedChanges > 0) {
      result.status = SyncStatus.FAILED;
    }

    return result;
  }

  private async validateChanges(changes: DataChangeRecord[]): Promise<void> {
    for (const change of changes) {
      // 验证必需字段
      if (!change.id || !change.entityType || !change.entityId || !change.operation) {
        throw new Error('Invalid change record: missing required fields');
      }

      // 验证校验和
      const expectedChecksum = this.calculateChecksum(change.data);
      if (change.checksum !== expectedChecksum) {
        throw new Error('Invalid change record: checksum mismatch');
      }
    }
  }

  private async applyChange(change: DataChangeRecord): Promise<void> {
    // 根据实体类型和操作应用变更
    // 这里需要根据具体的业务逻辑实现
    this.logger.debug('Applying change', {
      changeId: change.id,
      entityType: change.entityType,
      operation: change.operation,
    });

    // 记录变更到数据库
    await this.recordChange(change);
  }

  private async recordChange(change: DataChangeRecord): Promise<void> {
    await databaseService.query(`
      INSERT INTO data_changes (
        id, entity_type, entity_id, operation, data, previous_data,
        user_id, session_id, timestamp, version, checksum, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      ON CONFLICT (id) DO UPDATE SET
        data = EXCLUDED.data,
        updated_at = NOW()
    `, [
      change.id,
      change.entityType,
      change.entityId,
      change.operation,
      JSON.stringify(change.data),
      JSON.stringify(change.previousData),
      change.userId,
      change.sessionId,
      change.timestamp,
      change.version,
      change.checksum,
      JSON.stringify(change.metadata),
    ]);
  }

  private async getServerData(entityType: string, entityId: string): Promise<any> {
    // 根据实体类型获取服务器端数据
    // 这里需要根据具体的业务逻辑实现
    return null;
  }

  private async getRecentChanges(
    entityType: string,
    entityId: string,
    since: number
  ): Promise<DataChangeRecord[]> {
    const result = await databaseService.query(`
      SELECT * FROM data_changes 
      WHERE entity_type = $1 AND entity_id = $2 AND timestamp > $3
      ORDER BY timestamp DESC
    `, [entityType, entityId, since]);

    return result.rows.map(row => ({
      id: row.id,
      entityType: row.entity_type,
      entityId: row.entity_id,
      operation: row.operation,
      data: row.data,
      previousData: row.previous_data,
      userId: row.user_id,
      sessionId: row.session_id,
      timestamp: row.timestamp,
      version: row.version,
      checksum: row.checksum,
      metadata: row.metadata,
    }));
  }

  private mergeData(clientData: any, serverData: any): any {
    // 简单的合并策略，实际应用中需要更复杂的逻辑
    return { ...serverData, ...clientData };
  }

  private async applyResolution(conflict: DataConflict, finalData: any): Promise<void> {
    // 应用冲突解决方案
    // 这里需要根据具体的业务逻辑实现
    this.logger.debug('Applying conflict resolution', {
      conflictId: conflict.id,
      entityType: conflict.entityType,
      entityId: conflict.entityId,
    });
  }

  private async persistTask(task: SyncTask): Promise<void> {
    await databaseService.query(`
      INSERT INTO sync_tasks (
        id, user_id, client_id, changes, status, priority,
        created_at, updated_at, completed_at, error, retry_count, max_retries
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      ON CONFLICT (id) DO UPDATE SET
        status = EXCLUDED.status,
        updated_at = EXCLUDED.updated_at,
        completed_at = EXCLUDED.completed_at,
        error = EXCLUDED.error,
        retry_count = EXCLUDED.retry_count
    `, [
      task.id,
      task.userId,
      task.clientId,
      JSON.stringify(task.changes),
      task.status,
      task.priority,
      task.createdAt,
      task.updatedAt,
      task.completedAt,
      task.error,
      task.retryCount,
      task.maxRetries,
    ]);
  }

  private async persistConflict(conflict: DataConflict): Promise<void> {
    await databaseService.query(`
      INSERT INTO data_conflicts (
        id, entity_type, entity_id, client_version, server_version,
        client_data, server_data, conflict_type, timestamp, resolved, resolution
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      ON CONFLICT (id) DO UPDATE SET
        resolved = EXCLUDED.resolved,
        resolution = EXCLUDED.resolution
    `, [
      conflict.id,
      conflict.entityType,
      conflict.entityId,
      conflict.clientVersion,
      conflict.serverVersion,
      JSON.stringify(conflict.clientData),
      JSON.stringify(conflict.serverData),
      conflict.conflictType,
      conflict.timestamp,
      conflict.resolved,
      conflict.resolution,
    ]);
  }

  private calculateChecksum(data: any): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(JSON.stringify(data)).digest('hex');
  }

  private updateClientStats(clientId: string): void {
    if (!this.stats.clientStats[clientId]) {
      this.stats.clientStats[clientId] = {
        totalTasks: 0,
        lastSyncTime: 0,
        averageLatency: 0,
      };
    }
    this.stats.clientStats[clientId].totalTasks++;
    this.stats.clientStats[clientId].lastSyncTime = Date.now();
  }

  private setupPeriodicCleanup(): void {
    // 每小时清理已完成的旧任务
    setInterval(async () => {
      await this.cleanupOldTasks();
    }, 60 * 60 * 1000);
  }

  private async cleanupOldTasks(): Promise<void> {
    const cutoffTime = Date.now() - 24 * 60 * 60 * 1000; // 24小时前

    try {
      // 清理内存中的任务
      for (const [taskId, task] of this.syncTasks) {
        if (task.status === SyncStatus.COMPLETED && 
            task.completedAt && 
            task.completedAt.getTime() < cutoffTime) {
          this.syncTasks.delete(taskId);
        }
      }

      // 清理数据库中的任务
      await databaseService.query(`
        DELETE FROM sync_tasks 
        WHERE status = 'completed' AND completed_at < $1
      `, [new Date(cutoffTime)]);

      this.logger.debug('Cleaned up old sync tasks');
    } catch (error) {
      this.logger.error('Failed to cleanup old tasks:', error);
    }
  }

  private generateTaskId(): string {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateConflictId(): string {
    return `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取同步任务状态
   */
  getSyncTaskStatus(taskId: string): SyncTask | undefined {
    return this.syncTasks.get(taskId);
  }

  /**
   * 获取未解决的冲突
   */
  getUnresolvedConflicts(userId?: string): DataConflict[] {
    const conflicts = Array.from(this.conflicts.values()).filter(c => !c.resolved);
    return userId ? conflicts.filter(c => c.entityId.includes(userId)) : conflicts;
  }

  /**
   * 获取同步统计信息
   */
  getStats(): SyncStats {
    return { ...this.stats };
  }

  /**
   * 关闭服务
   */
  async close(): Promise<void> {
    this.logger.info('Closing data sync service...');
    
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
    
    this.syncTasks.clear();
    this.conflicts.clear();
    
    this.logger.info('Data sync service closed');
  }
}

// 导出单例实例
export const dataSyncService = new DataSyncService();
