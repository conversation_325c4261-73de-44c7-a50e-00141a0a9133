// ERP智能助手后端服务 - 健康检查服务

import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { databaseService } from '@/services/database';
import { redisService } from '@/services/redis';
import { vectorService } from '@/services/vector';

// 健康状态枚举
export enum HealthStatus {
  HEALTHY = 'healthy',
  UNHEALTHY = 'unhealthy',
  DEGRADED = 'degraded',
}

// 服务健康信息接口
export interface ServiceHealth {
  status: HealthStatus;
  responseTime: number;
  details?: any;
  error?: string;
}

// 系统健康信息接口
export interface SystemHealth {
  status: HealthStatus;
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: ServiceHealth;
    redis: ServiceHealth;
    vector: ServiceHealth;
    memory: ServiceHealth;
    disk: ServiceHealth;
  };
  summary: {
    healthy: number;
    unhealthy: number;
    degraded: number;
    total: number;
  };
}

export class HealthService {
  private logger: Logger;
  private startTime: number;
  private healthChecks = new Map<string, () => Promise<ServiceHealth>>();

  constructor() {
    this.logger = new Logger('HealthService');
    this.startTime = Date.now();
  }

  /**
   * 初始化健康检查服务
   */
  async initialize(): Promise<void> {
    this.logger.info('Initializing health check service...');

    // 注册默认健康检查
    this.registerHealthCheck('database', this.checkDatabase.bind(this));
    this.registerHealthCheck('redis', this.checkRedis.bind(this));
    this.registerHealthCheck('vector', this.checkVector.bind(this));
    this.registerHealthCheck('memory', this.checkMemory.bind(this));
    this.registerHealthCheck('disk', this.checkDisk.bind(this));

    // 启动定期健康检查
    if (config.monitoring.healthCheckInterval > 0) {
      this.startPeriodicHealthCheck();
    }

    this.logger.info('Health check service initialized successfully');
  }

  /**
   * 注册健康检查
   */
  registerHealthCheck(name: string, checkFunction: () => Promise<ServiceHealth>): void {
    this.healthChecks.set(name, checkFunction);
    this.logger.debug('Health check registered', { name });
  }

  /**
   * 移除健康检查
   */
  unregisterHealthCheck(name: string): void {
    this.healthChecks.delete(name);
    this.logger.debug('Health check unregistered', { name });
  }

  /**
   * 执行完整健康检查
   */
  async checkHealth(): Promise<SystemHealth> {
    const startTime = Date.now();
    const services: any = {};
    const summary = { healthy: 0, unhealthy: 0, degraded: 0, total: 0 };

    // 并行执行所有健康检查
    const healthCheckPromises = Array.from(this.healthChecks.entries()).map(
      async ([name, checkFunction]) => {
        try {
          const result = await Promise.race([
            checkFunction(),
            this.timeoutPromise(5000), // 5秒超时
          ]);
          
          services[name] = result;
          
          // 更新统计
          switch (result.status) {
            case HealthStatus.HEALTHY:
              summary.healthy++;
              break;
            case HealthStatus.UNHEALTHY:
              summary.unhealthy++;
              break;
            case HealthStatus.DEGRADED:
              summary.degraded++;
              break;
          }
          summary.total++;
          
        } catch (error) {
          services[name] = {
            status: HealthStatus.UNHEALTHY,
            responseTime: Date.now() - startTime,
            error: error.message,
          };
          summary.unhealthy++;
          summary.total++;
        }
      }
    );

    await Promise.all(healthCheckPromises);

    // 确定整体健康状态
    let overallStatus = HealthStatus.HEALTHY;
    if (summary.unhealthy > 0) {
      overallStatus = summary.unhealthy >= summary.total / 2 
        ? HealthStatus.UNHEALTHY 
        : HealthStatus.DEGRADED;
    } else if (summary.degraded > 0) {
      overallStatus = HealthStatus.DEGRADED;
    }

    const systemHealth: SystemHealth = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      version: process.env.npm_package_version || '1.0.0',
      environment: config.env,
      services,
      summary,
    };

    this.logger.debug('Health check completed', {
      status: overallStatus,
      responseTime: Date.now() - startTime,
      summary,
    });

    return systemHealth;
  }

  /**
   * 检查数据库健康状态
   */
  private async checkDatabase(): Promise<ServiceHealth> {
    try {
      const result = await databaseService.healthCheck();
      return {
        status: result.status === 'healthy' ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
        responseTime: result.details.responseTime,
        details: result.details,
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        responseTime: 0,
        error: error.message,
      };
    }
  }

  /**
   * 检查Redis健康状态
   */
  private async checkRedis(): Promise<ServiceHealth> {
    try {
      const result = await redisService.healthCheck();
      return {
        status: result.status === 'healthy' ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
        responseTime: result.details.responseTime,
        details: result.details,
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        responseTime: 0,
        error: error.message,
      };
    }
  }

  /**
   * 检查向量数据库健康状态
   */
  private async checkVector(): Promise<ServiceHealth> {
    try {
      const result = await vectorService.healthCheck();
      return {
        status: result.status === 'healthy' ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
        responseTime: result.details.responseTime,
        details: result.details,
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        responseTime: 0,
        error: error.message,
      };
    }
  }

  /**
   * 检查内存使用情况
   */
  private async checkMemory(): Promise<ServiceHealth> {
    try {
      const memUsage = process.memoryUsage();
      const totalMemory = memUsage.heapTotal;
      const usedMemory = memUsage.heapUsed;
      const memoryUsagePercent = (usedMemory / totalMemory) * 100;

      let status = HealthStatus.HEALTHY;
      if (memoryUsagePercent > 90) {
        status = HealthStatus.UNHEALTHY;
      } else if (memoryUsagePercent > 80) {
        status = HealthStatus.DEGRADED;
      }

      return {
        status,
        responseTime: 0,
        details: {
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
          external: Math.round(memUsage.external / 1024 / 1024), // MB
          rss: Math.round(memUsage.rss / 1024 / 1024), // MB
          usagePercent: Math.round(memoryUsagePercent),
        },
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        responseTime: 0,
        error: error.message,
      };
    }
  }

  /**
   * 检查磁盘使用情况
   */
  private async checkDisk(): Promise<ServiceHealth> {
    try {
      const fs = require('fs');
      const stats = fs.statSync('.');
      
      // 简单的磁盘检查（实际应用中可能需要更复杂的逻辑）
      return {
        status: HealthStatus.HEALTHY,
        responseTime: 0,
        details: {
          available: true,
          writable: true,
        },
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        responseTime: 0,
        error: error.message,
      };
    }
  }

  /**
   * 启动定期健康检查
   */
  private startPeriodicHealthCheck(): void {
    setInterval(async () => {
      try {
        const health = await this.checkHealth();
        
        if (health.status !== HealthStatus.HEALTHY) {
          this.logger.warn('System health degraded', {
            status: health.status,
            unhealthyServices: Object.entries(health.services)
              .filter(([, service]) => service.status !== HealthStatus.HEALTHY)
              .map(([name]) => name),
          });
        }
      } catch (error) {
        this.logger.error('Periodic health check failed:', error);
      }
    }, config.monitoring.healthCheckInterval);

    this.logger.info('Periodic health check started', {
      interval: config.monitoring.healthCheckInterval,
    });
  }

  /**
   * 超时Promise
   */
  private timeoutPromise(ms: number): Promise<ServiceHealth> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Health check timeout after ${ms}ms`));
      }, ms);
    });
  }

  /**
   * 获取简单健康状态
   */
  async getSimpleHealth(): Promise<{
    status: HealthStatus;
    timestamp: string;
    uptime: number;
  }> {
    return {
      status: HealthStatus.HEALTHY, // 简化版本，总是返回健康
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
    };
  }

  /**
   * 获取服务统计信息
   */
  getStats(): {
    uptime: number;
    startTime: number;
    registeredChecks: string[];
  } {
    return {
      uptime: Date.now() - this.startTime,
      startTime: this.startTime,
      registeredChecks: Array.from(this.healthChecks.keys()),
    };
  }
}

// 导出单例实例
export const healthService = new HealthService();
