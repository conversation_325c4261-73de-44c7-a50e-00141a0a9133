// ERP智能助手后端服务 - WebSocket实时通信服务

import { Server as HttpServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { createClient } from 'redis';
import jwt from 'jsonwebtoken';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { dataSyncService, DataChangeRecord, SyncResult } from '@/services/data-sync';
import { redisService } from '@/services/redis';

// WebSocket消息类型
export enum MessageType {
  // 认证相关
  AUTH = 'auth',
  AUTH_SUCCESS = 'auth_success',
  AUTH_FAILED = 'auth_failed',
  
  // 数据同步相关
  SYNC_REQUEST = 'sync_request',
  SYNC_RESPONSE = 'sync_response',
  SYNC_PROGRESS = 'sync_progress',
  DATA_CHANGE = 'data_change',
  
  // 实时通知
  NOTIFICATION = 'notification',
  SYSTEM_MESSAGE = 'system_message',
  
  // 录制相关
  RECORDING_START = 'recording_start',
  RECORDING_STOP = 'recording_stop',
  RECORDING_UPDATE = 'recording_update',
  
  // AI分析相关
  AI_ANALYSIS_START = 'ai_analysis_start',
  AI_ANALYSIS_PROGRESS = 'ai_analysis_progress',
  AI_ANALYSIS_COMPLETE = 'ai_analysis_complete',
  
  // 连接管理
  PING = 'ping',
  PONG = 'pong',
  DISCONNECT = 'disconnect',
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: MessageType;
  data: any;
  timestamp: number;
  messageId?: string;
  userId?: string;
  sessionId?: string;
}

// 客户端连接信息
export interface ClientConnection {
  id: string;
  socket: Socket;
  userId: string;
  clientId: string;
  userAgent: string;
  ipAddress: string;
  connectedAt: Date;
  lastActivity: Date;
  subscriptions: Set<string>;
  metadata: {
    version?: string;
    platform?: string;
    [key: string]: any;
  };
}

// 房间管理接口
export interface Room {
  id: string;
  name: string;
  type: 'user' | 'session' | 'global';
  members: Set<string>;
  createdAt: Date;
  metadata: Record<string, any>;
}

// WebSocket统计接口
export interface WebSocketStats {
  totalConnections: number;
  activeConnections: number;
  totalMessages: number;
  messagesByType: Record<MessageType, number>;
  averageLatency: number;
  connectionsByUser: Record<string, number>;
  roomStats: {
    totalRooms: number;
    averageMembersPerRoom: number;
  };
}

export class WebSocketService {
  private logger: Logger;
  private io: SocketIOServer | null = null;
  private connections = new Map<string, ClientConnection>();
  private rooms = new Map<string, Room>();
  private stats: WebSocketStats;
  private heartbeatInterval?: NodeJS.Timeout;

  constructor() {
    this.logger = new Logger('WebSocketService');
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      totalMessages: 0,
      messagesByType: {} as Record<MessageType, number>,
      averageLatency: 0,
      connectionsByUser: {},
      roomStats: {
        totalRooms: 0,
        averageMembersPerRoom: 0,
      },
    };

    // 初始化消息类型统计
    Object.values(MessageType).forEach(type => {
      this.stats.messagesByType[type as MessageType] = 0;
    });
  }

  /**
   * 初始化WebSocket服务
   */
  async initialize(httpServer: HttpServer): Promise<void> {
    try {
      this.logger.info('Initializing WebSocket service...');

      // 创建Socket.IO服务器
      this.io = new SocketIOServer(httpServer, {
        cors: {
          origin: config.cors.origin,
          methods: ['GET', 'POST'],
          credentials: true,
        },
        transports: ['websocket', 'polling'],
        pingTimeout: 60000,
        pingInterval: 25000,
      });

      // 设置Redis适配器（用于多实例支持）
      if (config.redis.enabled) {
        const pubClient = createClient({
          host: config.redis.host,
          port: config.redis.port,
          password: config.redis.password,
        });
        const subClient = pubClient.duplicate();

        await pubClient.connect();
        await subClient.connect();

        this.io.adapter(createAdapter(pubClient, subClient));
        this.logger.info('Redis adapter configured for WebSocket');
      }

      // 设置中间件
      this.setupMiddleware();

      // 设置事件处理
      this.setupEventHandlers();

      // 启动心跳检测
      this.startHeartbeat();

      // 监听数据同步事件
      this.setupSyncEventListeners();

      this.logger.info('WebSocket service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize WebSocket service:', error);
      throw error;
    }
  }

  /**
   * 设置中间件
   */
  private setupMiddleware(): void {
    if (!this.io) return;

    // 认证中间件
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, config.jwt.secret) as any;
        socket.data.userId = decoded.userId;
        socket.data.clientId = socket.handshake.query.clientId || socket.id;
        
        next();
      } catch (error) {
        next(new Error('Invalid authentication token'));
      }
    });

    // 速率限制中间件
    this.io.use(async (socket, next) => {
      const userId = socket.data.userId;
      const rateLimitKey = `websocket_rate_limit:${userId}`;
      
      try {
        const current = await redisService.incr(rateLimitKey);
        if (current === 1) {
          await redisService.expire(rateLimitKey, 60); // 1分钟窗口
        }
        
        if (current > 1000) { // 每分钟最多1000个连接
          return next(new Error('Rate limit exceeded'));
        }
        
        next();
      } catch (error) {
        next();
      }
    });
  }

  /**
   * 设置事件处理
   */
  private setupEventHandlers(): void {
    if (!this.io) return;

    this.io.on('connection', (socket: Socket) => {
      this.handleConnection(socket);
    });
  }

  /**
   * 处理新连接
   */
  private handleConnection(socket: Socket): void {
    const userId = socket.data.userId;
    const clientId = socket.data.clientId;
    const userAgent = socket.handshake.headers['user-agent'] || '';
    const ipAddress = socket.handshake.address;

    // 创建连接记录
    const connection: ClientConnection = {
      id: socket.id,
      socket,
      userId,
      clientId,
      userAgent,
      ipAddress,
      connectedAt: new Date(),
      lastActivity: new Date(),
      subscriptions: new Set(),
      metadata: {
        version: socket.handshake.query.version as string,
        platform: socket.handshake.query.platform as string,
      },
    };

    this.connections.set(socket.id, connection);

    // 更新统计
    this.stats.totalConnections++;
    this.stats.activeConnections++;
    this.stats.connectionsByUser[userId] = (this.stats.connectionsByUser[userId] || 0) + 1;

    // 加入用户房间
    socket.join(`user:${userId}`);
    this.joinRoom(socket.id, `user:${userId}`, 'user');

    this.logger.info('Client connected', {
      socketId: socket.id,
      userId,
      clientId,
      ipAddress,
    });

    // 发送认证成功消息
    this.sendMessage(socket.id, {
      type: MessageType.AUTH_SUCCESS,
      data: {
        socketId: socket.id,
        userId,
        serverTime: Date.now(),
      },
      timestamp: Date.now(),
    });

    // 设置事件监听器
    this.setupSocketEventListeners(socket);
  }

  /**
   * 设置Socket事件监听器
   */
  private setupSocketEventListeners(socket: Socket): void {
    const connection = this.connections.get(socket.id);
    if (!connection) return;

    // 通用消息处理
    socket.on('message', (message: WebSocketMessage) => {
      this.handleMessage(socket.id, message);
    });

    // 数据同步请求
    socket.on(MessageType.SYNC_REQUEST, async (data) => {
      await this.handleSyncRequest(socket.id, data);
    });

    // 订阅房间
    socket.on('subscribe', (roomId: string) => {
      this.subscribeToRoom(socket.id, roomId);
    });

    // 取消订阅房间
    socket.on('unsubscribe', (roomId: string) => {
      this.unsubscribeFromRoom(socket.id, roomId);
    });

    // 心跳响应
    socket.on(MessageType.PONG, () => {
      connection.lastActivity = new Date();
    });

    // 断开连接
    socket.on('disconnect', (reason) => {
      this.handleDisconnection(socket.id, reason);
    });

    // 错误处理
    socket.on('error', (error) => {
      this.logger.error('Socket error:', error, {
        socketId: socket.id,
        userId: connection.userId,
      });
    });
  }

  /**
   * 处理消息
   */
  private handleMessage(socketId: string, message: WebSocketMessage): void {
    const connection = this.connections.get(socketId);
    if (!connection) return;

    // 更新活动时间
    connection.lastActivity = new Date();

    // 更新统计
    this.stats.totalMessages++;
    this.stats.messagesByType[message.type]++;

    this.logger.debug('Received message', {
      socketId,
      type: message.type,
      userId: connection.userId,
    });

    // 根据消息类型处理
    switch (message.type) {
      case MessageType.PING:
        this.sendMessage(socketId, {
          type: MessageType.PONG,
          data: { timestamp: Date.now() },
          timestamp: Date.now(),
        });
        break;

      case MessageType.DATA_CHANGE:
        this.handleDataChange(socketId, message.data);
        break;

      case MessageType.RECORDING_START:
      case MessageType.RECORDING_STOP:
      case MessageType.RECORDING_UPDATE:
        this.broadcastToRoom(`user:${connection.userId}`, message);
        break;

      default:
        this.logger.debug('Unhandled message type', { type: message.type });
    }
  }

  /**
   * 处理数据同步请求
   */
  private async handleSyncRequest(socketId: string, data: any): Promise<void> {
    const connection = this.connections.get(socketId);
    if (!connection) return;

    try {
      const { changes, lastSyncTimestamp } = data;

      // 创建同步任务
      const taskId = await dataSyncService.createSyncTask(
        connection.userId,
        connection.clientId,
        changes,
        'high'
      );

      // 发送同步响应
      this.sendMessage(socketId, {
        type: MessageType.SYNC_RESPONSE,
        data: {
          taskId,
          status: 'accepted',
          estimatedTime: changes.length * 100, // 估算处理时间
        },
        timestamp: Date.now(),
      });

      // 获取增量变更
      const incrementalChanges = await dataSyncService.getIncrementalChanges(
        connection.userId,
        connection.clientId,
        lastSyncTimestamp
      );

      if (incrementalChanges.length > 0) {
        this.sendMessage(socketId, {
          type: MessageType.DATA_CHANGE,
          data: {
            changes: incrementalChanges,
            timestamp: Date.now(),
          },
          timestamp: Date.now(),
        });
      }
    } catch (error) {
      this.logger.error('Failed to handle sync request:', error);
      
      this.sendMessage(socketId, {
        type: MessageType.SYNC_RESPONSE,
        data: {
          status: 'error',
          error: error.message,
        },
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 处理数据变更
   */
  private handleDataChange(socketId: string, data: any): void {
    const connection = this.connections.get(socketId);
    if (!connection) return;

    // 广播数据变更到用户的其他连接
    this.broadcastToRoom(`user:${connection.userId}`, {
      type: MessageType.DATA_CHANGE,
      data,
      timestamp: Date.now(),
    }, [socketId]);
  }

  /**
   * 处理断开连接
   */
  private handleDisconnection(socketId: string, reason: string): void {
    const connection = this.connections.get(socketId);
    if (!connection) return;

    this.logger.info('Client disconnected', {
      socketId,
      userId: connection.userId,
      reason,
      duration: Date.now() - connection.connectedAt.getTime(),
    });

    // 清理连接
    this.connections.delete(socketId);

    // 更新统计
    this.stats.activeConnections--;
    this.stats.connectionsByUser[connection.userId]--;
    if (this.stats.connectionsByUser[connection.userId] <= 0) {
      delete this.stats.connectionsByUser[connection.userId];
    }

    // 清理房间订阅
    for (const roomId of connection.subscriptions) {
      this.leaveRoom(socketId, roomId);
    }
  }

  /**
   * 设置数据同步事件监听器
   */
  private setupSyncEventListeners(): void {
    // 监听同步任务完成
    dataSyncService.on('taskCompleted', (task, result: SyncResult) => {
      this.broadcastToRoom(`user:${task.userId}`, {
        type: MessageType.SYNC_RESPONSE,
        data: {
          taskId: task.id,
          status: result.status,
          processedChanges: result.processedChanges,
          conflicts: result.conflicts,
          duration: result.duration,
        },
        timestamp: Date.now(),
      });
    });

    // 监听同步进度
    dataSyncService.on('taskProgress', (task, progress) => {
      this.broadcastToRoom(`user:${task.userId}`, {
        type: MessageType.SYNC_PROGRESS,
        data: {
          taskId: task.id,
          progress,
        },
        timestamp: Date.now(),
      });
    });

    // 监听冲突解决
    dataSyncService.on('conflictResolved', (conflict, resolution) => {
      // 通知相关用户
      this.broadcastToRoom(`user:${conflict.entityId}`, {
        type: MessageType.DATA_CHANGE,
        data: {
          type: 'conflict_resolved',
          conflictId: conflict.id,
          resolution,
        },
        timestamp: Date.now(),
      });
    });
  }

  /**
   * 发送消息到指定连接
   */
  sendMessage(socketId: string, message: WebSocketMessage): void {
    const connection = this.connections.get(socketId);
    if (!connection) return;

    connection.socket.emit('message', message);
  }

  /**
   * 广播消息到房间
   */
  broadcastToRoom(roomId: string, message: WebSocketMessage, excludeSocketIds: string[] = []): void {
    if (!this.io) return;

    const room = this.io.sockets.adapter.rooms.get(roomId);
    if (!room) return;

    for (const socketId of room) {
      if (!excludeSocketIds.includes(socketId)) {
        this.sendMessage(socketId, message);
      }
    }
  }

  /**
   * 广播消息到所有连接
   */
  broadcast(message: WebSocketMessage, excludeSocketIds: string[] = []): void {
    for (const [socketId, connection] of this.connections) {
      if (!excludeSocketIds.includes(socketId)) {
        this.sendMessage(socketId, message);
      }
    }
  }

  /**
   * 订阅房间
   */
  subscribeToRoom(socketId: string, roomId: string): void {
    const connection = this.connections.get(socketId);
    if (!connection) return;

    connection.socket.join(roomId);
    connection.subscriptions.add(roomId);

    this.logger.debug('Client subscribed to room', {
      socketId,
      roomId,
      userId: connection.userId,
    });
  }

  /**
   * 取消订阅房间
   */
  unsubscribeFromRoom(socketId: string, roomId: string): void {
    const connection = this.connections.get(socketId);
    if (!connection) return;

    connection.socket.leave(roomId);
    connection.subscriptions.delete(roomId);

    this.logger.debug('Client unsubscribed from room', {
      socketId,
      roomId,
      userId: connection.userId,
    });
  }

  /**
   * 加入房间
   */
  private joinRoom(socketId: string, roomId: string, type: Room['type']): void {
    let room = this.rooms.get(roomId);
    if (!room) {
      room = {
        id: roomId,
        name: roomId,
        type,
        members: new Set(),
        createdAt: new Date(),
        metadata: {},
      };
      this.rooms.set(roomId, room);
      this.stats.roomStats.totalRooms++;
    }

    room.members.add(socketId);
    this.updateRoomStats();
  }

  /**
   * 离开房间
   */
  private leaveRoom(socketId: string, roomId: string): void {
    const room = this.rooms.get(roomId);
    if (!room) return;

    room.members.delete(socketId);

    // 如果房间为空，删除房间
    if (room.members.size === 0) {
      this.rooms.delete(roomId);
      this.stats.roomStats.totalRooms--;
    }

    this.updateRoomStats();
  }

  /**
   * 更新房间统计
   */
  private updateRoomStats(): void {
    const totalMembers = Array.from(this.rooms.values())
      .reduce((sum, room) => sum + room.members.size, 0);
    
    this.stats.roomStats.averageMembersPerRoom = 
      this.stats.roomStats.totalRooms > 0 ? totalMembers / this.stats.roomStats.totalRooms : 0;
  }

  /**
   * 启动心跳检测
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = Date.now();
      const timeout = 60000; // 60秒超时

      for (const [socketId, connection] of this.connections) {
        const lastActivity = connection.lastActivity.getTime();
        
        if (now - lastActivity > timeout) {
          this.logger.warn('Connection timeout, disconnecting client', {
            socketId,
            userId: connection.userId,
            lastActivity: connection.lastActivity,
          });
          
          connection.socket.disconnect(true);
        } else {
          // 发送心跳
          this.sendMessage(socketId, {
            type: MessageType.PING,
            data: { timestamp: now },
            timestamp: now,
          });
        }
      }
    }, 30000); // 每30秒检查一次
  }

  /**
   * 获取连接统计
   */
  getStats(): WebSocketStats {
    return { ...this.stats };
  }

  /**
   * 获取活跃连接
   */
  getActiveConnections(): ClientConnection[] {
    return Array.from(this.connections.values());
  }

  /**
   * 获取用户连接
   */
  getUserConnections(userId: string): ClientConnection[] {
    return Array.from(this.connections.values()).filter(conn => conn.userId === userId);
  }

  /**
   * 关闭服务
   */
  async close(): Promise<void> {
    this.logger.info('Closing WebSocket service...');

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    if (this.io) {
      this.io.close();
    }

    this.connections.clear();
    this.rooms.clear();

    this.logger.info('WebSocket service closed');
  }
}

// 导出单例实例
export const webSocketService = new WebSocketService();
