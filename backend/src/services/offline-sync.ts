// ERP智能助手后端服务 - 离线数据处理服务

import { EventEmitter } from 'events';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { databaseService } from '@/services/database';
import { redisService } from '@/services/redis';
import { dataSyncService, DataChangeRecord, SyncOperation } from '@/services/data-sync';

// 离线操作接口
export interface OfflineOperation {
  id: string;
  userId: string;
  clientId: string;
  type: SyncOperation;
  entityType: string;
  entityId: string;
  data: any;
  previousData?: any;
  timestamp: number;
  version: number;
  dependencies: string[]; // 依赖的其他操作ID
  metadata: {
    offline: true;
    queuedAt: number;
    retryCount: number;
    priority: 'low' | 'medium' | 'high';
    [key: string]: any;
  };
}

// 离线队列接口
export interface OfflineQueue {
  userId: string;
  clientId: string;
  operations: OfflineOperation[];
  lastSyncTimestamp: number;
  totalSize: number;
  status: 'active' | 'syncing' | 'error';
  createdAt: Date;
  updatedAt: Date;
}

// 数据合并策略接口
export interface MergeStrategy {
  name: string;
  canHandle(entityType: string): boolean;
  merge(localData: any, serverData: any, baseData?: any): Promise<any>;
  detectConflicts(localData: any, serverData: any): string[];
}

// 离线状态接口
export interface OfflineStatus {
  isOnline: boolean;
  lastOnlineTime: number;
  queuedOperations: number;
  pendingSyncSize: number;
  estimatedSyncTime: number;
  networkLatency?: number;
}

// 简单合并策略
class SimpleMergeStrategy implements MergeStrategy {
  name = 'simple';

  canHandle(entityType: string): boolean {
    return true; // 默认策略，处理所有类型
  }

  async merge(localData: any, serverData: any, baseData?: any): Promise<any> {
    // 简单的最后写入获胜策略
    return { ...serverData, ...localData };
  }

  detectConflicts(localData: any, serverData: any): string[] {
    const conflicts: string[] = [];
    
    // 检查字段级冲突
    for (const key in localData) {
      if (serverData[key] !== undefined && 
          localData[key] !== serverData[key] && 
          typeof localData[key] !== 'object') {
        conflicts.push(`Field '${key}' has conflicting values`);
      }
    }
    
    return conflicts;
  }
}

// 时间戳合并策略
class TimestampMergeStrategy implements MergeStrategy {
  name = 'timestamp';

  canHandle(entityType: string): boolean {
    return ['operation_records', 'recording_sessions'].includes(entityType);
  }

  async merge(localData: any, serverData: any, baseData?: any): Promise<any> {
    // 基于时间戳的合并
    const localTimestamp = localData.updated_at || localData.timestamp || 0;
    const serverTimestamp = serverData.updated_at || serverData.timestamp || 0;
    
    return localTimestamp > serverTimestamp ? localData : serverData;
  }

  detectConflicts(localData: any, serverData: any): string[] {
    const localTimestamp = localData.updated_at || localData.timestamp || 0;
    const serverTimestamp = serverData.updated_at || serverData.timestamp || 0;
    
    // 如果时间戳相近（5秒内），认为是冲突
    if (Math.abs(localTimestamp - serverTimestamp) < 5000) {
      return ['Concurrent modification detected'];
    }
    
    return [];
  }
}

// 版本合并策略
class VersionMergeStrategy implements MergeStrategy {
  name = 'version';

  canHandle(entityType: string): boolean {
    return true;
  }

  async merge(localData: any, serverData: any, baseData?: any): Promise<any> {
    const localVersion = localData.version || 0;
    const serverVersion = serverData.version || 0;
    
    if (localVersion > serverVersion) {
      return { ...localData, version: Math.max(localVersion, serverVersion) + 1 };
    } else {
      return { ...serverData, version: Math.max(localVersion, serverVersion) + 1 };
    }
  }

  detectConflicts(localData: any, serverData: any): string[] {
    const localVersion = localData.version || 0;
    const serverVersion = serverData.version || 0;
    
    // 版本分叉检测
    if (localVersion > 0 && serverVersion > 0 && localVersion !== serverVersion) {
      return ['Version conflict detected'];
    }
    
    return [];
  }
}

export class OfflineSyncService extends EventEmitter {
  private logger: Logger;
  private queues = new Map<string, OfflineQueue>();
  private mergeStrategies: MergeStrategy[] = [];
  private processingInterval?: NodeJS.Timeout;
  private isProcessing = false;

  constructor() {
    super();
    this.logger = new Logger('OfflineSyncService');
    
    // 注册合并策略
    this.mergeStrategies = [
      new VersionMergeStrategy(),
      new TimestampMergeStrategy(),
      new SimpleMergeStrategy(), // 默认策略，放在最后
    ];
  }

  /**
   * 初始化离线同步服务
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing offline sync service...');

      // 加载离线队列
      await this.loadOfflineQueues();

      // 启动处理循环
      this.startProcessingLoop();

      this.logger.info('Offline sync service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize offline sync service:', error);
      throw error;
    }
  }

  /**
   * 添加离线操作
   */
  async addOfflineOperation(
    userId: string,
    clientId: string,
    operation: Omit<OfflineOperation, 'id' | 'metadata'>
  ): Promise<string> {
    const operationId = this.generateOperationId();
    
    const offlineOperation: OfflineOperation = {
      ...operation,
      id: operationId,
      metadata: {
        offline: true,
        queuedAt: Date.now(),
        retryCount: 0,
        priority: 'medium',
      },
    };

    // 获取或创建队列
    const queueKey = `${userId}:${clientId}`;
    let queue = this.queues.get(queueKey);
    
    if (!queue) {
      queue = {
        userId,
        clientId,
        operations: [],
        lastSyncTimestamp: 0,
        totalSize: 0,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      this.queues.set(queueKey, queue);
    }

    // 添加操作到队列
    queue.operations.push(offlineOperation);
    queue.totalSize += this.calculateOperationSize(offlineOperation);
    queue.updatedAt = new Date();

    // 持久化队列
    await this.persistQueue(queue);

    // 发出事件
    this.emit('operationQueued', offlineOperation);

    this.logger.debug('Offline operation added', {
      operationId,
      userId,
      clientId,
      type: operation.type,
      entityType: operation.entityType,
    });

    return operationId;
  }

  /**
   * 批量添加离线操作
   */
  async addBatchOfflineOperations(
    userId: string,
    clientId: string,
    operations: Array<Omit<OfflineOperation, 'id' | 'metadata'>>
  ): Promise<string[]> {
    const operationIds: string[] = [];

    for (const operation of operations) {
      const operationId = await this.addOfflineOperation(userId, clientId, operation);
      operationIds.push(operationId);
    }

    this.logger.info('Batch offline operations added', {
      userId,
      clientId,
      count: operations.length,
    });

    return operationIds;
  }

  /**
   * 同步离线队列
   */
  async syncOfflineQueue(userId: string, clientId: string): Promise<void> {
    const queueKey = `${userId}:${clientId}`;
    const queue = this.queues.get(queueKey);
    
    if (!queue || queue.operations.length === 0) {
      this.logger.debug('No offline operations to sync', { userId, clientId });
      return;
    }

    try {
      queue.status = 'syncing';
      await this.persistQueue(queue);

      this.logger.info('Starting offline queue sync', {
        userId,
        clientId,
        operationsCount: queue.operations.length,
      });

      // 按依赖关系排序操作
      const sortedOperations = this.sortOperationsByDependencies(queue.operations);

      // 处理每个操作
      const processedOperations: OfflineOperation[] = [];
      const failedOperations: OfflineOperation[] = [];

      for (const operation of sortedOperations) {
        try {
          await this.processOfflineOperation(operation);
          processedOperations.push(operation);
        } catch (error) {
          this.logger.error('Failed to process offline operation:', error, {
            operationId: operation.id,
          });
          
          operation.metadata.retryCount++;
          if (operation.metadata.retryCount < 3) {
            failedOperations.push(operation);
          }
        }
      }

      // 更新队列
      queue.operations = failedOperations;
      queue.totalSize = failedOperations.reduce(
        (sum, op) => sum + this.calculateOperationSize(op), 
        0
      );
      queue.status = failedOperations.length > 0 ? 'error' : 'active';
      queue.lastSyncTimestamp = Date.now();
      queue.updatedAt = new Date();

      await this.persistQueue(queue);

      // 发出事件
      this.emit('queueSynced', {
        userId,
        clientId,
        processedCount: processedOperations.length,
        failedCount: failedOperations.length,
      });

      this.logger.info('Offline queue sync completed', {
        userId,
        clientId,
        processedCount: processedOperations.length,
        failedCount: failedOperations.length,
      });
    } catch (error) {
      queue.status = 'error';
      await this.persistQueue(queue);
      
      this.logger.error('Offline queue sync failed:', error, { userId, clientId });
      throw error;
    }
  }

  /**
   * 合并离线数据
   */
  async mergeOfflineData(
    entityType: string,
    localData: any,
    serverData: any,
    baseData?: any
  ): Promise<{ mergedData: any; conflicts: string[] }> {
    // 选择合并策略
    const strategy = this.mergeStrategies.find(s => s.canHandle(entityType)) || 
                    this.mergeStrategies[this.mergeStrategies.length - 1];

    try {
      // 检测冲突
      const conflicts = strategy.detectConflicts(localData, serverData);

      // 执行合并
      const mergedData = await strategy.merge(localData, serverData, baseData);

      this.logger.debug('Data merged successfully', {
        entityType,
        strategy: strategy.name,
        conflictsCount: conflicts.length,
      });

      return { mergedData, conflicts };
    } catch (error) {
      this.logger.error('Failed to merge data:', error, { entityType });
      throw error;
    }
  }

  /**
   * 获取离线状态
   */
  async getOfflineStatus(userId: string, clientId: string): Promise<OfflineStatus> {
    const queueKey = `${userId}:${clientId}`;
    const queue = this.queues.get(queueKey);

    // 检测网络状态
    const isOnline = await this.checkNetworkStatus();
    const lastOnlineTime = await this.getLastOnlineTime(userId, clientId);

    return {
      isOnline,
      lastOnlineTime,
      queuedOperations: queue?.operations.length || 0,
      pendingSyncSize: queue?.totalSize || 0,
      estimatedSyncTime: this.estimateSyncTime(queue?.operations || []),
      networkLatency: await this.measureNetworkLatency(),
    };
  }

  /**
   * 清理离线队列
   */
  async cleanupOfflineQueue(userId: string, clientId: string, olderThan?: number): Promise<number> {
    const queueKey = `${userId}:${clientId}`;
    const queue = this.queues.get(queueKey);
    
    if (!queue) return 0;

    const cutoffTime = olderThan || (Date.now() - 7 * 24 * 60 * 60 * 1000); // 7天前
    const initialCount = queue.operations.length;

    // 过滤掉过期的操作
    queue.operations = queue.operations.filter(
      op => op.metadata.queuedAt > cutoffTime
    );

    const removedCount = initialCount - queue.operations.length;

    if (removedCount > 0) {
      queue.totalSize = queue.operations.reduce(
        (sum, op) => sum + this.calculateOperationSize(op), 
        0
      );
      queue.updatedAt = new Date();
      await this.persistQueue(queue);

      this.logger.info('Cleaned up offline queue', {
        userId,
        clientId,
        removedCount,
        remainingCount: queue.operations.length,
      });
    }

    return removedCount;
  }

  // 私有方法

  private async loadOfflineQueues(): Promise<void> {
    try {
      const result = await databaseService.query(`
        SELECT * FROM offline_queues 
        WHERE status IN ('active', 'error')
      `);

      for (const row of result.rows) {
        const queue: OfflineQueue = {
          userId: row.user_id,
          clientId: row.client_id,
          operations: row.operations,
          lastSyncTimestamp: row.last_sync_timestamp,
          totalSize: row.total_size,
          status: row.status,
          createdAt: row.created_at,
          updatedAt: row.updated_at,
        };

        const queueKey = `${queue.userId}:${queue.clientId}`;
        this.queues.set(queueKey, queue);
      }

      this.logger.info('Loaded offline queues', {
        count: this.queues.size,
      });
    } catch (error) {
      this.logger.error('Failed to load offline queues:', error);
    }
  }

  private startProcessingLoop(): void {
    this.processingInterval = setInterval(async () => {
      if (!this.isProcessing) {
        await this.processOfflineQueues();
      }
    }, 30000); // 每30秒处理一次

    this.logger.info('Offline processing loop started');
  }

  private async processOfflineQueues(): Promise<void> {
    this.isProcessing = true;

    try {
      for (const [queueKey, queue] of this.queues) {
        if (queue.status === 'active' && queue.operations.length > 0) {
          // 检查是否在线
          const isOnline = await this.checkNetworkStatus();
          if (isOnline) {
            await this.syncOfflineQueue(queue.userId, queue.clientId);
          }
        }
      }
    } catch (error) {
      this.logger.error('Error in offline processing loop:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async processOfflineOperation(operation: OfflineOperation): Promise<void> {
    // 转换为数据变更记录
    const changeRecord: DataChangeRecord = {
      id: operation.id,
      entityType: operation.entityType,
      entityId: operation.entityId,
      operation: operation.type,
      data: operation.data,
      previousData: operation.previousData,
      userId: operation.userId,
      timestamp: operation.timestamp,
      version: operation.version,
      checksum: this.calculateChecksum(operation.data),
      metadata: {
        source: 'client',
        clientId: operation.clientId,
        offline: true,
        ...operation.metadata,
      },
    };

    // 创建同步任务
    await dataSyncService.createSyncTask(
      operation.userId,
      operation.clientId,
      [changeRecord],
      operation.metadata.priority
    );
  }

  private sortOperationsByDependencies(operations: OfflineOperation[]): OfflineOperation[] {
    const sorted: OfflineOperation[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();

    const visit = (operation: OfflineOperation) => {
      if (visiting.has(operation.id)) {
        throw new Error(`Circular dependency detected: ${operation.id}`);
      }
      
      if (visited.has(operation.id)) {
        return;
      }

      visiting.add(operation.id);

      // 先处理依赖
      for (const depId of operation.dependencies) {
        const depOperation = operations.find(op => op.id === depId);
        if (depOperation) {
          visit(depOperation);
        }
      }

      visiting.delete(operation.id);
      visited.add(operation.id);
      sorted.push(operation);
    };

    for (const operation of operations) {
      if (!visited.has(operation.id)) {
        visit(operation);
      }
    }

    return sorted;
  }

  private calculateOperationSize(operation: OfflineOperation): number {
    return JSON.stringify(operation).length;
  }

  private estimateSyncTime(operations: OfflineOperation[]): number {
    // 估算同步时间：每个操作100ms + 网络延迟
    return operations.length * 100 + (operations.length * 50); // 50ms网络延迟
  }

  private async checkNetworkStatus(): Promise<boolean> {
    try {
      // 简单的网络检测
      const response = await fetch(`${config.api.baseUrl}/health`, {
        method: 'HEAD',
        timeout: 5000,
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  private async getLastOnlineTime(userId: string, clientId: string): Promise<number> {
    try {
      const key = `last_online:${userId}:${clientId}`;
      const timestamp = await redisService.get<number>(key);
      return timestamp || 0;
    } catch {
      return 0;
    }
  }

  private async measureNetworkLatency(): Promise<number> {
    try {
      const start = Date.now();
      await fetch(`${config.api.baseUrl}/ping`, {
        method: 'HEAD',
        timeout: 5000,
      });
      return Date.now() - start;
    } catch {
      return -1; // 无法测量
    }
  }

  private async persistQueue(queue: OfflineQueue): Promise<void> {
    await databaseService.query(`
      INSERT INTO offline_queues (
        user_id, client_id, operations, last_sync_timestamp,
        total_size, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (user_id, client_id) DO UPDATE SET
        operations = EXCLUDED.operations,
        last_sync_timestamp = EXCLUDED.last_sync_timestamp,
        total_size = EXCLUDED.total_size,
        status = EXCLUDED.status,
        updated_at = EXCLUDED.updated_at
    `, [
      queue.userId,
      queue.clientId,
      JSON.stringify(queue.operations),
      queue.lastSyncTimestamp,
      queue.totalSize,
      queue.status,
      queue.createdAt,
      queue.updatedAt,
    ]);
  }

  private calculateChecksum(data: any): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(JSON.stringify(data)).digest('hex');
  }

  private generateOperationId(): string {
    return `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取队列统计
   */
  getQueueStats(): {
    totalQueues: number;
    totalOperations: number;
    totalSize: number;
    queuesByStatus: Record<string, number>;
  } {
    const stats = {
      totalQueues: this.queues.size,
      totalOperations: 0,
      totalSize: 0,
      queuesByStatus: {} as Record<string, number>,
    };

    for (const queue of this.queues.values()) {
      stats.totalOperations += queue.operations.length;
      stats.totalSize += queue.totalSize;
      stats.queuesByStatus[queue.status] = (stats.queuesByStatus[queue.status] || 0) + 1;
    }

    return stats;
  }

  /**
   * 关闭服务
   */
  async close(): Promise<void> {
    this.logger.info('Closing offline sync service...');

    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }

    this.queues.clear();

    this.logger.info('Offline sync service closed');
  }
}

// 导出单例实例
export const offlineSyncService = new OfflineSyncService();
