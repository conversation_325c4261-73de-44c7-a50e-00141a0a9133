// ERP智能助手后端服务 - 语义搜索服务

import { OpenAI } from 'openai';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { vectorDatabaseService, SearchResult, SearchOptions } from '@/services/vector-database';
import { redisService } from '@/services/redis';
import { ExternalServiceError } from '@/middleware/error-handler';

// 搜索查询接口
export interface SearchQuery {
  text: string;
  type?: 'operation' | 'documentation' | 'error' | 'guidance' | 'all';
  filters?: {
    userId?: string;
    sessionId?: string;
    dateRange?: { start: Date; end: Date };
    tags?: string[];
    language?: string;
    minQuality?: number;
  };
  options?: {
    limit?: number;
    threshold?: number;
    rerank?: boolean;
    expandQuery?: boolean;
    includeMetadata?: boolean;
  };
}

// 搜索结果增强接口
export interface EnhancedSearchResult extends SearchResult {
  highlights?: string[];
  explanation?: string;
  relatedResults?: SearchResult[];
  searchContext?: {
    queryExpansion?: string[];
    appliedFilters?: Record<string, any>;
    searchStrategy?: string;
  };
}

// 搜索统计接口
export interface SearchStats {
  totalSearches: number;
  averageResponseTime: number;
  popularQueries: Array<{ query: string; count: number }>;
  searchTypes: Record<string, number>;
  cacheHitRate: number;
  userSearchPatterns: Record<string, number>;
}

// 查询扩展器
class QueryExpander {
  private synonyms: Map<string, string[]> = new Map();
  private logger: Logger;

  constructor() {
    this.logger = new Logger('QueryExpander');
    this.initializeSynonyms();
  }

  private initializeSynonyms(): void {
    // 初始化同义词词典
    this.synonyms.set('点击', ['click', '单击', '选择', '按下']);
    this.synonyms.set('输入', ['input', '填写', '录入', '键入']);
    this.synonyms.set('提交', ['submit', '发送', '确认', '保存']);
    this.synonyms.set('错误', ['error', '异常', '问题', '故障']);
    this.synonyms.set('登录', ['login', '登陆', '签到', '认证']);
    this.synonyms.set('搜索', ['search', '查找', '检索', '查询']);
    // 可以从数据库或配置文件加载更多同义词
  }

  async expandQuery(query: string): Promise<string[]> {
    const expansions = [query];
    const words = query.toLowerCase().split(/\s+/);

    for (const word of words) {
      const synonyms = this.synonyms.get(word);
      if (synonyms) {
        // 添加同义词变体
        for (const synonym of synonyms) {
          const expandedQuery = query.replace(new RegExp(word, 'gi'), synonym);
          if (expandedQuery !== query) {
            expansions.push(expandedQuery);
          }
        }
      }
    }

    // 限制扩展数量
    return expansions.slice(0, 5);
  }
}

// 结果重排序器
class ResultReranker {
  private logger: Logger;

  constructor() {
    this.logger = new Logger('ResultReranker');
  }

  async rerank(query: string, results: SearchResult[]): Promise<SearchResult[]> {
    // 基于多个因素重新排序结果
    return results
      .map(result => ({
        ...result,
        rerankScore: this.calculateRerankScore(query, result),
      }))
      .sort((a, b) => (b as any).rerankScore - (a as any).rerankScore)
      .map(({ rerankScore, ...result }) => result);
  }

  private calculateRerankScore(query: string, result: SearchResult): number {
    let score = result.score;

    // 基于内容匹配度
    const contentMatch = this.calculateContentMatch(query, result.content);
    score += contentMatch * 0.3;

    // 基于元数据相关性
    const metadataRelevance = this.calculateMetadataRelevance(query, result.metadata);
    score += metadataRelevance * 0.2;

    // 基于时间新鲜度
    const freshness = this.calculateFreshness(result.metadata.timestamp);
    score += freshness * 0.1;

    // 基于质量分数
    if (result.metadata.quality) {
      score += result.metadata.quality * 0.1;
    }

    return Math.min(score, 1.0);
  }

  private calculateContentMatch(query: string, content: string): number {
    const queryWords = query.toLowerCase().split(/\s+/);
    const contentWords = content.toLowerCase().split(/\s+/);
    
    let matches = 0;
    for (const queryWord of queryWords) {
      if (contentWords.some(contentWord => 
        contentWord.includes(queryWord) || queryWord.includes(contentWord)
      )) {
        matches++;
      }
    }

    return matches / queryWords.length;
  }

  private calculateMetadataRelevance(query: string, metadata: any): number {
    let relevance = 0;

    // 检查标签匹配
    if (metadata.tags && Array.isArray(metadata.tags)) {
      const queryLower = query.toLowerCase();
      for (const tag of metadata.tags) {
        if (queryLower.includes(tag.toLowerCase())) {
          relevance += 0.2;
        }
      }
    }

    // 检查类型相关性
    if (metadata.type) {
      const typeRelevance = this.getTypeRelevance(query, metadata.type);
      relevance += typeRelevance;
    }

    return Math.min(relevance, 1.0);
  }

  private getTypeRelevance(query: string, type: string): number {
    const queryLower = query.toLowerCase();
    
    if (type === 'error' && (queryLower.includes('错误') || queryLower.includes('error'))) {
      return 0.3;
    }
    if (type === 'documentation' && (queryLower.includes('文档') || queryLower.includes('帮助'))) {
      return 0.3;
    }
    if (type === 'operation' && (queryLower.includes('操作') || queryLower.includes('步骤'))) {
      return 0.3;
    }
    
    return 0;
  }

  private calculateFreshness(timestamp: number): number {
    const now = Date.now();
    const daysSince = (now - timestamp) / (1000 * 60 * 60 * 24);
    
    if (daysSince < 1) return 0.2;
    if (daysSince < 7) return 0.15;
    if (daysSince < 30) return 0.1;
    if (daysSince < 90) return 0.05;
    
    return 0;
  }
}

export class SemanticSearchService {
  private logger: Logger;
  private openai: OpenAI | null = null;
  private queryExpander: QueryExpander;
  private resultReranker: ResultReranker;
  private stats: SearchStats;
  private searchCache = new Map<string, { results: EnhancedSearchResult[]; timestamp: number }>();
  private cacheTimeout = 300000; // 5分钟

  constructor() {
    this.logger = new Logger('SemanticSearchService');
    this.queryExpander = new QueryExpander();
    this.resultReranker = new ResultReranker();
    this.stats = {
      totalSearches: 0,
      averageResponseTime: 0,
      popularQueries: [],
      searchTypes: {},
      cacheHitRate: 0,
      userSearchPatterns: {},
    };
  }

  /**
   * 初始化语义搜索服务
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing semantic search service...');

      // 初始化OpenAI客户端
      if (config.ai.openai.apiKey) {
        this.openai = new OpenAI({
          apiKey: config.ai.openai.apiKey,
        });
      }

      // 加载统计信息
      await this.loadStats();

      this.logger.info('Semantic search service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize semantic search service:', error);
      throw error;
    }
  }

  /**
   * 执行语义搜索
   */
  async search(query: SearchQuery, userId?: string): Promise<EnhancedSearchResult[]> {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(query, userId);

    try {
      // 检查缓存
      const cachedResults = this.getFromCache(cacheKey);
      if (cachedResults) {
        this.updateStats(query, Date.now() - startTime, true, userId);
        return cachedResults;
      }

      this.logger.debug('Executing semantic search', {
        query: query.text.substring(0, 100),
        type: query.type,
        userId,
      });

      // 查询扩展
      let searchQueries = [query.text];
      if (query.options?.expandQuery) {
        searchQueries = await this.queryExpander.expandQuery(query.text);
      }

      // 执行多个搜索
      const allResults: SearchResult[] = [];
      
      for (const searchQuery of searchQueries) {
        const results = await this.executeSearch(searchQuery, query);
        allResults.push(...results);
      }

      // 去重和合并结果
      const uniqueResults = this.deduplicateResults(allResults);

      // 重排序
      let finalResults = uniqueResults;
      if (query.options?.rerank) {
        finalResults = await this.resultReranker.rerank(query.text, uniqueResults);
      }

      // 增强结果
      const enhancedResults = await this.enhanceResults(query.text, finalResults, query);

      // 缓存结果
      this.setCache(cacheKey, enhancedResults);

      // 更新统计
      this.updateStats(query, Date.now() - startTime, false, userId);

      this.logger.info('Semantic search completed', {
        query: query.text.substring(0, 100),
        resultCount: enhancedResults.length,
        responseTime: `${Date.now() - startTime}ms`,
      });

      return enhancedResults;
    } catch (error) {
      this.logger.error('Semantic search failed:', error, {
        query: query.text.substring(0, 100),
      });
      throw error;
    }
  }

  /**
   * 获取相关建议
   */
  async getSuggestions(partialQuery: string, limit: number = 5): Promise<string[]> {
    try {
      // 从流行查询中获取建议
      const suggestions = this.stats.popularQueries
        .filter(pq => pq.query.toLowerCase().includes(partialQuery.toLowerCase()))
        .slice(0, limit)
        .map(pq => pq.query);

      // 如果建议不足，可以使用AI生成更多建议
      if (suggestions.length < limit && this.openai) {
        const aiSuggestions = await this.generateAISuggestions(partialQuery, limit - suggestions.length);
        suggestions.push(...aiSuggestions);
      }

      return suggestions;
    } catch (error) {
      this.logger.error('Failed to get search suggestions:', error);
      return [];
    }
  }

  /**
   * 获取搜索统计
   */
  getStats(): SearchStats {
    return { ...this.stats };
  }

  // 私有方法

  private async executeSearch(searchQuery: string, query: SearchQuery): Promise<SearchResult[]> {
    const collections = this.getCollections(query.type);
    const allResults: SearchResult[] = [];

    for (const collection of collections) {
      const searchOptions: SearchOptions = {
        limit: query.options?.limit || 20,
        threshold: query.options?.threshold || 0.7,
        filter: this.buildFilter(query.filters),
        includeMetadata: query.options?.includeMetadata !== false,
        includeDocuments: true,
      };

      const results = await vectorDatabaseService.searchDocuments(
        collection,
        searchQuery,
        searchOptions
      );

      allResults.push(...results);
    }

    return allResults;
  }

  private getCollections(type?: string): string[] {
    if (!type || type === 'all') {
      return ['operations', 'documentation', 'errors', 'guidance'];
    }
    
    const collectionMap = {
      operation: ['operations'],
      documentation: ['documentation'],
      error: ['errors'],
      guidance: ['guidance'],
    };

    return collectionMap[type] || ['operations'];
  }

  private buildFilter(filters?: SearchQuery['filters']): Record<string, any> | undefined {
    if (!filters) return undefined;

    const filter: Record<string, any> = {};

    if (filters.userId) {
      filter.userId = filters.userId;
    }

    if (filters.sessionId) {
      filter.sessionId = filters.sessionId;
    }

    if (filters.tags && filters.tags.length > 0) {
      filter.tags = { $in: filters.tags };
    }

    if (filters.language) {
      filter.language = filters.language;
    }

    if (filters.minQuality) {
      filter.quality = { $gte: filters.minQuality };
    }

    if (filters.dateRange) {
      filter.timestamp = {
        $gte: filters.dateRange.start.getTime(),
        $lte: filters.dateRange.end.getTime(),
      };
    }

    return Object.keys(filter).length > 0 ? filter : undefined;
  }

  private deduplicateResults(results: SearchResult[]): SearchResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      if (seen.has(result.id)) {
        return false;
      }
      seen.add(result.id);
      return true;
    });
  }

  private async enhanceResults(
    query: string,
    results: SearchResult[],
    searchQuery: SearchQuery
  ): Promise<EnhancedSearchResult[]> {
    return results.map(result => ({
      ...result,
      highlights: this.generateHighlights(query, result.content),
      explanation: this.generateExplanation(result),
      searchContext: {
        appliedFilters: searchQuery.filters,
        searchStrategy: this.getSearchStrategy(searchQuery),
      },
    }));
  }

  private generateHighlights(query: string, content: string): string[] {
    const queryWords = query.toLowerCase().split(/\s+/);
    const highlights: string[] = [];

    for (const word of queryWords) {
      const regex = new RegExp(`(.{0,30}${word}.{0,30})`, 'gi');
      const matches = content.match(regex);
      if (matches) {
        highlights.push(...matches.slice(0, 3)); // 最多3个高亮
      }
    }

    return highlights;
  }

  private generateExplanation(result: SearchResult): string {
    const explanations = [];

    if (result.score > 0.9) {
      explanations.push('高度相关匹配');
    } else if (result.score > 0.7) {
      explanations.push('相关匹配');
    }

    if (result.metadata.type) {
      explanations.push(`类型: ${result.metadata.type}`);
    }

    if (result.metadata.quality && result.metadata.quality > 0.8) {
      explanations.push('高质量内容');
    }

    return explanations.join(', ');
  }

  private getSearchStrategy(query: SearchQuery): string {
    const strategies = [];

    if (query.options?.expandQuery) {
      strategies.push('查询扩展');
    }

    if (query.options?.rerank) {
      strategies.push('结果重排序');
    }

    if (query.filters) {
      strategies.push('过滤器应用');
    }

    return strategies.join(', ') || '基础搜索';
  }

  private async generateAISuggestions(partialQuery: string, count: number): Promise<string[]> {
    if (!this.openai) return [];

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个搜索建议助手。根据用户的部分查询，生成相关的搜索建议。',
          },
          {
            role: 'user',
            content: `为以下部分查询生成${count}个相关的搜索建议："${partialQuery}"`,
          },
        ],
        max_tokens: 100,
        temperature: 0.7,
      });

      const suggestions = response.choices[0]?.message?.content
        ?.split('\n')
        .filter(s => s.trim())
        .slice(0, count) || [];

      return suggestions;
    } catch (error) {
      this.logger.error('Failed to generate AI suggestions:', error);
      return [];
    }
  }

  private generateCacheKey(query: SearchQuery, userId?: string): string {
    const keyData = {
      text: query.text,
      type: query.type,
      filters: query.filters,
      options: query.options,
      userId,
    };
    
    const crypto = require('crypto');
    return crypto.createHash('md5').update(JSON.stringify(keyData)).digest('hex');
  }

  private getFromCache(key: string): EnhancedSearchResult[] | null {
    const cached = this.searchCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.results;
    }
    return null;
  }

  private setCache(key: string, results: EnhancedSearchResult[]): void {
    this.searchCache.set(key, { results, timestamp: Date.now() });
    
    // 清理过期缓存
    if (this.searchCache.size > 1000) {
      this.cleanupCache();
    }
  }

  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, value] of this.searchCache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        this.searchCache.delete(key);
      }
    }
  }

  private updateStats(query: SearchQuery, responseTime: number, cacheHit: boolean, userId?: string): void {
    this.stats.totalSearches++;
    
    // 更新平均响应时间
    this.stats.averageResponseTime = 
      (this.stats.averageResponseTime * (this.stats.totalSearches - 1) + responseTime) / 
      this.stats.totalSearches;

    // 更新缓存命中率
    const totalSearches = this.stats.totalSearches;
    const currentHitRate = this.stats.cacheHitRate;
    
    if (cacheHit) {
      this.stats.cacheHitRate = (currentHitRate * (totalSearches - 1) + 1) / totalSearches;
    } else {
      this.stats.cacheHitRate = (currentHitRate * (totalSearches - 1)) / totalSearches;
    }

    // 更新流行查询
    this.updatePopularQueries(query.text);

    // 更新搜索类型统计
    const type = query.type || 'all';
    this.stats.searchTypes[type] = (this.stats.searchTypes[type] || 0) + 1;

    // 更新用户搜索模式
    if (userId) {
      this.stats.userSearchPatterns[userId] = (this.stats.userSearchPatterns[userId] || 0) + 1;
    }
  }

  private updatePopularQueries(query: string): void {
    const existing = this.stats.popularQueries.find(pq => pq.query === query);
    if (existing) {
      existing.count++;
    } else {
      this.stats.popularQueries.push({ query, count: 1 });
    }

    // 保持前20个流行查询
    this.stats.popularQueries.sort((a, b) => b.count - a.count);
    this.stats.popularQueries = this.stats.popularQueries.slice(0, 20);
  }

  private async loadStats(): Promise<void> {
    try {
      const cachedStats = await redisService.get<SearchStats>('search_stats');
      if (cachedStats) {
        this.stats = { ...this.stats, ...cachedStats };
      }
    } catch (error) {
      this.logger.debug('Failed to load cached search stats:', error);
    }
  }

  private async saveStats(): Promise<void> {
    try {
      await redisService.set('search_stats', this.stats, { ttl: 3600 });
    } catch (error) {
      this.logger.debug('Failed to save search stats:', error);
    }
  }

  /**
   * 关闭服务
   */
  async close(): Promise<void> {
    this.logger.info('Closing semantic search service...');
    
    // 保存统计信息
    await this.saveStats();
    
    // 清理缓存
    this.searchCache.clear();
    
    this.logger.info('Semantic search service closed');
  }
}

// 导出单例实例
export const semanticSearchService = new SemanticSearchService();
