// ERP智能助手后端服务 - 缓存优化服务

import { EventEmitter } from 'events';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { redisService } from '@/services/redis';
import { performanceMonitorService, MetricType } from '@/services/performance-monitor';

// 缓存策略枚举
export enum CacheStrategy {
  LRU = 'lru', // 最近最少使用
  LFU = 'lfu', // 最少使用频率
  TTL = 'ttl', // 基于时间过期
  WRITE_THROUGH = 'write_through', // 写穿透
  WRITE_BACK = 'write_back', // 写回
  WRITE_AROUND = 'write_around', // 写绕过
}

// 缓存层级枚举
export enum CacheLevel {
  L1_MEMORY = 'l1_memory', // 内存缓存
  L2_REDIS = 'l2_redis', // Redis缓存
  L3_DATABASE = 'l3_database', // 数据库缓存
}

// 缓存项接口
export interface CacheItem {
  key: string;
  value: any;
  ttl: number;
  createdAt: number;
  lastAccessed: number;
  accessCount: number;
  size: number;
  tags: string[];
  metadata: Record<string, any>;
}

// 缓存配置接口
export interface CacheConfig {
  strategy: CacheStrategy;
  maxSize: number; // 最大缓存大小（字节）
  maxItems: number; // 最大缓存项数
  defaultTTL: number; // 默认过期时间（秒）
  cleanupInterval: number; // 清理间隔（秒）
  compressionEnabled: boolean; // 是否启用压缩
  serializationFormat: 'json' | 'msgpack' | 'protobuf';
}

// 缓存统计接口
export interface CacheStats {
  level: CacheLevel;
  hitCount: number;
  missCount: number;
  hitRate: number;
  evictionCount: number;
  totalSize: number;
  itemCount: number;
  averageAccessTime: number;
  memoryUsage: number;
}

// 缓存热点接口
export interface CacheHotspot {
  key: string;
  accessCount: number;
  lastAccessed: number;
  averageResponseTime: number;
  size: number;
  tags: string[];
}

export class CacheOptimizerService extends EventEmitter {
  private logger: Logger;
  private l1Cache = new Map<string, CacheItem>(); // 内存缓存
  private cacheConfigs = new Map<CacheLevel, CacheConfig>();
  private stats = new Map<CacheLevel, CacheStats>();
  private hotspots: CacheHotspot[] = [];
  private cleanupInterval?: NodeJS.Timeout;
  private optimizationInterval?: NodeJS.Timeout;

  constructor() {
    super();
    this.logger = new Logger('CacheOptimizerService');
    this.setupDefaultConfigs();
    this.initializeStats();
  }

  /**
   * 初始化缓存优化服务
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing cache optimizer service...');

      // 启动定期清理
      this.startPeriodicCleanup();

      // 启动缓存优化
      this.startCacheOptimization();

      // 预热关键缓存
      await this.warmupCache();

      this.logger.info('Cache optimizer service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize cache optimizer service:', error);
      throw error;
    }
  }

  /**
   * 多层缓存获取
   */
  async get<T>(key: string, options: {
    levels?: CacheLevel[];
    fallback?: () => Promise<T>;
    ttl?: number;
    tags?: string[];
  } = {}): Promise<T | null> {
    const startTime = Date.now();
    const levels = options.levels || [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS];

    try {
      // 按层级顺序查找
      for (const level of levels) {
        const value = await this.getFromLevel<T>(key, level);
        if (value !== null) {
          // 记录命中
          this.recordHit(level, Date.now() - startTime);
          
          // 向上层缓存回填
          await this.backfillCache(key, value, level, levels, options.ttl, options.tags);
          
          return value;
        }
      }

      // 所有层级都未命中，记录未命中
      levels.forEach(level => this.recordMiss(level));

      // 尝试从回调函数获取数据
      if (options.fallback) {
        const value = await options.fallback();
        if (value !== null) {
          // 存储到所有层级
          await this.setMultiLevel(key, value, {
            levels,
            ttl: options.ttl,
            tags: options.tags,
          });
        }
        return value;
      }

      return null;
    } catch (error) {
      this.logger.error('Cache get operation failed:', error, { key });
      return null;
    }
  }

  /**
   * 多层缓存设置
   */
  async set(key: string, value: any, options: {
    levels?: CacheLevel[];
    ttl?: number;
    tags?: string[];
    strategy?: CacheStrategy;
  } = {}): Promise<void> {
    await this.setMultiLevel(key, value, options);
  }

  /**
   * 删除缓存
   */
  async delete(key: string, levels?: CacheLevel[]): Promise<void> {
    const targetLevels = levels || [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS];

    for (const level of targetLevels) {
      await this.deleteFromLevel(key, level);
    }

    this.emit('cacheDeleted', { key, levels: targetLevels });
  }

  /**
   * 按标签清理缓存
   */
  async deleteByTags(tags: string[], levels?: CacheLevel[]): Promise<number> {
    const targetLevels = levels || [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS];
    let deletedCount = 0;

    for (const level of targetLevels) {
      deletedCount += await this.deleteByTagsFromLevel(tags, level);
    }

    this.emit('cacheDeletedByTags', { tags, levels: targetLevels, count: deletedCount });
    return deletedCount;
  }

  /**
   * 缓存预热
   */
  async warmup(keys: Array<{
    key: string;
    loader: () => Promise<any>;
    ttl?: number;
    tags?: string[];
  }>): Promise<void> {
    this.logger.info('Starting cache warmup', { keyCount: keys.length });

    const promises = keys.map(async ({ key, loader, ttl, tags }) => {
      try {
        const value = await loader();
        await this.set(key, value, { ttl, tags });
      } catch (error) {
        this.logger.error('Cache warmup failed for key:', error, { key });
      }
    });

    await Promise.allSettled(promises);
    this.logger.info('Cache warmup completed');
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): Map<CacheLevel, CacheStats> {
    // 更新L1缓存统计
    this.updateL1Stats();
    return new Map(this.stats);
  }

  /**
   * 获取缓存热点
   */
  getCacheHotspots(limit: number = 10): CacheHotspot[] {
    return this.hotspots
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, limit);
  }

  /**
   * 优化缓存配置
   */
  async optimizeCache(): Promise<void> {
    this.logger.info('Starting cache optimization...');

    try {
      // 分析访问模式
      await this.analyzeAccessPatterns();

      // 调整缓存大小
      await this.adjustCacheSize();

      // 优化TTL设置
      await this.optimizeTTL();

      // 清理低效缓存
      await this.cleanupIneffectiveCache();

      this.logger.info('Cache optimization completed');
    } catch (error) {
      this.logger.error('Cache optimization failed:', error);
    }
  }

  // 私有方法

  private setupDefaultConfigs(): void {
    // L1内存缓存配置
    this.cacheConfigs.set(CacheLevel.L1_MEMORY, {
      strategy: CacheStrategy.LRU,
      maxSize: 100 * 1024 * 1024, // 100MB
      maxItems: 10000,
      defaultTTL: 300, // 5分钟
      cleanupInterval: 60, // 1分钟
      compressionEnabled: false,
      serializationFormat: 'json',
    });

    // L2 Redis缓存配置
    this.cacheConfigs.set(CacheLevel.L2_REDIS, {
      strategy: CacheStrategy.TTL,
      maxSize: 1024 * 1024 * 1024, // 1GB
      maxItems: 100000,
      defaultTTL: 3600, // 1小时
      cleanupInterval: 300, // 5分钟
      compressionEnabled: true,
      serializationFormat: 'msgpack',
    });
  }

  private initializeStats(): void {
    for (const level of Object.values(CacheLevel)) {
      this.stats.set(level as CacheLevel, {
        level: level as CacheLevel,
        hitCount: 0,
        missCount: 0,
        hitRate: 0,
        evictionCount: 0,
        totalSize: 0,
        itemCount: 0,
        averageAccessTime: 0,
        memoryUsage: 0,
      });
    }
  }

  private async getFromLevel<T>(key: string, level: CacheLevel): Promise<T | null> {
    switch (level) {
      case CacheLevel.L1_MEMORY:
        return this.getFromL1<T>(key);
      case CacheLevel.L2_REDIS:
        return this.getFromL2<T>(key);
      default:
        return null;
    }
  }

  private getFromL1<T>(key: string): T | null {
    const item = this.l1Cache.get(key);
    if (!item) return null;

    // 检查过期
    if (Date.now() > item.createdAt + item.ttl * 1000) {
      this.l1Cache.delete(key);
      return null;
    }

    // 更新访问信息
    item.lastAccessed = Date.now();
    item.accessCount++;

    return item.value as T;
  }

  private async getFromL2<T>(key: string): Promise<T | null> {
    try {
      const value = await redisService.get<T>(key);
      if (value !== null) {
        // 记录访问
        await this.recordL2Access(key);
      }
      return value;
    } catch (error) {
      this.logger.error('L2 cache get failed:', error, { key });
      return null;
    }
  }

  private async setMultiLevel(key: string, value: any, options: {
    levels?: CacheLevel[];
    ttl?: number;
    tags?: string[];
    strategy?: CacheStrategy;
  }): Promise<void> {
    const levels = options.levels || [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS];

    for (const level of levels) {
      await this.setToLevel(key, value, level, options);
    }

    this.emit('cacheSet', { key, levels, ttl: options.ttl });
  }

  private async setToLevel(key: string, value: any, level: CacheLevel, options: {
    ttl?: number;
    tags?: string[];
    strategy?: CacheStrategy;
  }): Promise<void> {
    switch (level) {
      case CacheLevel.L1_MEMORY:
        await this.setToL1(key, value, options);
        break;
      case CacheLevel.L2_REDIS:
        await this.setToL2(key, value, options);
        break;
    }
  }

  private async setToL1(key: string, value: any, options: {
    ttl?: number;
    tags?: string[];
  }): Promise<void> {
    const config = this.cacheConfigs.get(CacheLevel.L1_MEMORY)!;
    const ttl = options.ttl || config.defaultTTL;
    const size = this.calculateSize(value);

    // 检查容量限制
    await this.ensureL1Capacity(size);

    const item: CacheItem = {
      key,
      value,
      ttl,
      createdAt: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 1,
      size,
      tags: options.tags || [],
      metadata: {},
    };

    this.l1Cache.set(key, item);
  }

  private async setToL2(key: string, value: any, options: {
    ttl?: number;
    tags?: string[];
  }): Promise<void> {
    try {
      const config = this.cacheConfigs.get(CacheLevel.L2_REDIS)!;
      const ttl = options.ttl || config.defaultTTL;

      await redisService.set(key, value, { ttl });

      // 存储标签信息
      if (options.tags && options.tags.length > 0) {
        await this.setTagsForKey(key, options.tags);
      }
    } catch (error) {
      this.logger.error('L2 cache set failed:', error, { key });
    }
  }

  private async deleteFromLevel(key: string, level: CacheLevel): Promise<void> {
    switch (level) {
      case CacheLevel.L1_MEMORY:
        this.l1Cache.delete(key);
        break;
      case CacheLevel.L2_REDIS:
        await redisService.del(key);
        await this.removeTagsForKey(key);
        break;
    }
  }

  private async deleteByTagsFromLevel(tags: string[], level: CacheLevel): Promise<number> {
    let deletedCount = 0;

    switch (level) {
      case CacheLevel.L1_MEMORY:
        for (const [key, item] of this.l1Cache) {
          if (tags.some(tag => item.tags.includes(tag))) {
            this.l1Cache.delete(key);
            deletedCount++;
          }
        }
        break;
      case CacheLevel.L2_REDIS:
        deletedCount = await this.deleteL2ByTags(tags);
        break;
    }

    return deletedCount;
  }

  private async backfillCache(
    key: string,
    value: any,
    sourceLevel: CacheLevel,
    allLevels: CacheLevel[],
    ttl?: number,
    tags?: string[]
  ): Promise<void> {
    const sourceLevelIndex = allLevels.indexOf(sourceLevel);
    if (sourceLevelIndex <= 0) return;

    // 向更高层级回填
    const higherLevels = allLevels.slice(0, sourceLevelIndex);
    for (const level of higherLevels) {
      await this.setToLevel(key, value, level, { ttl, tags });
    }
  }

  private recordHit(level: CacheLevel, accessTime: number): void {
    const stats = this.stats.get(level)!;
    stats.hitCount++;
    stats.hitRate = stats.hitCount / (stats.hitCount + stats.missCount);
    stats.averageAccessTime = (stats.averageAccessTime + accessTime) / 2;

    // 记录性能指标
    performanceMonitorService.recordCacheOperation('hit', level, accessTime);
  }

  private recordMiss(level: CacheLevel): void {
    const stats = this.stats.get(level)!;
    stats.missCount++;
    stats.hitRate = stats.hitCount / (stats.hitCount + stats.missCount);

    // 记录性能指标
    performanceMonitorService.recordCacheOperation('miss', level);
  }

  private async ensureL1Capacity(newItemSize: number): Promise<void> {
    const config = this.cacheConfigs.get(CacheLevel.L1_MEMORY)!;
    
    // 检查项目数量限制
    while (this.l1Cache.size >= config.maxItems) {
      await this.evictL1Item();
    }

    // 检查大小限制
    let currentSize = this.calculateL1Size();
    while (currentSize + newItemSize > config.maxSize) {
      await this.evictL1Item();
      currentSize = this.calculateL1Size();
    }
  }

  private async evictL1Item(): Promise<void> {
    const config = this.cacheConfigs.get(CacheLevel.L1_MEMORY)!;
    let victimKey: string | null = null;

    switch (config.strategy) {
      case CacheStrategy.LRU:
        victimKey = this.findLRUVictim();
        break;
      case CacheStrategy.LFU:
        victimKey = this.findLFUVictim();
        break;
      default:
        victimKey = this.l1Cache.keys().next().value;
    }

    if (victimKey) {
      this.l1Cache.delete(victimKey);
      const stats = this.stats.get(CacheLevel.L1_MEMORY)!;
      stats.evictionCount++;
    }
  }

  private findLRUVictim(): string | null {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, item] of this.l1Cache) {
      if (item.lastAccessed < oldestTime) {
        oldestTime = item.lastAccessed;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  private findLFUVictim(): string | null {
    let leastUsedKey: string | null = null;
    let leastCount = Infinity;

    for (const [key, item] of this.l1Cache) {
      if (item.accessCount < leastCount) {
        leastCount = item.accessCount;
        leastUsedKey = key;
      }
    }

    return leastUsedKey;
  }

  private calculateSize(value: any): number {
    return JSON.stringify(value).length * 2; // 粗略估算
  }

  private calculateL1Size(): number {
    let totalSize = 0;
    for (const item of this.l1Cache.values()) {
      totalSize += item.size;
    }
    return totalSize;
  }

  private updateL1Stats(): void {
    const stats = this.stats.get(CacheLevel.L1_MEMORY)!;
    stats.itemCount = this.l1Cache.size;
    stats.totalSize = this.calculateL1Size();
    stats.memoryUsage = stats.totalSize;
  }

  private async setTagsForKey(key: string, tags: string[]): Promise<void> {
    for (const tag of tags) {
      await redisService.sadd(`tag:${tag}`, key);
    }
    await redisService.set(`tags:${key}`, tags);
  }

  private async removeTagsForKey(key: string): Promise<void> {
    const tags = await redisService.get<string[]>(`tags:${key}`);
    if (tags) {
      for (const tag of tags) {
        await redisService.srem(`tag:${tag}`, key);
      }
      await redisService.del(`tags:${key}`);
    }
  }

  private async deleteL2ByTags(tags: string[]): Promise<number> {
    let deletedCount = 0;
    const keysToDelete = new Set<string>();

    for (const tag of tags) {
      const keys = await redisService.smembers(`tag:${tag}`);
      keys.forEach(key => keysToDelete.add(key));
    }

    for (const key of keysToDelete) {
      await redisService.del(key);
      await this.removeTagsForKey(key);
      deletedCount++;
    }

    return deletedCount;
  }

  private async recordL2Access(key: string): Promise<void> {
    // 记录访问统计
    await redisService.incr(`access:${key}`);
    await redisService.set(`last_access:${key}`, Date.now());
  }

  private startPeriodicCleanup(): void {
    this.cleanupInterval = setInterval(async () => {
      await this.performCleanup();
    }, 60000); // 每分钟清理一次
  }

  private startCacheOptimization(): void {
    this.optimizationInterval = setInterval(async () => {
      await this.optimizeCache();
    }, 300000); // 每5分钟优化一次
  }

  private async performCleanup(): Promise<void> {
    try {
      // 清理过期的L1缓存项
      const now = Date.now();
      for (const [key, item] of this.l1Cache) {
        if (now > item.createdAt + item.ttl * 1000) {
          this.l1Cache.delete(key);
        }
      }

      // 更新热点统计
      await this.updateHotspots();

    } catch (error) {
      this.logger.error('Cache cleanup failed:', error);
    }
  }

  private async warmupCache(): Promise<void> {
    // 预热常用的缓存键
    const commonKeys = [
      'system_config',
      'user_permissions',
      'api_rate_limits',
    ];

    for (const key of commonKeys) {
      try {
        // 这里应该根据实际业务逻辑加载数据
        await this.get(key, {
          fallback: async () => {
            // 模拟数据加载
            return { key, loaded: true, timestamp: Date.now() };
          },
          ttl: 3600,
        });
      } catch (error) {
        this.logger.error('Cache warmup failed for key:', error, { key });
      }
    }
  }

  private async analyzeAccessPatterns(): Promise<void> {
    // 分析访问模式，识别热点数据
    // 这里可以实现更复杂的分析逻辑
  }

  private async adjustCacheSize(): Promise<void> {
    // 根据使用情况动态调整缓存大小
    // 这里可以实现自适应缓存大小调整
  }

  private async optimizeTTL(): Promise<void> {
    // 根据访问模式优化TTL设置
    // 这里可以实现智能TTL调整
  }

  private async cleanupIneffectiveCache(): Promise<void> {
    // 清理低效的缓存项
    // 这里可以实现基于访问频率的清理策略
  }

  private async updateHotspots(): Promise<void> {
    // 更新缓存热点统计
    const hotspots: CacheHotspot[] = [];

    for (const [key, item] of this.l1Cache) {
      if (item.accessCount > 10) { // 访问次数超过10次的认为是热点
        hotspots.push({
          key,
          accessCount: item.accessCount,
          lastAccessed: item.lastAccessed,
          averageResponseTime: 0, // 简化实现
          size: item.size,
          tags: item.tags,
        });
      }
    }

    this.hotspots = hotspots;
  }

  /**
   * 关闭缓存优化服务
   */
  async close(): Promise<void> {
    this.logger.info('Closing cache optimizer service...');

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
    }

    this.l1Cache.clear();
    this.stats.clear();
    this.hotspots = [];

    this.logger.info('Cache optimizer service closed');
  }
}

// 导出单例实例
export const cacheOptimizerService = new CacheOptimizerService();
