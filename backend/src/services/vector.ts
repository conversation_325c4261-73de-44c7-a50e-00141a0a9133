// ERP智能助手后端服务 - 向量数据库服务

import { ChromaApi, OpenAIEmbeddingFunction, Collection } from 'chromadb';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';
import { ExternalServiceError } from '@/middleware/error-handler';

// 向量文档接口
export interface VectorDocument {
  id: string;
  content: string;
  metadata: {
    type: 'operation' | 'documentation' | 'error' | 'guidance';
    source: string;
    timestamp: number;
    userId?: string;
    sessionId?: string;
    tags?: string[];
    [key: string]: any;
  };
  embedding?: number[];
}

// 搜索结果接口
export interface SearchResult {
  id: string;
  content: string;
  metadata: VectorDocument['metadata'];
  score: number;
  distance: number;
}

// 向量统计
interface VectorStats {
  totalDocuments: number;
  totalSearches: number;
  averageSearchTime: number;
  collectionStats: {
    [collectionName: string]: {
      documentCount: number;
      lastUpdated: number;
    };
  };
}

export class VectorService {
  private logger: Logger;
  private client: ChromaApi | null = null;
  private embeddingFunction: OpenAIEmbeddingFunction | null = null;
  private collections = new Map<string, Collection>();
  private stats: VectorStats;

  constructor() {
    this.logger = new Logger('VectorService');
    this.stats = {
      totalDocuments: 0,
      totalSearches: 0,
      averageSearchTime: 0,
      collectionStats: {},
    };
  }

  /**
   * 初始化向量数据库服务
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing vector database service...');

      // 创建ChromaDB客户端
      this.client = new ChromaApi({
        path: `http://${config.vector.host}:${config.vector.port}`,
      });

      // 创建嵌入函数
      if (config.ai.openai.apiKey) {
        this.embeddingFunction = new OpenAIEmbeddingFunction({
          openai_api_key: config.ai.openai.apiKey,
          openai_model: 'text-embedding-ada-002',
        });
      }

      // 测试连接
      await this.testConnection();

      // 初始化默认集合
      await this.initializeCollections();

      this.logger.info('Vector database service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize vector database service:', error);
      throw new ExternalServiceError('ChromaDB', 'Initialization failed', error);
    }
  }

  /**
   * 测试连接
   */
  private async testConnection(): Promise<void> {
    if (!this.client) {
      throw new ExternalServiceError('ChromaDB', 'Client not initialized');
    }

    try {
      const version = await this.client.version();
      this.logger.info('ChromaDB connection test successful', { version });
    } catch (error) {
      throw new ExternalServiceError('ChromaDB', 'Connection test failed', error);
    }
  }

  /**
   * 初始化集合
   */
  private async initializeCollections(): Promise<void> {
    const defaultCollections = [
      'operations', // 用户操作记录
      'documentation', // 文档和指南
      'errors', // 错误和问题
      'guidance', // 智能引导内容
    ];

    for (const collectionName of defaultCollections) {
      await this.getOrCreateCollection(collectionName);
    }
  }

  /**
   * 获取或创建集合
   */
  async getOrCreateCollection(name: string): Promise<Collection> {
    if (!this.client) {
      throw new ExternalServiceError('ChromaDB', 'Client not initialized');
    }

    try {
      // 检查缓存
      if (this.collections.has(name)) {
        return this.collections.get(name)!;
      }

      let collection: Collection;

      try {
        // 尝试获取现有集合
        collection = await this.client.getCollection({
          name,
          embeddingFunction: this.embeddingFunction,
        });
        this.logger.debug('Retrieved existing collection', { name });
      } catch (error) {
        // 集合不存在，创建新集合
        collection = await this.client.createCollection({
          name,
          embeddingFunction: this.embeddingFunction,
          metadata: {
            description: `ERP Assistant ${name} collection`,
            created_at: new Date().toISOString(),
          },
        });
        this.logger.info('Created new collection', { name });
      }

      // 缓存集合
      this.collections.set(name, collection);

      // 更新统计信息
      const count = await collection.count();
      this.stats.collectionStats[name] = {
        documentCount: count,
        lastUpdated: Date.now(),
      };

      return collection;
    } catch (error) {
      this.logger.error('Failed to get or create collection:', error, { name });
      throw new ExternalServiceError('ChromaDB', `Failed to get or create collection: ${name}`, error);
    }
  }

  /**
   * 添加文档
   */
  async addDocuments(
    collectionName: string,
    documents: VectorDocument[]
  ): Promise<void> {
    if (documents.length === 0) {
      return;
    }

    try {
      const collection = await this.getOrCreateCollection(collectionName);

      const ids = documents.map(doc => doc.id);
      const contents = documents.map(doc => doc.content);
      const metadatas = documents.map(doc => doc.metadata);
      const embeddings = documents.some(doc => doc.embedding) 
        ? documents.map(doc => doc.embedding || [])
        : undefined;

      await collection.add({
        ids,
        documents: contents,
        metadatas,
        embeddings,
      });

      // 更新统计信息
      this.stats.totalDocuments += documents.length;
      this.stats.collectionStats[collectionName] = {
        documentCount: await collection.count(),
        lastUpdated: Date.now(),
      };

      this.logger.info('Documents added to collection', {
        collectionName,
        count: documents.length,
      });
    } catch (error) {
      this.logger.error('Failed to add documents:', error, {
        collectionName,
        documentCount: documents.length,
      });
      throw new ExternalServiceError('ChromaDB', 'Failed to add documents', error);
    }
  }

  /**
   * 搜索文档
   */
  async searchDocuments(
    collectionName: string,
    query: string,
    options: {
      limit?: number;
      filter?: { [key: string]: any };
      includeMetadata?: boolean;
      includeDocuments?: boolean;
    } = {}
  ): Promise<SearchResult[]> {
    const startTime = Date.now();

    try {
      const collection = await this.getOrCreateCollection(collectionName);

      const {
        limit = 10,
        filter,
        includeMetadata = true,
        includeDocuments = true,
      } = options;

      const results = await collection.query({
        queryTexts: [query],
        nResults: limit,
        where: filter,
        include: [
          ...(includeMetadata ? ['metadatas'] : []),
          ...(includeDocuments ? ['documents'] : []),
          'distances',
        ],
      });

      const searchResults: SearchResult[] = [];

      if (results.ids && results.ids[0]) {
        for (let i = 0; i < results.ids[0].length; i++) {
          const id = results.ids[0][i];
          const content = results.documents?.[0]?.[i] || '';
          const metadata = results.metadatas?.[0]?.[i] || {};
          const distance = results.distances?.[0]?.[i] || 0;
          const score = 1 - distance; // 转换为相似度分数

          searchResults.push({
            id,
            content,
            metadata: metadata as VectorDocument['metadata'],
            score,
            distance,
          });
        }
      }

      // 更新统计信息
      const searchTime = Date.now() - startTime;
      this.stats.totalSearches++;
      this.stats.averageSearchTime = 
        (this.stats.averageSearchTime * (this.stats.totalSearches - 1) + searchTime) / 
        this.stats.totalSearches;

      this.logger.debug('Document search completed', {
        collectionName,
        query: query.substring(0, 100),
        resultCount: searchResults.length,
        searchTime,
      });

      return searchResults;
    } catch (error) {
      this.logger.error('Failed to search documents:', error, {
        collectionName,
        query: query.substring(0, 100),
      });
      throw new ExternalServiceError('ChromaDB', 'Failed to search documents', error);
    }
  }

  /**
   * 更新文档
   */
  async updateDocuments(
    collectionName: string,
    documents: VectorDocument[]
  ): Promise<void> {
    if (documents.length === 0) {
      return;
    }

    try {
      const collection = await this.getOrCreateCollection(collectionName);

      const ids = documents.map(doc => doc.id);
      const contents = documents.map(doc => doc.content);
      const metadatas = documents.map(doc => doc.metadata);
      const embeddings = documents.some(doc => doc.embedding) 
        ? documents.map(doc => doc.embedding || [])
        : undefined;

      await collection.update({
        ids,
        documents: contents,
        metadatas,
        embeddings,
      });

      // 更新统计信息
      this.stats.collectionStats[collectionName] = {
        documentCount: await collection.count(),
        lastUpdated: Date.now(),
      };

      this.logger.info('Documents updated in collection', {
        collectionName,
        count: documents.length,
      });
    } catch (error) {
      this.logger.error('Failed to update documents:', error, {
        collectionName,
        documentCount: documents.length,
      });
      throw new ExternalServiceError('ChromaDB', 'Failed to update documents', error);
    }
  }

  /**
   * 删除文档
   */
  async deleteDocuments(
    collectionName: string,
    ids: string[]
  ): Promise<void> {
    if (ids.length === 0) {
      return;
    }

    try {
      const collection = await this.getOrCreateCollection(collectionName);

      await collection.delete({
        ids,
      });

      // 更新统计信息
      this.stats.collectionStats[collectionName] = {
        documentCount: await collection.count(),
        lastUpdated: Date.now(),
      };

      this.logger.info('Documents deleted from collection', {
        collectionName,
        count: ids.length,
      });
    } catch (error) {
      this.logger.error('Failed to delete documents:', error, {
        collectionName,
        documentCount: ids.length,
      });
      throw new ExternalServiceError('ChromaDB', 'Failed to delete documents', error);
    }
  }

  /**
   * 获取文档
   */
  async getDocuments(
    collectionName: string,
    ids: string[]
  ): Promise<VectorDocument[]> {
    try {
      const collection = await this.getOrCreateCollection(collectionName);

      const results = await collection.get({
        ids,
        include: ['metadatas', 'documents', 'embeddings'],
      });

      const documents: VectorDocument[] = [];

      if (results.ids) {
        for (let i = 0; i < results.ids.length; i++) {
          const id = results.ids[i];
          const content = results.documents?.[i] || '';
          const metadata = results.metadatas?.[i] || {};
          const embedding = results.embeddings?.[i];

          documents.push({
            id,
            content,
            metadata: metadata as VectorDocument['metadata'],
            embedding,
          });
        }
      }

      return documents;
    } catch (error) {
      this.logger.error('Failed to get documents:', error, {
        collectionName,
        documentCount: ids.length,
      });
      throw new ExternalServiceError('ChromaDB', 'Failed to get documents', error);
    }
  }

  /**
   * 获取集合统计信息
   */
  async getCollectionStats(collectionName: string): Promise<{
    name: string;
    count: number;
    lastUpdated: number;
  }> {
    try {
      const collection = await this.getOrCreateCollection(collectionName);
      const count = await collection.count();

      return {
        name: collectionName,
        count,
        lastUpdated: this.stats.collectionStats[collectionName]?.lastUpdated || Date.now(),
      };
    } catch (error) {
      this.logger.error('Failed to get collection stats:', error, { collectionName });
      throw new ExternalServiceError('ChromaDB', 'Failed to get collection stats', error);
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): VectorStats {
    return { ...this.stats };
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      responseTime: number;
      collections: number;
      totalDocuments: number;
    };
  }> {
    const startTime = Date.now();

    try {
      if (!this.client) {
        return {
          status: 'unhealthy',
          details: {
            connected: false,
            responseTime: Date.now() - startTime,
            collections: 0,
            totalDocuments: 0,
          },
        };
      }

      await this.client.version();
      const collections = await this.client.listCollections();

      return {
        status: 'healthy',
        details: {
          connected: true,
          responseTime: Date.now() - startTime,
          collections: collections.length,
          totalDocuments: this.stats.totalDocuments,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          responseTime: Date.now() - startTime,
          collections: 0,
          totalDocuments: 0,
        },
      };
    }
  }

  /**
   * 关闭连接
   */
  async close(): Promise<void> {
    this.logger.info('Closing vector database connections...');
    this.collections.clear();
    this.client = null;
    this.embeddingFunction = null;
    this.logger.info('Vector database connections closed');
  }
}

// 导出单例实例
export const vectorService = new VectorService();
