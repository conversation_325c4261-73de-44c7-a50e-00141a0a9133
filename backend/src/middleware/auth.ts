// ERP智能助手后端服务 - 认证中间件

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '@/config';
import { logger } from '@/utils/logger';
import { AuthenticationError, AuthorizationError } from '@/middleware/error-handler';

// 用户接口
export interface User {
  id: string;
  email: string;
  username: string;
  role: UserRole;
  permissions: string[];
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 用户角色
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  VIEWER = 'viewer',
}

// JWT载荷接口
export interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  permissions: string[];
  iat: number;
  exp: number;
  type: 'access' | 'refresh';
}

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      user?: User;
      token?: string;
    }
  }
}

// JWT工具类
export class JWTService {
  // 生成访问令牌
  static generateAccessToken(user: User): string {
    const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
      userId: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      type: 'access',
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
      issuer: 'erp-assistant',
      audience: 'erp-assistant-client',
    });
  }

  // 生成刷新令牌
  static generateRefreshToken(user: User): string {
    const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
      userId: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      type: 'refresh',
    };

    return jwt.sign(payload, config.jwt.refreshSecret, {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: 'erp-assistant',
      audience: 'erp-assistant-client',
    });
  }

  // 验证访问令牌
  static verifyAccessToken(token: string): JWTPayload {
    try {
      const payload = jwt.verify(token, config.jwt.secret, {
        issuer: 'erp-assistant',
        audience: 'erp-assistant-client',
      }) as JWTPayload;

      if (payload.type !== 'access') {
        throw new AuthenticationError('Invalid token type');
      }

      return payload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthenticationError('Token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new AuthenticationError('Invalid token');
      }
      throw error;
    }
  }

  // 验证刷新令牌
  static verifyRefreshToken(token: string): JWTPayload {
    try {
      const payload = jwt.verify(token, config.jwt.refreshSecret, {
        issuer: 'erp-assistant',
        audience: 'erp-assistant-client',
      }) as JWTPayload;

      if (payload.type !== 'refresh') {
        throw new AuthenticationError('Invalid token type');
      }

      return payload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthenticationError('Refresh token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new AuthenticationError('Invalid refresh token');
      }
      throw error;
    }
  }

  // 解码令牌（不验证）
  static decodeToken(token: string): JWTPayload | null {
    try {
      return jwt.decode(token) as JWTPayload;
    } catch {
      return null;
    }
  }
}

// 从请求中提取令牌
function extractToken(req: Request): string | null {
  // 从Authorization头部提取
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // 从查询参数提取（用于WebSocket等场景）
  if (req.query.token && typeof req.query.token === 'string') {
    return req.query.token;
  }

  // 从Cookie提取
  if (req.cookies && req.cookies.accessToken) {
    return req.cookies.accessToken;
  }

  return null;
}

// 认证中间件
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      throw new AuthenticationError('No token provided');
    }

    // 验证令牌
    const payload = JWTService.verifyAccessToken(token);
    
    // 这里应该从数据库获取用户信息
    // 暂时使用payload中的信息创建用户对象
    const user: User = {
      id: payload.userId,
      email: payload.email,
      username: payload.email.split('@')[0],
      role: payload.role,
      permissions: payload.permissions,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // 检查用户是否激活
    if (!user.isActive) {
      throw new AuthenticationError('Account is deactivated');
    }

    // 将用户信息添加到请求对象
    req.user = user;
    req.token = token;

    // 记录认证日志
    logger.debug('User authenticated', {
      userId: user.id,
      email: user.email,
      role: user.role,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    next();
  } catch (error) {
    next(error);
  }
};

// 可选认证中间件（不强制要求认证）
export const optionalAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractToken(req);
    
    if (token) {
      const payload = JWTService.verifyAccessToken(token);
      
      const user: User = {
        id: payload.userId,
        email: payload.email,
        username: payload.email.split('@')[0],
        role: payload.role,
        permissions: payload.permissions,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      if (user.isActive) {
        req.user = user;
        req.token = token;
      }
    }

    next();
  } catch (error) {
    // 可选认证失败时不抛出错误，继续处理请求
    logger.debug('Optional authentication failed', { error: error.message });
    next();
  }
};

// 角色检查中间件
export const requireRole = (...roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    if (!roles.includes(req.user.role)) {
      throw new AuthorizationError(`Required role: ${roles.join(' or ')}`);
    }

    next();
  };
};

// 权限检查中间件
export const requirePermission = (...permissions: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    const hasPermission = permissions.some(permission => 
      req.user!.permissions.includes(permission)
    );

    if (!hasPermission) {
      throw new AuthorizationError(`Required permission: ${permissions.join(' or ')}`);
    }

    next();
  };
};

// 资源所有者检查中间件
export const requireOwnership = (resourceIdParam: string = 'id') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    const resourceId = req.params[resourceIdParam];
    const userId = req.user.id;

    // 管理员可以访问所有资源
    if (req.user.role === UserRole.ADMIN) {
      return next();
    }

    // 检查资源所有权（这里需要根据实际业务逻辑实现）
    // 暂时简单比较ID
    if (resourceId !== userId) {
      throw new AuthorizationError('Access denied: not resource owner');
    }

    next();
  };
};

// API密钥认证中间件
export const apiKeyMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const apiKey = req.headers['x-api-key'] as string;
  
  if (!apiKey) {
    throw new AuthenticationError('API key required');
  }

  // 这里应该验证API密钥
  // 暂时跳过验证
  logger.debug('API key authentication', { apiKey: apiKey.substring(0, 8) + '...' });
  
  next();
};

// 认证统计
export class AuthStats {
  private static stats = {
    totalAttempts: 0,
    successfulAttempts: 0,
    failedAttempts: 0,
    tokenRefreshes: 0,
    byRole: new Map<UserRole, number>(),
  };

  static recordAttempt(success: boolean, role?: UserRole) {
    this.stats.totalAttempts++;
    if (success) {
      this.stats.successfulAttempts++;
      if (role) {
        const current = this.stats.byRole.get(role) || 0;
        this.stats.byRole.set(role, current + 1);
      }
    } else {
      this.stats.failedAttempts++;
    }
  }

  static recordTokenRefresh() {
    this.stats.tokenRefreshes++;
  }

  static getStats() {
    return {
      ...this.stats,
      byRole: Object.fromEntries(this.stats.byRole),
      successRate: this.stats.totalAttempts > 0 
        ? (this.stats.successfulAttempts / this.stats.totalAttempts) * 100 
        : 0,
    };
  }

  static reset() {
    this.stats.totalAttempts = 0;
    this.stats.successfulAttempts = 0;
    this.stats.failedAttempts = 0;
    this.stats.tokenRefreshes = 0;
    this.stats.byRole.clear();
  }
}
