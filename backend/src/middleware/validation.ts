// ERP智能助手后端服务 - 验证中间件

import { Request, Response, NextFunction } from 'express';
import Jo<PERSON> from 'joi';
import { ValidationError } from '@/middleware/error-handler';
import { logger } from '@/utils/logger';

// 验证选项
interface ValidationOptions {
  body?: Joi.ObjectSchema;
  params?: Joi.ObjectSchema;
  query?: Joi.ObjectSchema;
  headers?: Joi.ObjectSchema;
  allowUnknown?: boolean;
  stripUnknown?: boolean;
  abortEarly?: boolean;
}

// 创建验证中间件
export const validateRequest = (options: ValidationOptions) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const errors: string[] = [];

    try {
      // 验证请求体
      if (options.body) {
        const { error, value } = options.body.validate(req.body, {
          allowUnknown: options.allowUnknown || false,
          stripUnknown: options.stripUnknown || true,
          abortEarly: options.abortEarly || false,
        });

        if (error) {
          errors.push(...error.details.map(detail => detail.message));
        } else {
          req.body = value;
        }
      }

      // 验证路径参数
      if (options.params) {
        const { error, value } = options.params.validate(req.params, {
          allowUnknown: options.allowUnknown || false,
          stripUnknown: options.stripUnknown || true,
          abortEarly: options.abortEarly || false,
        });

        if (error) {
          errors.push(...error.details.map(detail => detail.message));
        } else {
          req.params = value;
        }
      }

      // 验证查询参数
      if (options.query) {
        const { error, value } = options.query.validate(req.query, {
          allowUnknown: options.allowUnknown || false,
          stripUnknown: options.stripUnknown || true,
          abortEarly: options.abortEarly || false,
        });

        if (error) {
          errors.push(...error.details.map(detail => detail.message));
        } else {
          req.query = value;
        }
      }

      // 验证请求头
      if (options.headers) {
        const { error, value } = options.headers.validate(req.headers, {
          allowUnknown: true, // 头部通常包含很多未知字段
          stripUnknown: false,
          abortEarly: options.abortEarly || false,
        });

        if (error) {
          errors.push(...error.details.map(detail => detail.message));
        }
      }

      // 如果有验证错误，抛出异常
      if (errors.length > 0) {
        logger.debug('Validation failed', {
          errors,
          path: req.path,
          method: req.method,
          body: req.body,
          params: req.params,
          query: req.query,
        });

        throw new ValidationError('Validation failed', errors);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// 常用验证模式
export const commonSchemas = {
  // ID验证
  id: Joi.string().uuid().required(),
  optionalId: Joi.string().uuid().optional(),

  // 分页验证
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sort: Joi.string().optional(),
    order: Joi.string().valid('asc', 'desc').default('desc'),
  }),

  // 搜索验证
  search: Joi.object({
    q: Joi.string().min(1).max(100).optional(),
    filters: Joi.object().optional(),
  }),

  // 用户相关验证
  email: Joi.string().email().required(),
  password: Joi.string().min(8).max(128).pattern(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
  ).required().messages({
    'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character',
  }),
  username: Joi.string().alphanum().min(3).max(30).required(),

  // 时间验证
  dateRange: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
  }),

  // 文件上传验证
  file: Joi.object({
    fieldname: Joi.string().required(),
    originalname: Joi.string().required(),
    encoding: Joi.string().required(),
    mimetype: Joi.string().required(),
    size: Joi.number().max(10 * 1024 * 1024).required(), // 10MB
  }),
};

// 预定义验证中间件
export const validatePagination = validateRequest({
  query: commonSchemas.pagination,
});

export const validateSearch = validateRequest({
  query: commonSchemas.search,
});

export const validateId = validateRequest({
  params: Joi.object({
    id: commonSchemas.id,
  }),
});

// 用户注册验证
export const validateUserRegistration = validateRequest({
  body: Joi.object({
    email: commonSchemas.email,
    password: commonSchemas.password,
    username: commonSchemas.username,
    firstName: Joi.string().min(1).max(50).required(),
    lastName: Joi.string().min(1).max(50).required(),
    role: Joi.string().valid('admin', 'user', 'viewer').default('user'),
  }),
});

// 用户登录验证
export const validateUserLogin = validateRequest({
  body: Joi.object({
    email: commonSchemas.email,
    password: Joi.string().required(),
    rememberMe: Joi.boolean().default(false),
  }),
});

// 密码重置验证
export const validatePasswordReset = validateRequest({
  body: Joi.object({
    email: commonSchemas.email,
  }),
});

// 密码更新验证
export const validatePasswordUpdate = validateRequest({
  body: Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: commonSchemas.password,
    confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required().messages({
      'any.only': 'Passwords do not match',
    }),
  }),
});

// AI请求验证
export const validateAIRequest = validateRequest({
  body: Joi.object({
    prompt: Joi.string().min(1).max(4000).required(),
    model: Joi.string().valid('gpt-4', 'gpt-3.5-turbo', 'claude-3-sonnet').optional(),
    maxTokens: Joi.number().integer().min(1).max(4000).optional(),
    temperature: Joi.number().min(0).max(2).optional(),
    context: Joi.object().optional(),
  }),
});

// 录制会话验证
export const validateRecordingSession = validateRequest({
  body: Joi.object({
    title: Joi.string().min(1).max(200).required(),
    description: Joi.string().max(1000).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional(),
    isPublic: Joi.boolean().default(false),
  }),
});

// 操作记录验证
export const validateOperationRecord = validateRequest({
  body: Joi.object({
    sessionId: commonSchemas.id,
    type: Joi.string().valid('dom_event', 'navigation', 'screenshot').required(),
    timestamp: Joi.date().iso().required(),
    data: Joi.object().required(),
    metadata: Joi.object().optional(),
  }),
});

// 文件上传验证
export const validateFileUpload = (allowedTypes: string[] = [], maxSize: number = 10 * 1024 * 1024) => {
  return validateRequest({
    body: Joi.object({
      file: Joi.object({
        fieldname: Joi.string().required(),
        originalname: Joi.string().required(),
        encoding: Joi.string().required(),
        mimetype: Joi.string().valid(...allowedTypes).required(),
        size: Joi.number().max(maxSize).required(),
      }).required(),
    }),
  });
};

// 批量操作验证
export const validateBatchOperation = validateRequest({
  body: Joi.object({
    ids: Joi.array().items(commonSchemas.id).min(1).max(100).required(),
    operation: Joi.string().required(),
    params: Joi.object().optional(),
  }),
});

// 导出请求验证
export const validateExportRequest = validateRequest({
  body: Joi.object({
    format: Joi.string().valid('json', 'csv', 'xlsx', 'pdf').required(),
    filters: Joi.object().optional(),
    fields: Joi.array().items(Joi.string()).optional(),
    dateRange: commonSchemas.dateRange.optional(),
  }),
});

// 自定义验证器
export class CustomValidators {
  // 验证Chrome扩展ID
  static chromeExtensionId = Joi.string().pattern(/^[a-z]{32}$/).messages({
    'string.pattern.base': 'Invalid Chrome extension ID format',
  });

  // 验证URL
  static url = Joi.string().uri().messages({
    'string.uri': 'Invalid URL format',
  });

  // 验证颜色代码
  static colorCode = Joi.string().pattern(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/).messages({
    'string.pattern.base': 'Invalid color code format',
  });

  // 验证JSON字符串
  static jsonString = Joi.string().custom((value, helpers) => {
    try {
      JSON.parse(value);
      return value;
    } catch {
      return helpers.error('any.invalid');
    }
  }).messages({
    'any.invalid': 'Invalid JSON string',
  });

  // 验证Base64字符串
  static base64 = Joi.string().base64().messages({
    'string.base64': 'Invalid Base64 string',
  });
}

// 验证统计
export class ValidationStats {
  private static stats = {
    totalValidations: 0,
    failedValidations: 0,
    byEndpoint: new Map<string, { total: number; failed: number }>(),
  };

  static recordValidation(endpoint: string, failed: boolean = false) {
    this.stats.totalValidations++;
    if (failed) {
      this.stats.failedValidations++;
    }

    const endpointStats = this.stats.byEndpoint.get(endpoint) || { total: 0, failed: 0 };
    endpointStats.total++;
    if (failed) {
      endpointStats.failed++;
    }
    this.stats.byEndpoint.set(endpoint, endpointStats);
  }

  static getStats() {
    return {
      ...this.stats,
      byEndpoint: Object.fromEntries(this.stats.byEndpoint),
      failureRate: this.stats.totalValidations > 0 
        ? (this.stats.failedValidations / this.stats.totalValidations) * 100 
        : 0,
    };
  }

  static reset() {
    this.stats.totalValidations = 0;
    this.stats.failedValidations = 0;
    this.stats.byEndpoint.clear();
  }
}
