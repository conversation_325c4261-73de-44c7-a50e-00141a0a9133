// ERP智能助手后端服务 - 速率限制中间件

import { Request, Response, NextFunction } from 'express';
import { RateLimiterRedis, RateLimiterMemory } from 'rate-limiter-flexible';
import { config } from '@/config';
import { logger } from '@/utils/logger';
import { RateLimitError } from '@/middleware/error-handler';

// Redis客户端（稍后实现）
let redisClient: any = null;

// 速率限制器配置
interface RateLimiterConfig {
  keyPrefix: string;
  points: number; // 允许的请求数
  duration: number; // 时间窗口（秒）
  blockDuration?: number; // 阻塞时间（秒）
  execEvenly?: boolean; // 是否均匀分布
}

// 创建速率限制器
function createRateLimiter(config: RateLimiterConfig) {
  const options = {
    storeClient: redisClient,
    keyPrefix: config.keyPrefix,
    points: config.points,
    duration: config.duration,
    blockDuration: config.blockDuration || config.duration,
    execEvenly: config.execEvenly || false,
  };

  // 如果有Redis客户端，使用Redis存储，否则使用内存存储
  return redisClient 
    ? new RateLimiterRedis(options)
    : new RateLimiterMemory(options);
}

// 不同类型的速率限制器
const rateLimiters = {
  // 全局速率限制
  global: createRateLimiter({
    keyPrefix: 'global',
    points: config.security.rateLimitMaxRequests,
    duration: Math.floor(config.security.rateLimitWindowMs / 1000),
  }),

  // 认证相关的严格限制
  auth: createRateLimiter({
    keyPrefix: 'auth',
    points: 5, // 5次尝试
    duration: 900, // 15分钟
    blockDuration: 1800, // 阻塞30分钟
  }),

  // API调用限制
  api: createRateLimiter({
    keyPrefix: 'api',
    points: 1000, // 1000次请求
    duration: 3600, // 1小时
  }),

  // AI服务调用限制
  ai: createRateLimiter({
    keyPrefix: 'ai',
    points: 100, // 100次AI调用
    duration: 3600, // 1小时
    execEvenly: true, // 均匀分布
  }),

  // 文件上传限制
  upload: createRateLimiter({
    keyPrefix: 'upload',
    points: 10, // 10次上传
    duration: 3600, // 1小时
  }),

  // 数据导出限制
  export: createRateLimiter({
    keyPrefix: 'export',
    points: 5, // 5次导出
    duration: 3600, // 1小时
  }),
};

// 获取客户端标识符
function getClientId(req: Request): string {
  // 优先使用用户ID
  if ((req as any).user?.id) {
    return `user:${(req as any).user.id}`;
  }
  
  // 使用IP地址
  return `ip:${req.ip}`;
}

// 创建速率限制中间件
export function createRateLimitMiddleware(
  limiterType: keyof typeof rateLimiters,
  options: {
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
    keyGenerator?: (req: Request) => string;
    onLimitReached?: (req: Request, res: Response) => void;
  } = {}
) {
  const limiter = rateLimiters[limiterType];
  
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // 生成限制键
      const key = options.keyGenerator ? options.keyGenerator(req) : getClientId(req);
      
      // 检查速率限制
      const result = await limiter.consume(key);
      
      // 添加速率限制头部
      res.set({
        'X-RateLimit-Limit': limiter.points.toString(),
        'X-RateLimit-Remaining': result.remainingPoints?.toString() || '0',
        'X-RateLimit-Reset': new Date(Date.now() + result.msBeforeNext).toISOString(),
      });
      
      // 记录速率限制信息
      logger.debug('Rate limit check passed', {
        limiterType,
        key,
        remaining: result.remainingPoints,
        resetTime: result.msBeforeNext,
      });
      
      next();
      
    } catch (rejRes: any) {
      // 速率限制被触发
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      
      // 添加速率限制头部
      res.set({
        'X-RateLimit-Limit': limiter.points.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': new Date(Date.now() + rejRes.msBeforeNext).toISOString(),
        'Retry-After': secs.toString(),
      });
      
      // 记录速率限制触发
      logger.warn('Rate limit exceeded', {
        limiterType,
        key: getClientId(req),
        retryAfter: secs,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
      });
      
      // 调用自定义处理函数
      if (options.onLimitReached) {
        options.onLimitReached(req, res);
      }
      
      // 抛出速率限制错误
      throw new RateLimitError(`Too many requests. Try again in ${secs} seconds.`);
    }
  };
}

// 预定义的中间件
export const rateLimiter = createRateLimitMiddleware('global', {
  skipSuccessfulRequests: config.security.rateLimitSkipSuccessful,
});

export const authRateLimiter = createRateLimitMiddleware('auth', {
  keyGenerator: (req) => `${getClientId(req)}:${req.path}`,
  onLimitReached: (req, res) => {
    logger.warn('Authentication rate limit exceeded', {
      ip: req.ip,
      path: req.path,
      userAgent: req.get('User-Agent'),
    });
  },
});

export const apiRateLimiter = createRateLimitMiddleware('api');

export const aiRateLimiter = createRateLimitMiddleware('ai', {
  keyGenerator: (req) => {
    const userId = (req as any).user?.id;
    return userId ? `user:${userId}` : `ip:${req.ip}`;
  },
  onLimitReached: (req, res) => {
    logger.warn('AI service rate limit exceeded', {
      userId: (req as any).user?.id,
      ip: req.ip,
      path: req.path,
    });
  },
});

export const uploadRateLimiter = createRateLimitMiddleware('upload');

export const exportRateLimiter = createRateLimitMiddleware('export');

// 动态速率限制
export class DynamicRateLimiter {
  private limiters = new Map<string, any>();

  createLimiter(key: string, config: RateLimiterConfig) {
    const limiter = createRateLimiter(config);
    this.limiters.set(key, limiter);
    return limiter;
  }

  getLimiter(key: string) {
    return this.limiters.get(key);
  }

  removeLimiter(key: string) {
    this.limiters.delete(key);
  }

  middleware(limiterKey: string) {
    return async (req: Request, res: Response, next: NextFunction) => {
      const limiter = this.getLimiter(limiterKey);
      if (!limiter) {
        return next();
      }

      try {
        const key = getClientId(req);
        await limiter.consume(key);
        next();
      } catch (rejRes: any) {
        const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
        res.set('Retry-After', secs.toString());
        throw new RateLimitError(`Too many requests. Try again in ${secs} seconds.`);
      }
    };
  }
}

// 速率限制统计
export class RateLimitStats {
  private static stats = {
    totalRequests: 0,
    limitedRequests: 0,
    byLimiter: new Map<string, { total: number; limited: number }>(),
  };

  static recordRequest(limiterType: string, limited: boolean = false) {
    this.stats.totalRequests++;
    if (limited) {
      this.stats.limitedRequests++;
    }

    const limiterStats = this.stats.byLimiter.get(limiterType) || { total: 0, limited: 0 };
    limiterStats.total++;
    if (limited) {
      limiterStats.limited++;
    }
    this.stats.byLimiter.set(limiterType, limiterStats);
  }

  static getStats() {
    return {
      ...this.stats,
      byLimiter: Object.fromEntries(this.stats.byLimiter),
      limitRate: this.stats.totalRequests > 0 
        ? (this.stats.limitedRequests / this.stats.totalRequests) * 100 
        : 0,
    };
  }

  static reset() {
    this.stats.totalRequests = 0;
    this.stats.limitedRequests = 0;
    this.stats.byLimiter.clear();
  }
}

// 设置Redis客户端
export function setRedisClient(client: any) {
  redisClient = client;
  logger.info('Redis client set for rate limiting');
}

// 清理速率限制记录
export async function clearRateLimitRecords(pattern: string = '*') {
  if (!redisClient) {
    logger.warn('No Redis client available for clearing rate limit records');
    return;
  }

  try {
    const keys = await redisClient.keys(`rl:${pattern}`);
    if (keys.length > 0) {
      await redisClient.del(keys);
      logger.info(`Cleared ${keys.length} rate limit records`);
    }
  } catch (error) {
    logger.error('Failed to clear rate limit records', error);
  }
}
