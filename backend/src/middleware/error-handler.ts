// ERP智能助手后端服务 - 错误处理中间件

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';
import { config } from '@/config';

// 自定义错误类
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    code?: string,
    details?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;
    this.details = details;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 验证错误
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, true, 'VALIDATION_ERROR', details);
  }
}

// 认证错误
export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401, true, 'AUTHENTICATION_ERROR');
  }
}

// 授权错误
export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403, true, 'AUTHORIZATION_ERROR');
  }
}

// 资源未找到错误
export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, true, 'NOT_FOUND_ERROR');
  }
}

// 冲突错误
export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, true, 'CONFLICT_ERROR');
  }
}

// 速率限制错误
export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, true, 'RATE_LIMIT_ERROR');
  }
}

// 数据库错误
export class DatabaseError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 500, true, 'DATABASE_ERROR', details);
  }
}

// 外部服务错误
export class ExternalServiceError extends AppError {
  constructor(service: string, message: string, details?: any) {
    super(`${service} service error: ${message}`, 502, true, 'EXTERNAL_SERVICE_ERROR', details);
  }
}

// 错误响应接口
interface ErrorResponse {
  error: {
    message: string;
    code?: string;
    statusCode: number;
    details?: any;
    timestamp: string;
    path: string;
    method: string;
    requestId?: string;
    stack?: string;
  };
}

// 错误处理中间件
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = 500;
  let message = 'Internal Server Error';
  let code = 'INTERNAL_ERROR';
  let details: any = undefined;

  // 处理自定义错误
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    code = error.code || 'APP_ERROR';
    details = error.details;
  }
  // 处理Joi验证错误
  else if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation failed';
    code = 'VALIDATION_ERROR';
    details = (error as any).details;
  }
  // 处理JWT错误
  else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
    code = 'INVALID_TOKEN';
  }
  else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
    code = 'TOKEN_EXPIRED';
  }
  // 处理数据库错误
  else if (error.name === 'SequelizeValidationError') {
    statusCode = 400;
    message = 'Database validation failed';
    code = 'DATABASE_VALIDATION_ERROR';
    details = (error as any).errors;
  }
  else if (error.name === 'SequelizeUniqueConstraintError') {
    statusCode = 409;
    message = 'Resource already exists';
    code = 'DUPLICATE_RESOURCE';
    details = (error as any).errors;
  }
  // 处理Multer错误（文件上传）
  else if (error.name === 'MulterError') {
    statusCode = 400;
    message = 'File upload error';
    code = 'FILE_UPLOAD_ERROR';
    details = { type: (error as any).code };
  }

  // 构建错误响应
  const errorResponse: ErrorResponse = {
    error: {
      message,
      code,
      statusCode,
      timestamp: new Date().toISOString(),
      path: req.originalUrl,
      method: req.method,
      requestId: req.headers['x-request-id'] as string,
    }
  };

  // 添加详细信息（如果有）
  if (details) {
    errorResponse.error.details = details;
  }

  // 在开发环境中添加堆栈信息
  if (config.isDevelopment && error.stack) {
    errorResponse.error.stack = error.stack;
  }

  // 记录错误日志
  const logContext = {
    statusCode,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
    requestId: req.headers['x-request-id'] as string,
  };

  if (statusCode >= 500) {
    logger.error(message, error, logContext);
  } else {
    logger.warn(message, logContext);
  }

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
};

// 异步错误处理包装器
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404处理中间件
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.originalUrl}`);
  next(error);
};

// 错误工厂函数
export const createError = {
  validation: (message: string, details?: any) => new ValidationError(message, details),
  authentication: (message?: string) => new AuthenticationError(message),
  authorization: (message?: string) => new AuthorizationError(message),
  notFound: (resource?: string) => new NotFoundError(resource),
  conflict: (message: string) => new ConflictError(message),
  rateLimit: (message?: string) => new RateLimitError(message),
  database: (message: string, details?: any) => new DatabaseError(message, details),
  externalService: (service: string, message: string, details?: any) => 
    new ExternalServiceError(service, message, details),
  internal: (message: string, details?: any) => new AppError(message, 500, true, 'INTERNAL_ERROR', details),
};

// 错误类型检查
export const isOperationalError = (error: Error): boolean => {
  if (error instanceof AppError) {
    return error.isOperational;
  }
  return false;
};

// 错误统计
export class ErrorStats {
  private static stats = new Map<string, number>();

  static increment(code: string): void {
    const current = this.stats.get(code) || 0;
    this.stats.set(code, current + 1);
  }

  static getStats(): { [key: string]: number } {
    return Object.fromEntries(this.stats);
  }

  static reset(): void {
    this.stats.clear();
  }
}

// 错误监控中间件
export const errorMonitoring = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // 统计错误
  if (error instanceof AppError && error.code) {
    ErrorStats.increment(error.code);
  } else {
    ErrorStats.increment('UNKNOWN_ERROR');
  }

  // 继续错误处理
  next(error);
};
