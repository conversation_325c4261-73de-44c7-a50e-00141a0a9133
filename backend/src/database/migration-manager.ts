// ERP智能助手后端服务 - 数据库迁移管理器

import knex, { Knex } from 'knex';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';

export class MigrationManager {
  private logger: Logger;
  private knexInstance: Knex;

  constructor() {
    this.logger = new Logger('MigrationManager');
    
    // 创建Knex实例
    this.knexInstance = knex({
      client: 'postgresql',
      connection: {
        host: config.database.host,
        port: config.database.port,
        database: config.database.database,
        user: config.database.user,
        password: config.database.password,
        ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
      },
      pool: {
        min: config.database.pool.min,
        max: config.database.pool.max,
      },
      migrations: {
        directory: './src/database/migrations',
        tableName: 'knex_migrations',
        extension: 'ts',
        loadExtensions: ['.ts'],
      },
      seeds: {
        directory: './src/database/seeds',
        extension: 'ts',
        loadExtensions: ['.ts'],
      },
    });
  }

  /**
   * 运行所有待执行的迁移
   */
  async runMigrations(): Promise<void> {
    try {
      this.logger.info('Starting database migrations...');
      
      const [batchNo, migrations] = await this.knexInstance.migrate.latest();
      
      if (migrations.length === 0) {
        this.logger.info('Database is already up to date');
      } else {
        this.logger.info(`Ran ${migrations.length} migrations in batch ${batchNo}:`, {
          migrations: migrations.map(m => m.split('/').pop()),
        });
      }
    } catch (error) {
      this.logger.error('Migration failed:', error);
      throw error;
    }
  }

  /**
   * 回滚最后一批迁移
   */
  async rollbackMigrations(): Promise<void> {
    try {
      this.logger.info('Rolling back last batch of migrations...');
      
      const [batchNo, migrations] = await this.knexInstance.migrate.rollback();
      
      if (migrations.length === 0) {
        this.logger.info('No migrations to rollback');
      } else {
        this.logger.info(`Rolled back ${migrations.length} migrations from batch ${batchNo}:`, {
          migrations: migrations.map(m => m.split('/').pop()),
        });
      }
    } catch (error) {
      this.logger.error('Migration rollback failed:', error);
      throw error;
    }
  }

  /**
   * 获取迁移状态
   */
  async getMigrationStatus(): Promise<{
    currentVersion: string;
    pendingMigrations: string[];
    completedMigrations: Array<{ name: string; batch: number; migration_time: Date }>;
  }> {
    try {
      // 获取已完成的迁移
      const completedMigrations = await this.knexInstance('knex_migrations')
        .select('*')
        .orderBy('id');

      // 获取所有迁移文件
      const allMigrations = await this.knexInstance.migrate.list();
      const [completed, pending] = allMigrations;

      const currentVersion = completed.length > 0 
        ? completed[completed.length - 1].split('/').pop()?.replace('.ts', '') || 'none'
        : 'none';

      return {
        currentVersion,
        pendingMigrations: pending.map(m => m.split('/').pop()?.replace('.ts', '') || m),
        completedMigrations: completedMigrations.map(m => ({
          name: m.name,
          batch: m.batch,
          migration_time: m.migration_time,
        })),
      };
    } catch (error) {
      this.logger.error('Failed to get migration status:', error);
      throw error;
    }
  }

  /**
   * 创建新的迁移文件
   */
  async createMigration(name: string): Promise<string> {
    try {
      const migrationPath = await this.knexInstance.migrate.make(name, {
        extension: 'ts',
      });
      
      this.logger.info('Created new migration:', { path: migrationPath });
      return migrationPath;
    } catch (error) {
      this.logger.error('Failed to create migration:', error);
      throw error;
    }
  }

  /**
   * 运行数据种子
   */
  async runSeeds(): Promise<void> {
    try {
      this.logger.info('Running database seeds...');
      
      const seeds = await this.knexInstance.seed.run();
      
      this.logger.info(`Ran ${seeds[0].length} seed files:`, {
        seeds: seeds[0].map(s => s.split('/').pop()),
      });
    } catch (error) {
      this.logger.error('Seed execution failed:', error);
      throw error;
    }
  }

  /**
   * 验证数据库连接
   */
  async validateConnection(): Promise<boolean> {
    try {
      await this.knexInstance.raw('SELECT 1');
      this.logger.info('Database connection validated successfully');
      return true;
    } catch (error) {
      this.logger.error('Database connection validation failed:', error);
      return false;
    }
  }

  /**
   * 获取数据库信息
   */
  async getDatabaseInfo(): Promise<{
    version: string;
    size: string;
    tables: Array<{ name: string; rows: number; size: string }>;
  }> {
    try {
      // 获取PostgreSQL版本
      const versionResult = await this.knexInstance.raw('SELECT version()');
      const version = versionResult.rows[0].version.split(' ')[1];

      // 获取数据库大小
      const sizeResult = await this.knexInstance.raw(`
        SELECT pg_size_pretty(pg_database_size(current_database())) as size
      `);
      const size = sizeResult.rows[0].size;

      // 获取表信息
      const tablesResult = await this.knexInstance.raw(`
        SELECT 
          schemaname,
          tablename,
          attname,
          n_distinct,
          correlation
        FROM pg_stats 
        WHERE schemaname = 'public'
        ORDER BY tablename
      `);

      // 获取表大小和行数
      const tableStatsResult = await this.knexInstance.raw(`
        SELECT 
          schemaname,
          tablename,
          attname,
          n_distinct,
          correlation,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
          (SELECT reltuples::bigint FROM pg_class WHERE relname = tablename) as rows
        FROM pg_stats 
        WHERE schemaname = 'public'
        GROUP BY schemaname, tablename, attname, n_distinct, correlation
        ORDER BY tablename
      `);

      const tables = Array.from(
        new Map(
          tableStatsResult.rows.map((row: any) => [
            row.tablename,
            {
              name: row.tablename,
              rows: parseInt(row.rows) || 0,
              size: row.size || '0 bytes',
            },
          ])
        ).values()
      );

      return { version, size, tables };
    } catch (error) {
      this.logger.error('Failed to get database info:', error);
      throw error;
    }
  }

  /**
   * 检查表是否存在
   */
  async tableExists(tableName: string): Promise<boolean> {
    try {
      const exists = await this.knexInstance.schema.hasTable(tableName);
      return exists;
    } catch (error) {
      this.logger.error(`Failed to check if table ${tableName} exists:`, error);
      return false;
    }
  }

  /**
   * 获取表结构
   */
  async getTableSchema(tableName: string): Promise<any[]> {
    try {
      const columns = await this.knexInstance(tableName).columnInfo();
      return Object.entries(columns).map(([name, info]) => ({
        name,
        ...info,
      }));
    } catch (error) {
      this.logger.error(`Failed to get schema for table ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * 执行健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      migrationsUpToDate: boolean;
      tablesCount: number;
      responseTime: number;
    };
  }> {
    const startTime = Date.now();
    
    try {
      // 检查连接
      const connected = await this.validateConnection();
      if (!connected) {
        return {
          status: 'unhealthy',
          details: {
            connected: false,
            migrationsUpToDate: false,
            tablesCount: 0,
            responseTime: Date.now() - startTime,
          },
        };
      }

      // 检查迁移状态
      const migrationStatus = await this.getMigrationStatus();
      const migrationsUpToDate = migrationStatus.pendingMigrations.length === 0;

      // 获取表数量
      const tablesResult = await this.knexInstance.raw(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
      const tablesCount = parseInt(tablesResult.rows[0].count);

      const status = connected && migrationsUpToDate ? 'healthy' : 'unhealthy';

      return {
        status,
        details: {
          connected,
          migrationsUpToDate,
          tablesCount,
          responseTime: Date.now() - startTime,
        },
      };
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          migrationsUpToDate: false,
          tablesCount: 0,
          responseTime: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    try {
      await this.knexInstance.destroy();
      this.logger.info('Database connection closed');
    } catch (error) {
      this.logger.error('Failed to close database connection:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const migrationManager = new MigrationManager();
