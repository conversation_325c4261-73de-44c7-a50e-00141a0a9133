// ERP智能助手后端服务 - 数据库备份管理器

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import cron from 'node-cron';
import { config } from '@/config';
import { logger, Logger } from '@/utils/logger';

export interface BackupConfig {
  enabled: boolean;
  schedule: string; // cron表达式
  retentionDays: number;
  backupPath: string;
  compression: boolean;
  includeData: boolean;
  includeSchema: boolean;
  excludeTables?: string[];
}

export interface BackupInfo {
  id: string;
  filename: string;
  filepath: string;
  size: number;
  createdAt: Date;
  type: 'full' | 'schema' | 'data';
  compressed: boolean;
  checksum?: string;
}

export class BackupManager {
  private logger: Logger;
  private config: BackupConfig;
  private cronJob?: cron.ScheduledTask;
  private backups: BackupInfo[] = [];

  constructor(backupConfig?: Partial<BackupConfig>) {
    this.logger = new Logger('BackupManager');
    
    this.config = {
      enabled: true,
      schedule: '0 2 * * *', // 每天凌晨2点
      retentionDays: 30,
      backupPath: './backups',
      compression: true,
      includeData: true,
      includeSchema: true,
      excludeTables: ['audit_logs'], // 排除审计日志表
      ...backupConfig,
    };
  }

  /**
   * 初始化备份管理器
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing backup manager...');

      // 创建备份目录
      await this.ensureBackupDirectory();

      // 加载现有备份信息
      await this.loadBackupHistory();

      // 启动定时备份
      if (this.config.enabled) {
        this.startScheduledBackup();
      }

      this.logger.info('Backup manager initialized successfully', {
        enabled: this.config.enabled,
        schedule: this.config.schedule,
        backupPath: this.config.backupPath,
        existingBackups: this.backups.length,
      });
    } catch (error) {
      this.logger.error('Failed to initialize backup manager:', error);
      throw error;
    }
  }

  /**
   * 创建数据库备份
   */
  async createBackup(options?: {
    type?: 'full' | 'schema' | 'data';
    filename?: string;
    compress?: boolean;
  }): Promise<BackupInfo> {
    const startTime = Date.now();
    const backupType = options?.type || 'full';
    const compress = options?.compress ?? this.config.compression;
    
    try {
      this.logger.info('Starting database backup...', { type: backupType });

      // 生成备份文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = options?.filename || `erp_assistant_${backupType}_${timestamp}.sql`;
      const filepath = path.join(this.config.backupPath, filename);

      // 构建pg_dump命令
      const dumpArgs = this.buildDumpCommand(backupType, filepath);

      // 执行备份
      await this.executeDump(dumpArgs);

      // 压缩备份文件
      let finalFilepath = filepath;
      if (compress) {
        finalFilepath = await this.compressBackup(filepath);
        await fs.unlink(filepath); // 删除未压缩的文件
      }

      // 获取文件信息
      const stats = await fs.stat(finalFilepath);
      const checksum = await this.calculateChecksum(finalFilepath);

      // 创建备份信息
      const backupInfo: BackupInfo = {
        id: this.generateBackupId(),
        filename: path.basename(finalFilepath),
        filepath: finalFilepath,
        size: stats.size,
        createdAt: new Date(),
        type: backupType,
        compressed: compress,
        checksum,
      };

      // 保存备份信息
      this.backups.push(backupInfo);
      await this.saveBackupHistory();

      const duration = Date.now() - startTime;
      this.logger.info('Database backup completed successfully', {
        filename: backupInfo.filename,
        size: this.formatFileSize(backupInfo.size),
        duration: `${duration}ms`,
        type: backupType,
        compressed: compress,
      });

      return backupInfo;
    } catch (error) {
      this.logger.error('Database backup failed:', error);
      throw error;
    }
  }

  /**
   * 恢复数据库备份
   */
  async restoreBackup(backupId: string, options?: {
    dropExisting?: boolean;
    dataOnly?: boolean;
    schemaOnly?: boolean;
  }): Promise<void> {
    try {
      const backup = this.backups.find(b => b.id === backupId);
      if (!backup) {
        throw new Error(`Backup not found: ${backupId}`);
      }

      this.logger.info('Starting database restore...', {
        backupId,
        filename: backup.filename,
        type: backup.type,
      });

      // 验证备份文件
      await this.validateBackup(backup);

      // 解压备份文件（如果需要）
      let restoreFilepath = backup.filepath;
      if (backup.compressed) {
        restoreFilepath = await this.decompressBackup(backup.filepath);
      }

      // 构建psql恢复命令
      const restoreArgs = this.buildRestoreCommand(restoreFilepath, options);

      // 执行恢复
      await this.executeRestore(restoreArgs);

      // 清理临时文件
      if (backup.compressed && restoreFilepath !== backup.filepath) {
        await fs.unlink(restoreFilepath);
      }

      this.logger.info('Database restore completed successfully', {
        backupId,
        filename: backup.filename,
      });
    } catch (error) {
      this.logger.error('Database restore failed:', error);
      throw error;
    }
  }

  /**
   * 获取备份列表
   */
  getBackups(): BackupInfo[] {
    return [...this.backups].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  /**
   * 删除备份
   */
  async deleteBackup(backupId: string): Promise<void> {
    try {
      const backupIndex = this.backups.findIndex(b => b.id === backupId);
      if (backupIndex === -1) {
        throw new Error(`Backup not found: ${backupId}`);
      }

      const backup = this.backups[backupIndex];

      // 删除备份文件
      await fs.unlink(backup.filepath);

      // 从列表中移除
      this.backups.splice(backupIndex, 1);
      await this.saveBackupHistory();

      this.logger.info('Backup deleted successfully', {
        backupId,
        filename: backup.filename,
      });
    } catch (error) {
      this.logger.error('Failed to delete backup:', error);
      throw error;
    }
  }

  /**
   * 清理过期备份
   */
  async cleanupOldBackups(): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays);

      const expiredBackups = this.backups.filter(b => b.createdAt < cutoffDate);
      let deletedCount = 0;

      for (const backup of expiredBackups) {
        try {
          await this.deleteBackup(backup.id);
          deletedCount++;
        } catch (error) {
          this.logger.warn('Failed to delete expired backup:', error, {
            backupId: backup.id,
            filename: backup.filename,
          });
        }
      }

      if (deletedCount > 0) {
        this.logger.info(`Cleaned up ${deletedCount} expired backups`);
      }

      return deletedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup old backups:', error);
      return 0;
    }
  }

  /**
   * 启动定时备份
   */
  private startScheduledBackup(): void {
    if (this.cronJob) {
      this.cronJob.stop();
    }

    this.cronJob = cron.schedule(this.config.schedule, async () => {
      try {
        this.logger.info('Starting scheduled backup...');
        await this.createBackup();
        await this.cleanupOldBackups();
      } catch (error) {
        this.logger.error('Scheduled backup failed:', error);
      }
    }, {
      scheduled: true,
      timezone: 'UTC',
    });

    this.logger.info('Scheduled backup started', {
      schedule: this.config.schedule,
    });
  }

  /**
   * 停止定时备份
   */
  stopScheduledBackup(): void {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = undefined;
      this.logger.info('Scheduled backup stopped');
    }
  }

  // 私有辅助方法

  private async ensureBackupDirectory(): Promise<void> {
    try {
      await fs.access(this.config.backupPath);
    } catch {
      await fs.mkdir(this.config.backupPath, { recursive: true });
      this.logger.info('Created backup directory', { path: this.config.backupPath });
    }
  }

  private async loadBackupHistory(): Promise<void> {
    try {
      const historyFile = path.join(this.config.backupPath, 'backup-history.json');
      const data = await fs.readFile(historyFile, 'utf-8');
      this.backups = JSON.parse(data).map((b: any) => ({
        ...b,
        createdAt: new Date(b.createdAt),
      }));
    } catch {
      // 历史文件不存在，从空开始
      this.backups = [];
    }
  }

  private async saveBackupHistory(): Promise<void> {
    const historyFile = path.join(this.config.backupPath, 'backup-history.json');
    await fs.writeFile(historyFile, JSON.stringify(this.backups, null, 2));
  }

  private buildDumpCommand(type: string, filepath: string): string[] {
    const args = [
      'pg_dump',
      '-h', config.database.host,
      '-p', config.database.port.toString(),
      '-U', config.database.user,
      '-d', config.database.database,
      '-f', filepath,
      '--verbose',
    ];

    // 根据备份类型添加参数
    switch (type) {
      case 'schema':
        args.push('--schema-only');
        break;
      case 'data':
        args.push('--data-only');
        break;
      default:
        // full backup - 包含schema和data
        break;
    }

    // 排除表
    if (this.config.excludeTables) {
      for (const table of this.config.excludeTables) {
        args.push('--exclude-table', table);
      }
    }

    return args;
  }

  private buildRestoreCommand(filepath: string, options?: any): string[] {
    const args = [
      'psql',
      '-h', config.database.host,
      '-p', config.database.port.toString(),
      '-U', config.database.user,
      '-d', config.database.database,
      '-f', filepath,
      '--verbose',
    ];

    return args;
  }

  private executeDump(args: string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      const process = spawn(args[0], args.slice(1), {
        env: { ...process.env, PGPASSWORD: config.database.password },
      });

      let stderr = '';

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`pg_dump failed with code ${code}: ${stderr}`));
        }
      });
    });
  }

  private executeRestore(args: string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      const process = spawn(args[0], args.slice(1), {
        env: { ...process.env, PGPASSWORD: config.database.password },
      });

      let stderr = '';

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`psql failed with code ${code}: ${stderr}`));
        }
      });
    });
  }

  private async compressBackup(filepath: string): Promise<string> {
    // 简化版本 - 实际应用中可以使用gzip
    const compressedPath = `${filepath}.gz`;
    // 这里应该实现实际的压缩逻辑
    await fs.copyFile(filepath, compressedPath);
    return compressedPath;
  }

  private async decompressBackup(filepath: string): Promise<string> {
    // 简化版本 - 实际应用中应该解压gzip
    const decompressedPath = filepath.replace('.gz', '');
    await fs.copyFile(filepath, decompressedPath);
    return decompressedPath;
  }

  private async calculateChecksum(filepath: string): Promise<string> {
    const crypto = require('crypto');
    const data = await fs.readFile(filepath);
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  private async validateBackup(backup: BackupInfo): Promise<void> {
    // 检查文件是否存在
    await fs.access(backup.filepath);

    // 验证校验和
    if (backup.checksum) {
      const currentChecksum = await this.calculateChecksum(backup.filepath);
      if (currentChecksum !== backup.checksum) {
        throw new Error('Backup file checksum mismatch - file may be corrupted');
      }
    }
  }

  private generateBackupId(): string {
    return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 销毁备份管理器
   */
  destroy(): void {
    this.stopScheduledBackup();
    this.logger.info('Backup manager destroyed');
  }
}

// 导出单例实例
export const backupManager = new BackupManager();
