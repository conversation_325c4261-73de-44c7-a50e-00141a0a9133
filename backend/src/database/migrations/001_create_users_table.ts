// ERP智能助手后端服务 - 用户表迁移

import { Knex } from 'knex';
import { TableNames } from '../types';

export async function up(knex: Knex): Promise<void> {
  // 创建用户表
  await knex.schema.createTable(TableNames.USERS, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 基本信息
    table.string('email', 255).notNullable().unique();
    table.string('username', 50).notNullable().unique();
    table.string('password', 255).notNullable();
    table.string('first_name', 100).notNullable();
    table.string('last_name', 100).notNullable();
    
    // 角色和权限
    table.enum('role', ['admin', 'user', 'viewer']).notNullable().defaultTo('user');
    table.jsonb('permissions').notNullable().defaultTo('[]');
    
    // 状态字段
    table.boolean('is_active').notNullable().defaultTo(true);
    table.boolean('email_verified').notNullable().defaultTo(false);
    
    // 时间戳
    table.timestamp('last_login_at').nullable();
    table.timestamps(true, true); // created_at, updated_at
    
    // 索引
    table.index(['email']);
    table.index(['username']);
    table.index(['role']);
    table.index(['is_active']);
    table.index(['created_at']);
  });

  // 创建用户配置表
  await knex.schema.createTable(TableNames.USER_PROFILES, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 外键
    table.uuid('user_id').notNullable().references('id').inTable(TableNames.USERS).onDelete('CASCADE');
    
    // 个人信息
    table.string('avatar_url', 500).nullable();
    table.string('phone', 20).nullable();
    table.string('company', 200).nullable();
    table.string('department', 200).nullable();
    table.string('position', 200).nullable();
    
    // 偏好设置
    table.string('timezone', 50).notNullable().defaultTo('UTC');
    table.string('language', 10).notNullable().defaultTo('en');
    table.string('theme', 20).notNullable().defaultTo('light');
    table.jsonb('preferences').notNullable().defaultTo('{}');
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.unique(['user_id']);
    table.index(['company']);
    table.index(['department']);
  });

  // 创建API密钥表
  await knex.schema.createTable(TableNames.API_KEYS, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 外键
    table.uuid('user_id').notNullable().references('id').inTable(TableNames.USERS).onDelete('CASCADE');
    
    // 密钥信息
    table.string('name', 100).notNullable();
    table.string('key_hash', 255).notNullable().unique();
    table.string('key_prefix', 10).notNullable();
    table.jsonb('permissions').notNullable().defaultTo('[]');
    
    // 限制和统计
    table.integer('rate_limit').notNullable().defaultTo(1000);
    table.timestamp('expires_at').nullable();
    table.timestamp('last_used_at').nullable();
    table.bigInteger('usage_count').notNullable().defaultTo(0);
    
    // 状态
    table.boolean('is_active').notNullable().defaultTo(true);
    table.jsonb('metadata').notNullable().defaultTo('{}');
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['user_id']);
    table.index(['key_hash']);
    table.index(['is_active']);
    table.index(['expires_at']);
  });

  // 创建通知表
  await knex.schema.createTable(TableNames.NOTIFICATIONS, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 外键
    table.uuid('user_id').notNullable().references('id').inTable(TableNames.USERS).onDelete('CASCADE');
    
    // 通知内容
    table.enum('type', ['info', 'success', 'warning', 'error']).notNullable();
    table.string('title', 200).notNullable();
    table.text('message').notNullable();
    table.string('action_url', 500).nullable();
    table.string('action_text', 100).nullable();
    
    // 状态
    table.boolean('is_read').notNullable().defaultTo(false);
    table.timestamp('read_at').nullable();
    table.timestamp('expires_at').nullable();
    table.jsonb('metadata').notNullable().defaultTo('{}');
    
    // 时间戳
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    
    // 索引
    table.index(['user_id']);
    table.index(['type']);
    table.index(['is_read']);
    table.index(['created_at']);
    table.index(['expires_at']);
  });

  // 创建系统配置表
  await knex.schema.createTable(TableNames.SYSTEM_CONFIGS, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 配置信息
    table.string('key', 100).notNullable().unique();
    table.jsonb('value').notNullable();
    table.enum('type', ['string', 'number', 'boolean', 'json']).notNullable();
    table.string('category', 50).notNullable();
    table.text('description').nullable();
    
    // 权限和安全
    table.boolean('is_public').notNullable().defaultTo(false);
    table.boolean('is_encrypted').notNullable().defaultTo(false);
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['key']);
    table.index(['category']);
    table.index(['is_public']);
  });

  // 创建审计日志表
  await knex.schema.createTable(TableNames.AUDIT_LOGS, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 用户信息（可选，系统操作可能没有用户）
    table.uuid('user_id').nullable().references('id').inTable(TableNames.USERS).onDelete('SET NULL');
    
    // 操作信息
    table.string('action', 100).notNullable();
    table.string('resource_type', 50).notNullable();
    table.string('resource_id', 100).nullable();
    table.jsonb('old_values').nullable();
    table.jsonb('new_values').nullable();
    
    // 请求信息
    table.string('ip_address', 45).notNullable(); // IPv6支持
    table.text('user_agent').nullable();
    table.string('session_id', 100).nullable();
    
    // 结果
    table.enum('status', ['success', 'failure']).notNullable();
    table.text('error_message').nullable();
    table.jsonb('metadata').notNullable().defaultTo('{}');
    
    // 时间戳
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    
    // 索引
    table.index(['user_id']);
    table.index(['action']);
    table.index(['resource_type']);
    table.index(['status']);
    table.index(['created_at']);
    table.index(['ip_address']);
  });

  console.log('✅ Created users and related tables');
}

export async function down(knex: Knex): Promise<void> {
  // 按依赖关系逆序删除表
  await knex.schema.dropTableIfExists(TableNames.AUDIT_LOGS);
  await knex.schema.dropTableIfExists(TableNames.SYSTEM_CONFIGS);
  await knex.schema.dropTableIfExists(TableNames.NOTIFICATIONS);
  await knex.schema.dropTableIfExists(TableNames.API_KEYS);
  await knex.schema.dropTableIfExists(TableNames.USER_PROFILES);
  await knex.schema.dropTableIfExists(TableNames.USERS);
  
  console.log('✅ Dropped users and related tables');
}
