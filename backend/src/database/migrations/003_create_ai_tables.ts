// ERP智能助手后端服务 - AI分析相关表迁移

import { Knex } from 'knex';
import { TableNames } from '../types';

export async function up(knex: Knex): Promise<void> {
  // 创建AI分析结果表
  await knex.schema.createTable(TableNames.AI_ANALYSES, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 外键
    table.uuid('session_id').notNullable().references('id').inTable(TableNames.RECORDING_SESSIONS).onDelete('CASCADE');
    table.uuid('operation_id').nullable().references('id').inTable(TableNames.OPERATION_RECORDS).onDelete('SET NULL');
    
    // 分析类型和模型信息
    table.enum('analysis_type', ['intent', 'pattern', 'error', 'optimization', 'guidance']).notNullable();
    table.string('model_name', 100).notNullable();
    table.string('model_version', 50).notNullable();
    
    // 输入输出数据
    table.jsonb('input_data').notNullable();
    table.jsonb('output_data').notNullable();
    
    // 分析结果
    table.decimal('confidence_score', 5, 4).notNullable(); // 0.0000 - 1.0000
    table.integer('processing_time').notNullable(); // 毫秒
    table.integer('tokens_used').notNullable().defaultTo(0);
    table.decimal('cost', 10, 6).notNullable().defaultTo(0); // 美分，支持6位小数
    
    // 状态
    table.enum('status', ['pending', 'completed', 'failed']).notNullable().defaultTo('pending');
    table.text('error_message').nullable();
    table.jsonb('metadata').notNullable().defaultTo('{}');
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['session_id']);
    table.index(['operation_id']);
    table.index(['analysis_type']);
    table.index(['model_name']);
    table.index(['status']);
    table.index(['confidence_score']);
    table.index(['created_at']);
  });

  // 创建智能引导步骤表
  await knex.schema.createTable(TableNames.GUIDANCE_STEPS, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 外键
    table.uuid('session_id').notNullable().references('id').inTable(TableNames.RECORDING_SESSIONS).onDelete('CASCADE');
    table.uuid('analysis_id').nullable().references('id').inTable(TableNames.AI_ANALYSES).onDelete('SET NULL');
    
    // 步骤信息
    table.integer('step_number').notNullable();
    table.string('title', 200).notNullable();
    table.text('description').notNullable();
    
    // 操作信息
    table.enum('action_type', ['click', 'input', 'navigate', 'wait', 'verify']).notNullable();
    table.text('target_selector').nullable();
    table.text('target_xpath').nullable();
    table.text('expected_value').nullable();
    table.uuid('screenshot_id').nullable().references('id').inTable(TableNames.SCREENSHOTS).onDelete('SET NULL');
    
    // 完成状态
    table.boolean('is_completed').notNullable().defaultTo(false);
    table.timestamp('completion_time').nullable();
    
    // 难度和时间估算
    table.decimal('success_rate', 5, 4).notNullable().defaultTo(0); // 0.0000 - 1.0000
    table.enum('difficulty_level', ['easy', 'medium', 'hard']).notNullable().defaultTo('medium');
    table.integer('estimated_time').notNullable().defaultTo(0); // 秒
    
    // 辅助信息
    table.specificType('prerequisites', 'text[]').notNullable().defaultTo('{}');
    table.specificType('tips', 'text[]').notNullable().defaultTo('{}');
    table.specificType('warnings', 'text[]').notNullable().defaultTo('{}');
    table.jsonb('metadata').notNullable().defaultTo('{}');
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['session_id']);
    table.index(['analysis_id']);
    table.index(['step_number']);
    table.index(['action_type']);
    table.index(['is_completed']);
    table.index(['difficulty_level']);
    table.unique(['session_id', 'step_number']);
  });

  // 创建向量文档表
  await knex.schema.createTable(TableNames.VECTOR_DOCUMENTS, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 集合和类型
    table.string('collection_name', 100).notNullable();
    table.enum('document_type', ['operation', 'documentation', 'error', 'guidance']).notNullable();
    
    // 文档内容
    table.string('title', 500).notNullable();
    table.text('content').notNullable();
    table.string('content_hash', 64).notNullable(); // SHA-256哈希
    
    // 向量信息
    table.string('embedding_model', 100).notNullable();
    table.integer('embedding_dimension').notNullable();
    
    // 源信息
    table.string('source_id', 100).nullable();
    table.string('source_type', 50).nullable();
    
    // 标签和分类
    table.specificType('tags', 'text[]').notNullable().defaultTo('{}');
    table.string('language', 10).notNullable().defaultTo('en');
    table.integer('version').notNullable().defaultTo(1);
    
    // 状态
    table.boolean('is_active').notNullable().defaultTo(true);
    table.jsonb('metadata').notNullable().defaultTo('{}');
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['collection_name']);
    table.index(['document_type']);
    table.index(['content_hash']);
    table.index(['embedding_model']);
    table.index(['source_id']);
    table.index(['source_type']);
    table.index(['is_active']);
    table.index(['tags'], undefined, 'gin'); // GIN索引用于数组搜索
    table.index(['language']);
    table.index(['created_at']);
    
    // 唯一约束
    table.unique(['collection_name', 'content_hash']);
  });

  // 创建全文搜索索引
  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_vector_documents_content_fts 
    ON ${TableNames.VECTOR_DOCUMENTS} 
    USING gin(to_tsvector('english', title || ' ' || content))
  `);

  // 创建复合索引
  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_ai_analyses_session_type_status 
    ON ${TableNames.AI_ANALYSES} (session_id, analysis_type, status)
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_guidance_steps_session_completed 
    ON ${TableNames.GUIDANCE_STEPS} (session_id, is_completed, step_number)
  `);

  console.log('✅ Created AI analysis and related tables');
}

export async function down(knex: Knex): Promise<void> {
  // 删除索引
  await knex.raw(`DROP INDEX IF EXISTS idx_vector_documents_content_fts`);
  await knex.raw(`DROP INDEX IF EXISTS idx_ai_analyses_session_type_status`);
  await knex.raw(`DROP INDEX IF EXISTS idx_guidance_steps_session_completed`);
  
  // 按依赖关系逆序删除表
  await knex.schema.dropTableIfExists(TableNames.VECTOR_DOCUMENTS);
  await knex.schema.dropTableIfExists(TableNames.GUIDANCE_STEPS);
  await knex.schema.dropTableIfExists(TableNames.AI_ANALYSES);
  
  console.log('✅ Dropped AI analysis and related tables');
}
