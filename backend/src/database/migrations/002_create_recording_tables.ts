// ERP智能助手后端服务 - 录制相关表迁移

import { Knex } from 'knex';
import { TableNames } from '../types';

export async function up(knex: Knex): Promise<void> {
  // 创建录制会话表
  await knex.schema.createTable(TableNames.RECORDING_SESSIONS, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 外键
    table.uuid('user_id').notNullable().references('id').inTable(TableNames.USERS).onDelete('CASCADE');
    
    // 基本信息
    table.string('title', 200).notNullable();
    table.text('description').nullable();
    table.enum('status', ['active', 'completed', 'failed', 'cancelled']).notNullable().defaultTo('active');
    
    // 时间信息
    table.timestamp('start_time').notNullable().defaultTo(knex.fn.now());
    table.timestamp('end_time').nullable();
    table.integer('duration').nullable(); // 毫秒
    
    // 页面信息
    table.string('page_url', 2000).notNullable();
    table.string('page_title', 500).nullable();
    table.jsonb('browser_info').notNullable().defaultTo('{}');
    
    // 统计信息
    table.integer('operation_count').notNullable().defaultTo(0);
    table.integer('screenshot_count').notNullable().defaultTo(0);
    table.integer('error_count').notNullable().defaultTo(0);
    
    // 标签和分类
    table.specificType('tags', 'text[]').notNullable().defaultTo('{}');
    table.boolean('is_public').notNullable().defaultTo(false);
    table.jsonb('metadata').notNullable().defaultTo('{}');
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['user_id']);
    table.index(['status']);
    table.index(['start_time']);
    table.index(['is_public']);
    table.index(['tags'], undefined, 'gin'); // GIN索引用于数组搜索
  });

  // 创建操作记录表
  await knex.schema.createTable(TableNames.OPERATION_RECORDS, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 外键
    table.uuid('session_id').notNullable().references('id').inTable(TableNames.RECORDING_SESSIONS).onDelete('CASCADE');
    
    // 序列信息
    table.integer('sequence_number').notNullable();
    table.enum('type', ['dom_event', 'navigation', 'screenshot', 'error', 'custom']).notNullable();
    table.string('event_type', 50).nullable(); // click, input, submit, etc.
    
    // 时间信息
    table.timestamp('timestamp').notNullable();
    
    // 页面信息
    table.string('page_url', 2000).notNullable();
    
    // 元素信息
    table.text('element_selector').nullable();
    table.text('element_xpath').nullable();
    table.text('element_text').nullable();
    table.jsonb('element_attributes').notNullable().defaultTo('{}');
    
    // 交互信息
    table.jsonb('coordinates').nullable(); // {x, y}
    table.text('input_value').nullable();
    table.uuid('screenshot_id').nullable();
    
    // 错误信息
    table.text('error_message').nullable();
    table.text('stack_trace').nullable();
    
    // 性能信息
    table.jsonb('performance_metrics').notNullable().defaultTo('{}');
    table.jsonb('metadata').notNullable().defaultTo('{}');
    
    // 时间戳
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    
    // 索引
    table.index(['session_id']);
    table.index(['type']);
    table.index(['event_type']);
    table.index(['timestamp']);
    table.index(['sequence_number']);
    table.unique(['session_id', 'sequence_number']);
  });

  // 创建截图表
  await knex.schema.createTable(TableNames.SCREENSHOTS, (table) => {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 外键
    table.uuid('session_id').notNullable().references('id').inTable(TableNames.RECORDING_SESSIONS).onDelete('CASCADE');
    table.uuid('operation_id').nullable().references('id').inTable(TableNames.OPERATION_RECORDS).onDelete('SET NULL');
    
    // 文件信息
    table.string('filename', 255).notNullable();
    table.string('file_path', 1000).notNullable();
    table.bigInteger('file_size').notNullable();
    table.string('mime_type', 100).notNullable();
    
    // 图像信息
    table.integer('width').notNullable();
    table.integer('height').notNullable();
    table.enum('quality', ['high', 'medium', 'low']).notNullable().defaultTo('medium');
    table.string('thumbnail_path', 1000).nullable();
    
    // 页面信息
    table.string('page_url', 2000).notNullable();
    table.jsonb('viewport').notNullable(); // {width, height}
    table.jsonb('scroll_position').notNullable(); // {x, y}
    table.jsonb('element_bounds').nullable(); // {x, y, width, height}
    
    // 注释和标记
    table.jsonb('annotations').notNullable().defaultTo('[]');
    table.jsonb('metadata').notNullable().defaultTo('{}');
    
    // 时间戳
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    
    // 索引
    table.index(['session_id']);
    table.index(['operation_id']);
    table.index(['quality']);
    table.index(['created_at']);
    table.index(['file_size']);
  });

  // 为操作记录表添加截图外键约束
  await knex.schema.alterTable(TableNames.OPERATION_RECORDS, (table) => {
    table.foreign('screenshot_id').references('id').inTable(TableNames.SCREENSHOTS).onDelete('SET NULL');
  });

  console.log('✅ Created recording and related tables');
}

export async function down(knex: Knex): Promise<void> {
  // 按依赖关系逆序删除表
  await knex.schema.dropTableIfExists(TableNames.SCREENSHOTS);
  await knex.schema.dropTableIfExists(TableNames.OPERATION_RECORDS);
  await knex.schema.dropTableIfExists(TableNames.RECORDING_SESSIONS);
  
  console.log('✅ Dropped recording and related tables');
}
