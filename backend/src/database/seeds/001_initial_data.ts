// ERP智能助手后端服务 - 初始数据种子

import { Knex } from 'knex';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { TableNames } from '../types';

export async function seed(knex: Knex): Promise<void> {
  // 清理现有数据（开发环境）
  if (process.env.NODE_ENV === 'development') {
    await knex(TableNames.AUDIT_LOGS).del();
    await knex(TableNames.NOTIFICATIONS).del();
    await knex(TableNames.API_KEYS).del();
    await knex(TableNames.USER_PROFILES).del();
    await knex(TableNames.USERS).del();
    await knex(TableNames.SYSTEM_CONFIGS).del();
  }

  // 创建管理员用户
  const adminId = uuidv4();
  const adminPassword = await bcrypt.hash('Admin123!', 12);
  
  await knex(TableNames.USERS).insert({
    id: adminId,
    email: '<EMAIL>',
    username: 'admin',
    password: adminPassword,
    first_name: 'System',
    last_name: 'Administrator',
    role: 'admin',
    permissions: JSON.stringify(['read', 'write', 'delete', 'admin']),
    is_active: true,
    email_verified: true,
    created_at: knex.fn.now(),
    updated_at: knex.fn.now(),
  });

  // 创建管理员配置
  await knex(TableNames.USER_PROFILES).insert({
    id: uuidv4(),
    user_id: adminId,
    company: 'ERP Assistant',
    department: 'Engineering',
    position: 'System Administrator',
    timezone: 'UTC',
    language: 'en',
    theme: 'light',
    preferences: JSON.stringify({
      notifications: {
        email: true,
        browser: true,
        mobile: false,
      },
      dashboard: {
        layout: 'grid',
        widgets: ['stats', 'recent-sessions', 'ai-insights'],
      },
      recording: {
        autoStart: false,
        quality: 'medium',
        includeScreenshots: true,
      },
    }),
    created_at: knex.fn.now(),
    updated_at: knex.fn.now(),
  });

  // 创建演示用户
  const demoUserId = uuidv4();
  const demoPassword = await bcrypt.hash('Demo123!', 12);
  
  await knex(TableNames.USERS).insert({
    id: demoUserId,
    email: '<EMAIL>',
    username: 'demo',
    password: demoPassword,
    first_name: 'Demo',
    last_name: 'User',
    role: 'user',
    permissions: JSON.stringify(['read', 'write']),
    is_active: true,
    email_verified: true,
    created_at: knex.fn.now(),
    updated_at: knex.fn.now(),
  });

  // 创建演示用户配置
  await knex(TableNames.USER_PROFILES).insert({
    id: uuidv4(),
    user_id: demoUserId,
    company: 'Demo Company',
    department: 'Sales',
    position: 'Sales Representative',
    timezone: 'America/New_York',
    language: 'en',
    theme: 'light',
    preferences: JSON.stringify({
      notifications: {
        email: true,
        browser: true,
        mobile: true,
      },
      dashboard: {
        layout: 'list',
        widgets: ['recent-sessions', 'quick-actions'],
      },
      recording: {
        autoStart: true,
        quality: 'medium',
        includeScreenshots: true,
      },
    }),
    created_at: knex.fn.now(),
    updated_at: knex.fn.now(),
  });

  // 创建查看者用户
  const viewerId = uuidv4();
  const viewerPassword = await bcrypt.hash('Viewer123!', 12);
  
  await knex(TableNames.USERS).insert({
    id: viewerId,
    email: '<EMAIL>',
    username: 'viewer',
    password: viewerPassword,
    first_name: 'Read',
    last_name: 'Only',
    role: 'viewer',
    permissions: JSON.stringify(['read']),
    is_active: true,
    email_verified: true,
    created_at: knex.fn.now(),
    updated_at: knex.fn.now(),
  });

  // 创建查看者配置
  await knex(TableNames.USER_PROFILES).insert({
    id: uuidv4(),
    user_id: viewerId,
    company: 'External Auditor',
    department: 'Compliance',
    position: 'Auditor',
    timezone: 'Europe/London',
    language: 'en',
    theme: 'light',
    preferences: JSON.stringify({
      notifications: {
        email: false,
        browser: false,
        mobile: false,
      },
      dashboard: {
        layout: 'grid',
        widgets: ['stats'],
      },
      recording: {
        autoStart: false,
        quality: 'low',
        includeScreenshots: false,
      },
    }),
    created_at: knex.fn.now(),
    updated_at: knex.fn.now(),
  });

  // 创建系统配置
  const systemConfigs = [
    {
      id: uuidv4(),
      key: 'app.name',
      value: JSON.stringify('ERP智能助手'),
      type: 'string',
      category: 'application',
      description: '应用程序名称',
      is_public: true,
      is_encrypted: false,
    },
    {
      id: uuidv4(),
      key: 'app.version',
      value: JSON.stringify('1.0.0'),
      type: 'string',
      category: 'application',
      description: '应用程序版本',
      is_public: true,
      is_encrypted: false,
    },
    {
      id: uuidv4(),
      key: 'recording.max_duration',
      value: JSON.stringify(3600000), // 1小时
      type: 'number',
      category: 'recording',
      description: '录制会话最大持续时间（毫秒）',
      is_public: false,
      is_encrypted: false,
    },
    {
      id: uuidv4(),
      key: 'recording.max_operations',
      value: JSON.stringify(10000),
      type: 'number',
      category: 'recording',
      description: '单次录制最大操作数量',
      is_public: false,
      is_encrypted: false,
    },
    {
      id: uuidv4(),
      key: 'ai.default_model',
      value: JSON.stringify('gpt-4'),
      type: 'string',
      category: 'ai',
      description: '默认AI模型',
      is_public: false,
      is_encrypted: false,
    },
    {
      id: uuidv4(),
      key: 'ai.max_tokens',
      value: JSON.stringify(2000),
      type: 'number',
      category: 'ai',
      description: 'AI响应最大令牌数',
      is_public: false,
      is_encrypted: false,
    },
    {
      id: uuidv4(),
      key: 'storage.max_file_size',
      value: JSON.stringify(10485760), // 10MB
      type: 'number',
      category: 'storage',
      description: '文件上传最大大小（字节）',
      is_public: false,
      is_encrypted: false,
    },
    {
      id: uuidv4(),
      key: 'security.session_timeout',
      value: JSON.stringify(86400), // 24小时
      type: 'number',
      category: 'security',
      description: '会话超时时间（秒）',
      is_public: false,
      is_encrypted: false,
    },
    {
      id: uuidv4(),
      key: 'features.ai_analysis',
      value: JSON.stringify(true),
      type: 'boolean',
      category: 'features',
      description: '启用AI分析功能',
      is_public: true,
      is_encrypted: false,
    },
    {
      id: uuidv4(),
      key: 'features.smart_guidance',
      value: JSON.stringify(true),
      type: 'boolean',
      category: 'features',
      description: '启用智能引导功能',
      is_public: true,
      is_encrypted: false,
    },
  ];

  for (const config of systemConfigs) {
    await knex(TableNames.SYSTEM_CONFIGS).insert({
      ...config,
      created_at: knex.fn.now(),
      updated_at: knex.fn.now(),
    });
  }

  // 创建欢迎通知
  const welcomeNotifications = [
    {
      id: uuidv4(),
      user_id: adminId,
      type: 'info',
      title: '欢迎使用ERP智能助手',
      message: '您已成功登录管理员账户。请查看系统配置并开始使用各项功能。',
      action_url: '/dashboard',
      action_text: '前往控制台',
      is_read: false,
      metadata: JSON.stringify({ source: 'system', priority: 'high' }),
      created_at: knex.fn.now(),
    },
    {
      id: uuidv4(),
      user_id: demoUserId,
      type: 'success',
      title: '账户创建成功',
      message: '欢迎使用ERP智能助手！您可以开始录制操作并体验AI分析功能。',
      action_url: '/recordings/new',
      action_text: '开始录制',
      is_read: false,
      metadata: JSON.stringify({ source: 'system', priority: 'medium' }),
      created_at: knex.fn.now(),
    },
    {
      id: uuidv4(),
      user_id: viewerId,
      type: 'info',
      title: '查看者权限说明',
      message: '您当前拥有只读权限，可以查看录制会话和分析结果，但无法创建或修改内容。',
      is_read: false,
      metadata: JSON.stringify({ source: 'system', priority: 'low' }),
      created_at: knex.fn.now(),
    },
  ];

  for (const notification of welcomeNotifications) {
    await knex(TableNames.NOTIFICATIONS).insert(notification);
  }

  console.log('✅ Inserted initial seed data:');
  console.log('   - 3 users (admin, demo, viewer)');
  console.log('   - 3 user profiles');
  console.log('   - 10 system configurations');
  console.log('   - 3 welcome notifications');
  console.log('');
  console.log('🔑 Default login credentials:');
  console.log('   Admin: <EMAIL> / Admin123!');
  console.log('   Demo:  <EMAIL> / Demo123!');
  console.log('   Viewer: <EMAIL> / Viewer123!');
}
