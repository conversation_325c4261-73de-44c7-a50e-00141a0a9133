// ERP智能助手后端服务 - 数据库类型定义

// 用户表
export interface User {
  id: string;
  email: string;
  username: string;
  password: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'user' | 'viewer';
  permissions: string[];
  is_active: boolean;
  email_verified: boolean;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;
}

// 用户配置表
export interface UserProfile {
  id: string;
  user_id: string;
  avatar_url?: string;
  phone?: string;
  company?: string;
  department?: string;
  position?: string;
  timezone: string;
  language: string;
  theme: string;
  preferences: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

// 录制会话表
export interface RecordingSession {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  status: 'active' | 'completed' | 'failed' | 'cancelled';
  start_time: Date;
  end_time?: Date;
  duration?: number; // 毫秒
  page_url: string;
  page_title?: string;
  browser_info: Record<string, any>;
  operation_count: number;
  screenshot_count: number;
  error_count: number;
  tags: string[];
  is_public: boolean;
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

// 操作记录表
export interface OperationRecord {
  id: string;
  session_id: string;
  sequence_number: number;
  type: 'dom_event' | 'navigation' | 'screenshot' | 'error' | 'custom';
  event_type?: string; // click, input, submit, etc.
  timestamp: Date;
  page_url: string;
  element_selector?: string;
  element_xpath?: string;
  element_text?: string;
  element_attributes: Record<string, any>;
  coordinates?: { x: number; y: number };
  input_value?: string;
  screenshot_id?: string;
  error_message?: string;
  stack_trace?: string;
  performance_metrics: Record<string, any>;
  metadata: Record<string, any>;
  created_at: Date;
}

// 截图表
export interface Screenshot {
  id: string;
  session_id: string;
  operation_id?: string;
  filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  width: number;
  height: number;
  quality: 'high' | 'medium' | 'low';
  thumbnail_path?: string;
  page_url: string;
  viewport: { width: number; height: number };
  scroll_position: { x: number; y: number };
  element_bounds?: { x: number; y: number; width: number; height: number };
  annotations: Record<string, any>[];
  metadata: Record<string, any>;
  created_at: Date;
}

// AI分析结果表
export interface AIAnalysis {
  id: string;
  session_id: string;
  operation_id?: string;
  analysis_type: 'intent' | 'pattern' | 'error' | 'optimization' | 'guidance';
  model_name: string;
  model_version: string;
  input_data: Record<string, any>;
  output_data: Record<string, any>;
  confidence_score: number;
  processing_time: number; // 毫秒
  tokens_used: number;
  cost: number; // 美分
  status: 'pending' | 'completed' | 'failed';
  error_message?: string;
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

// 智能引导表
export interface GuidanceStep {
  id: string;
  session_id: string;
  analysis_id?: string;
  step_number: number;
  title: string;
  description: string;
  action_type: 'click' | 'input' | 'navigate' | 'wait' | 'verify';
  target_selector?: string;
  target_xpath?: string;
  expected_value?: string;
  screenshot_id?: string;
  is_completed: boolean;
  completion_time?: Date;
  success_rate: number;
  difficulty_level: 'easy' | 'medium' | 'hard';
  estimated_time: number; // 秒
  prerequisites: string[];
  tips: string[];
  warnings: string[];
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

// 向量文档表
export interface VectorDocument {
  id: string;
  collection_name: string;
  document_type: 'operation' | 'documentation' | 'error' | 'guidance';
  title: string;
  content: string;
  content_hash: string;
  embedding_model: string;
  embedding_dimension: number;
  source_id?: string; // 关联的原始记录ID
  source_type?: string; // 原始记录类型
  tags: string[];
  language: string;
  version: number;
  is_active: boolean;
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

// 系统配置表
export interface SystemConfig {
  id: string;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'json';
  category: string;
  description?: string;
  is_public: boolean;
  is_encrypted: boolean;
  created_at: Date;
  updated_at: Date;
}

// 审计日志表
export interface AuditLog {
  id: string;
  user_id?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address: string;
  user_agent: string;
  session_id?: string;
  status: 'success' | 'failure';
  error_message?: string;
  metadata: Record<string, any>;
  created_at: Date;
}

// API密钥表
export interface APIKey {
  id: string;
  user_id: string;
  name: string;
  key_hash: string;
  key_prefix: string; // 用于显示的前缀
  permissions: string[];
  rate_limit: number;
  expires_at?: Date;
  last_used_at?: Date;
  usage_count: number;
  is_active: boolean;
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

// 通知表
export interface Notification {
  id: string;
  user_id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  action_url?: string;
  action_text?: string;
  is_read: boolean;
  read_at?: Date;
  expires_at?: Date;
  metadata: Record<string, any>;
  created_at: Date;
}

// 数据库表名枚举
export enum TableNames {
  USERS = 'users',
  USER_PROFILES = 'user_profiles',
  RECORDING_SESSIONS = 'recording_sessions',
  OPERATION_RECORDS = 'operation_records',
  SCREENSHOTS = 'screenshots',
  AI_ANALYSES = 'ai_analyses',
  GUIDANCE_STEPS = 'guidance_steps',
  VECTOR_DOCUMENTS = 'vector_documents',
  SYSTEM_CONFIGS = 'system_configs',
  AUDIT_LOGS = 'audit_logs',
  API_KEYS = 'api_keys',
  NOTIFICATIONS = 'notifications',
}

// 数据库索引定义
export interface IndexDefinition {
  table: string;
  name: string;
  columns: string[];
  unique?: boolean;
  partial?: string;
  type?: 'btree' | 'hash' | 'gin' | 'gist';
}

// 外键约束定义
export interface ForeignKeyDefinition {
  table: string;
  column: string;
  references: {
    table: string;
    column: string;
  };
  onDelete?: 'CASCADE' | 'SET NULL' | 'RESTRICT';
  onUpdate?: 'CASCADE' | 'SET NULL' | 'RESTRICT';
}
