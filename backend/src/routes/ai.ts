// ERP智能助手后端服务 - AI服务路由

import { Router } from 'express';
import { authMiddleware } from '@/middleware/auth';
import { asyncHandler } from '@/middleware/error-handler';
import { aiRateLimiter } from '@/middleware/rate-limiter';
import { validateAIRequest } from '@/middleware/validation';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: AI
 *   description: AI服务接口
 */

// 所有AI路由都需要认证和速率限制
router.use(authMiddleware);
router.use(aiRateLimiter);

/**
 * @swagger
 * /ai/analyze:
 *   post:
 *     summary: 分析用户操作
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 分析结果
 */
router.post('/analyze',
  validateAIRequest,
  asyncHandler(async (req, res) => {
    // TODO: 实现AI分析
    res.json({
      analysis: {
        intent: 'unknown',
        confidence: 0.5,
        suggestions: [],
      },
    });
  })
);

/**
 * @swagger
 * /ai/guidance:
 *   post:
 *     summary: 获取智能引导
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 引导建议
 */
router.post('/guidance',
  validateAIRequest,
  asyncHandler(async (req, res) => {
    // TODO: 实现智能引导
    res.json({
      guidance: {
        steps: [],
        tips: [],
        warnings: [],
      },
    });
  })
);

export default router;
