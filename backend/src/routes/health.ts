// ERP智能助手后端服务 - 健康检查路由

import { Router } from 'express';
import { healthService } from '@/services/health';
import { databaseService } from '@/services/database';
import { redisService } from '@/services/redis';
import { vectorService } from '@/services/vector';
import { asyncHandler } from '@/middleware/error-handler';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * @swagger
 * /health:
 *   get:
 *     summary: 获取系统健康状态
 *     description: 返回系统整体健康状态和各个服务的详细信息
 *     tags: [Health]
 *     security: []
 *     responses:
 *       200:
 *         description: 系统健康状态
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [healthy, unhealthy, degraded]
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 uptime:
 *                   type: number
 *                   description: 系统运行时间（毫秒）
 *                 version:
 *                   type: string
 *                 environment:
 *                   type: string
 *                 services:
 *                   type: object
 *                   properties:
 *                     database:
 *                       $ref: '#/components/schemas/ServiceHealth'
 *                     redis:
 *                       $ref: '#/components/schemas/ServiceHealth'
 *                     vector:
 *                       $ref: '#/components/schemas/ServiceHealth'
 *                     memory:
 *                       $ref: '#/components/schemas/ServiceHealth'
 *                     disk:
 *                       $ref: '#/components/schemas/ServiceHealth'
 *                 summary:
 *                   type: object
 *                   properties:
 *                     healthy:
 *                       type: number
 *                     unhealthy:
 *                       type: number
 *                     degraded:
 *                       type: number
 *                     total:
 *                       type: number
 */
router.get('/', asyncHandler(async (req, res) => {
  const health = await healthService.checkHealth();
  
  // 根据健康状态设置HTTP状态码
  const statusCode = health.status === 'healthy' ? 200 : 
                    health.status === 'degraded' ? 200 : 503;
  
  res.status(statusCode).json(health);
}));

/**
 * @swagger
 * /health/simple:
 *   get:
 *     summary: 获取简单健康状态
 *     description: 返回简化的健康状态信息，用于负载均衡器等快速检查
 *     tags: [Health]
 *     security: []
 *     responses:
 *       200:
 *         description: 简单健康状态
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [healthy, unhealthy, degraded]
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 uptime:
 *                   type: number
 */
router.get('/simple', asyncHandler(async (req, res) => {
  const health = await healthService.getSimpleHealth();
  res.json(health);
}));

/**
 * @swagger
 * /health/database:
 *   get:
 *     summary: 检查数据库健康状态
 *     description: 返回数据库连接和性能状态
 *     tags: [Health]
 *     security: []
 *     responses:
 *       200:
 *         description: 数据库健康状态
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ServiceHealth'
 */
router.get('/database', asyncHandler(async (req, res) => {
  const health = await databaseService.healthCheck();
  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
}));

/**
 * @swagger
 * /health/redis:
 *   get:
 *     summary: 检查Redis健康状态
 *     description: 返回Redis连接和性能状态
 *     tags: [Health]
 *     security: []
 *     responses:
 *       200:
 *         description: Redis健康状态
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ServiceHealth'
 */
router.get('/redis', asyncHandler(async (req, res) => {
  const health = await redisService.healthCheck();
  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
}));

/**
 * @swagger
 * /health/vector:
 *   get:
 *     summary: 检查向量数据库健康状态
 *     description: 返回向量数据库连接和性能状态
 *     tags: [Health]
 *     security: []
 *     responses:
 *       200:
 *         description: 向量数据库健康状态
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ServiceHealth'
 */
router.get('/vector', asyncHandler(async (req, res) => {
  const health = await vectorService.healthCheck();
  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
}));

/**
 * @swagger
 * /health/stats:
 *   get:
 *     summary: 获取系统统计信息
 *     description: 返回各个服务的详细统计信息
 *     tags: [Health]
 *     security: []
 *     responses:
 *       200:
 *         description: 系统统计信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 health:
 *                   type: object
 *                 database:
 *                   type: object
 *                 redis:
 *                   type: object
 *                 vector:
 *                   type: object
 *                 system:
 *                   type: object
 */
router.get('/stats', asyncHandler(async (req, res) => {
  const stats = {
    health: healthService.getStats(),
    database: databaseService.getStats(),
    redis: redisService.getStats(),
    vector: vectorService.getStats(),
    system: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid,
      memory: process.memoryUsage(),
      uptime: process.uptime(),
    },
  };
  
  res.json(stats);
}));

/**
 * @swagger
 * /health/readiness:
 *   get:
 *     summary: 就绪检查
 *     description: 检查服务是否准备好接收请求（Kubernetes就绪探针）
 *     tags: [Health]
 *     security: []
 *     responses:
 *       200:
 *         description: 服务就绪
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 ready:
 *                   type: boolean
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       503:
 *         description: 服务未就绪
 */
router.get('/readiness', asyncHandler(async (req, res) => {
  try {
    // 检查关键服务是否可用
    const [dbHealth, redisHealth] = await Promise.all([
      databaseService.healthCheck(),
      redisService.healthCheck(),
    ]);
    
    const ready = dbHealth.status === 'healthy' && redisHealth.status === 'healthy';
    
    res.status(ready ? 200 : 503).json({
      ready,
      timestamp: new Date().toISOString(),
      services: {
        database: dbHealth.status === 'healthy',
        redis: redisHealth.status === 'healthy',
      },
    });
  } catch (error) {
    logger.error('Readiness check failed:', error);
    res.status(503).json({
      ready: false,
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
}));

/**
 * @swagger
 * /health/liveness:
 *   get:
 *     summary: 存活检查
 *     description: 检查服务是否存活（Kubernetes存活探针）
 *     tags: [Health]
 *     security: []
 *     responses:
 *       200:
 *         description: 服务存活
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 alive:
 *                   type: boolean
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 uptime:
 *                   type: number
 */
router.get('/liveness', asyncHandler(async (req, res) => {
  // 简单的存活检查 - 如果能响应就说明服务存活
  res.json({
    alive: true,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    pid: process.pid,
  });
}));

/**
 * @swagger
 * components:
 *   schemas:
 *     ServiceHealth:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           enum: [healthy, unhealthy, degraded]
 *         responseTime:
 *           type: number
 *           description: 响应时间（毫秒）
 *         details:
 *           type: object
 *           description: 服务特定的详细信息
 *         error:
 *           type: string
 *           description: 错误信息（如果有）
 */

export default router;
