// ERP智能助手后端服务 - 向量数据库路由

import { Router } from 'express';
import { authMiddleware } from '@/middleware/auth';
import { asyncHand<PERSON> } from '@/middleware/error-handler';
import { validateRequest } from '@/middleware/validation';
import { semanticSearchService, SearchQuery } from '@/services/semantic-search';
import { vectorDatabaseService } from '@/services/vector-database';
import { vectorizationPipelineService } from '@/services/vectorization-pipeline';
import { similarityMatcherService } from '@/services/similarity-matcher';
import Joi from 'joi';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Vector
 *   description: 向量数据库操作和语义搜索
 */

// 所有向量路由都需要认证
router.use(authMiddleware);

// 验证模式
const searchSchema = Joi.object({
  text: Joi.string().min(1).max(1000).required(),
  type: Joi.string().valid('operation', 'documentation', 'error', 'guidance', 'all').optional(),
  filters: Joi.object({
    userId: Joi.string().uuid().optional(),
    sessionId: Joi.string().uuid().optional(),
    dateRange: Joi.object({
      start: Joi.date().iso().optional(),
      end: Joi.date().iso().optional(),
    }).optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    language: Joi.string().valid('en', 'zh').optional(),
    minQuality: Joi.number().min(0).max(1).optional(),
  }).optional(),
  options: Joi.object({
    limit: Joi.number().integer().min(1).max(100).default(10),
    threshold: Joi.number().min(0).max(1).default(0.7),
    rerank: Joi.boolean().default(false),
    expandQuery: Joi.boolean().default(false),
    includeMetadata: Joi.boolean().default(true),
  }).optional(),
});

const addDocumentSchema = Joi.object({
  collectionName: Joi.string().required(),
  documents: Joi.array().items(
    Joi.object({
      id: Joi.string().required(),
      content: Joi.string().required(),
      metadata: Joi.object().required(),
      embedding: Joi.array().items(Joi.number()).optional(),
    })
  ).min(1).required(),
});

const vectorizeSchema = Joi.object({
  type: Joi.string().valid('operation', 'documentation', 'error', 'guidance').required(),
  sourceId: Joi.string().required(),
  content: Joi.string().required(),
  metadata: Joi.object().default({}),
  priority: Joi.string().valid('low', 'medium', 'high').default('medium'),
});

const similaritySchema = Joi.object({
  sourceData: Joi.object().required(),
  config: Joi.object({
    algorithm: Joi.string().valid('cosine', 'euclidean', 'jaccard', 'hybrid').default('hybrid'),
    threshold: Joi.number().min(0).max(1).default(0.7),
    maxResults: Joi.number().integer().min(1).max(50).default(10),
    weights: Joi.object({
      semantic: Joi.number().min(0).max(1).default(0.4),
      structural: Joi.number().min(0).max(1).default(0.3),
      behavioral: Joi.number().min(0).max(1).default(0.2),
      temporal: Joi.number().min(0).max(1).default(0.1),
    }).optional(),
    features: Joi.array().items(
      Joi.string().valid('semantic', 'structural', 'behavioral', 'temporal')
    ).default(['semantic', 'structural', 'behavioral', 'temporal']),
  }).optional(),
});

/**
 * @swagger
 * /vector/search:
 *   post:
 *     summary: 语义搜索
 *     description: 在向量数据库中执行语义搜索
 *     tags: [Vector]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text
 *             properties:
 *               text:
 *                 type: string
 *                 description: 搜索查询文本
 *                 example: "如何点击登录按钮"
 *               type:
 *                 type: string
 *                 enum: [operation, documentation, error, guidance, all]
 *                 description: 搜索类型
 *               filters:
 *                 type: object
 *                 description: 搜索过滤器
 *               options:
 *                 type: object
 *                 description: 搜索选项
 *     responses:
 *       200:
 *         description: 搜索结果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: number
 *                 searchTime:
 *                   type: number
 */
router.post('/search',
  validateRequest({ body: searchSchema }),
  asyncHandler(async (req, res) => {
    const query: SearchQuery = req.body;
    const userId = req.user?.id;

    const results = await semanticSearchService.search(query, userId);

    res.json({
      results,
      total: results.length,
      searchTime: Date.now(),
    });
  })
);

/**
 * @swagger
 * /vector/suggestions:
 *   get:
 *     summary: 获取搜索建议
 *     description: 根据部分查询获取搜索建议
 *     tags: [Vector]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: 部分查询文本
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 20
 *           default: 5
 *         description: 建议数量限制
 *     responses:
 *       200:
 *         description: 搜索建议列表
 */
router.get('/suggestions',
  asyncHandler(async (req, res) => {
    const { q, limit = 5 } = req.query;

    if (!q || typeof q !== 'string') {
      return res.status(400).json({ error: 'Query parameter "q" is required' });
    }

    const suggestions = await semanticSearchService.getSuggestions(q, parseInt(limit as string));

    res.json({
      suggestions,
      query: q,
    });
  })
);

/**
 * @swagger
 * /vector/documents:
 *   post:
 *     summary: 添加文档到向量数据库
 *     description: 批量添加文档到指定集合
 *     tags: [Vector]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - collectionName
 *               - documents
 *             properties:
 *               collectionName:
 *                 type: string
 *                 example: "operations"
 *               documents:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     content:
 *                       type: string
 *                     metadata:
 *                       type: object
 *     responses:
 *       201:
 *         description: 文档添加成功
 */
router.post('/documents',
  validateRequest({ body: addDocumentSchema }),
  asyncHandler(async (req, res) => {
    const { collectionName, documents } = req.body;

    await vectorDatabaseService.addDocuments(collectionName, documents);

    res.status(201).json({
      message: 'Documents added successfully',
      collectionName,
      count: documents.length,
    });
  })
);

/**
 * @swagger
 * /vector/vectorize:
 *   post:
 *     summary: 向量化任务
 *     description: 添加内容到向量化处理队列
 *     tags: [Vector]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - sourceId
 *               - content
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [operation, documentation, error, guidance]
 *               sourceId:
 *                 type: string
 *               content:
 *                 type: string
 *               metadata:
 *                 type: object
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high]
 *     responses:
 *       202:
 *         description: 向量化任务已添加到队列
 */
router.post('/vectorize',
  validateRequest({ body: vectorizeSchema }),
  asyncHandler(async (req, res) => {
    const { type, sourceId, content, metadata, priority } = req.body;

    const taskId = await vectorizationPipelineService.addTask(
      type,
      sourceId,
      content,
      metadata,
      priority
    );

    res.status(202).json({
      message: 'Vectorization task added to queue',
      taskId,
      status: 'pending',
    });
  })
);

/**
 * @swagger
 * /vector/vectorize/batch:
 *   post:
 *     summary: 批量向量化任务
 *     description: 批量添加内容到向量化处理队列
 *     tags: [Vector]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tasks
 *             properties:
 *               tasks:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     type:
 *                       type: string
 *                       enum: [operation, documentation, error, guidance]
 *                     sourceId:
 *                       type: string
 *                     content:
 *                       type: string
 *                     metadata:
 *                       type: object
 *                     priority:
 *                       type: string
 *                       enum: [low, medium, high]
 *     responses:
 *       202:
 *         description: 批量向量化任务已添加到队列
 */
router.post('/vectorize/batch',
  asyncHandler(async (req, res) => {
    const { tasks } = req.body;

    if (!Array.isArray(tasks) || tasks.length === 0) {
      return res.status(400).json({ error: 'Tasks array is required and cannot be empty' });
    }

    const taskIds = await vectorizationPipelineService.addBatchTasks(tasks);

    res.status(202).json({
      message: 'Batch vectorization tasks added to queue',
      taskIds,
      count: taskIds.length,
    });
  })
);

/**
 * @swagger
 * /vector/vectorize/{taskId}:
 *   get:
 *     summary: 获取向量化任务状态
 *     description: 查询向量化任务的处理状态
 *     tags: [Vector]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: taskId
 *         required: true
 *         schema:
 *           type: string
 *         description: 任务ID
 *     responses:
 *       200:
 *         description: 任务状态信息
 *       404:
 *         description: 任务不存在
 */
router.get('/vectorize/:taskId',
  asyncHandler(async (req, res) => {
    const { taskId } = req.params;

    const task = await vectorizationPipelineService.getTaskStatus(taskId);

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    res.json({
      task,
    });
  })
);

/**
 * @swagger
 * /vector/similarity:
 *   post:
 *     summary: 相似度匹配
 *     description: 查找与给定数据相似的项目
 *     tags: [Vector]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sourceData
 *             properties:
 *               sourceData:
 *                 type: object
 *                 description: 源数据对象
 *               config:
 *                 type: object
 *                 description: 匹配配置
 *     responses:
 *       200:
 *         description: 相似度匹配结果
 */
router.post('/similarity',
  validateRequest({ body: similaritySchema }),
  asyncHandler(async (req, res) => {
    const { sourceData, config } = req.body;

    const matches = await similarityMatcherService.findSimilar(sourceData, config);

    res.json({
      matches,
      sourceId: sourceData.id,
      matchCount: matches.length,
    });
  })
);

/**
 * @swagger
 * /vector/collections:
 *   get:
 *     summary: 获取集合列表
 *     description: 获取所有向量数据库集合的信息
 *     tags: [Vector]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 集合列表
 */
router.get('/collections',
  asyncHandler(async (req, res) => {
    const collections = ['operations', 'documentation', 'errors', 'guidance'];
    const collectionStats = [];

    for (const collectionName of collections) {
      try {
        const stats = await vectorDatabaseService.getCollectionStats(collectionName);
        collectionStats.push(stats);
      } catch (error) {
        collectionStats.push({
          name: collectionName,
          count: 0,
          lastUpdated: 0,
          error: error.message,
        });
      }
    }

    res.json({
      collections: collectionStats,
      total: collectionStats.length,
    });
  })
);

/**
 * @swagger
 * /vector/stats:
 *   get:
 *     summary: 获取向量数据库统计信息
 *     description: 获取向量数据库和搜索服务的统计信息
 *     tags: [Vector]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 统计信息
 */
router.get('/stats',
  asyncHandler(async (req, res) => {
    const vectorStats = vectorDatabaseService.getStats();
    const searchStats = semanticSearchService.getStats();
    const pipelineStats = vectorizationPipelineService.getStats();
    const queueStatus = vectorizationPipelineService.getQueueStatus();

    res.json({
      vector: vectorStats,
      search: searchStats,
      pipeline: pipelineStats,
      queue: queueStatus,
      timestamp: new Date().toISOString(),
    });
  })
);

export default router;
