// ERP智能助手后端服务 - 用户管理路由

import { Router } from 'express';
import { authMiddleware, requireRole, UserRole } from '@/middleware/auth';
import { asyncHandler } from '@/middleware/error-handler';
import { validatePagination, validateId } from '@/middleware/validation';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: 用户管理
 */

// 所有用户路由都需要认证
router.use(authMiddleware);

/**
 * @swagger
 * /users:
 *   get:
 *     summary: 获取用户列表
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *     responses:
 *       200:
 *         description: 用户列表
 */
router.get('/', 
  requireRole(UserRole.ADMIN),
  validatePagination,
  asyncHandler(async (req, res) => {
    // TODO: 实现用户列表获取
    res.json({
      data: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    });
  })
);

/**
 * @swagger
 * /users/{id}:
 *   get:
 *     summary: 获取用户详情
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: 用户详情
 */
router.get('/:id',
  validateId,
  asyncHandler(async (req, res) => {
    // TODO: 实现用户详情获取
    res.json({
      user: {
        id: req.params.id,
        email: '<EMAIL>',
        username: 'user',
        role: 'user',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });
  })
);

export default router;
