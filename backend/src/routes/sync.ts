// ERP智能助手后端服务 - 数据同步路由

import { Router } from 'express';
import { authMiddleware } from '@/middleware/auth';
import { asyncHandler } from '@/middleware/error-handler';
import { validateRequest } from '@/middleware/validation';
import { dataSyncService, SyncOperation } from '@/services/data-sync';
import { offlineSyncService } from '@/services/offline-sync';
import { webSocketService } from '@/services/websocket';
import Joi from 'joi';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Sync
 *   description: 数据同步和离线处理
 */

// 所有同步路由都需要认证
router.use(authMiddleware);

// 验证模式
const syncRequestSchema = Joi.object({
  clientId: Joi.string().required(),
  changes: Joi.array().items(
    Joi.object({
      id: Joi.string().required(),
      entityType: Joi.string().required(),
      entityId: Joi.string().required(),
      operation: Joi.string().valid('create', 'update', 'delete', 'batch').required(),
      data: Joi.any().required(),
      previousData: Joi.any().optional(),
      timestamp: Joi.number().required(),
      version: Joi.number().required(),
      checksum: Joi.string().required(),
      metadata: Joi.object().default({}),
    })
  ).required(),
  lastSyncTimestamp: Joi.number().default(0),
  priority: Joi.string().valid('low', 'medium', 'high').default('medium'),
});

const incrementalSyncSchema = Joi.object({
  clientId: Joi.string().required(),
  lastSyncTimestamp: Joi.number().required(),
  entityTypes: Joi.array().items(Joi.string()).optional(),
});

const conflictResolutionSchema = Joi.object({
  conflictId: Joi.string().required(),
  resolution: Joi.string().valid('client', 'server', 'merge', 'manual').required(),
  mergedData: Joi.any().optional(),
});

const offlineOperationSchema = Joi.object({
  clientId: Joi.string().required(),
  operations: Joi.array().items(
    Joi.object({
      type: Joi.string().valid('create', 'update', 'delete', 'batch').required(),
      entityType: Joi.string().required(),
      entityId: Joi.string().required(),
      data: Joi.any().required(),
      previousData: Joi.any().optional(),
      timestamp: Joi.number().required(),
      version: Joi.number().required(),
      dependencies: Joi.array().items(Joi.string()).default([]),
    })
  ).required(),
});

/**
 * @swagger
 * /sync/request:
 *   post:
 *     summary: 请求数据同步
 *     description: 提交客户端变更并请求服务器端增量数据
 *     tags: [Sync]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - clientId
 *               - changes
 *             properties:
 *               clientId:
 *                 type: string
 *                 description: 客户端唯一标识
 *               changes:
 *                 type: array
 *                 description: 客户端变更记录
 *                 items:
 *                   type: object
 *               lastSyncTimestamp:
 *                 type: number
 *                 description: 上次同步时间戳
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high]
 *                 description: 同步优先级
 *     responses:
 *       200:
 *         description: 同步任务创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 taskId:
 *                   type: string
 *                 status:
 *                   type: string
 *                 incrementalChanges:
 *                   type: array
 *                 estimatedTime:
 *                   type: number
 */
router.post('/request',
  validateRequest({ body: syncRequestSchema }),
  asyncHandler(async (req, res) => {
    const { clientId, changes, lastSyncTimestamp, priority } = req.body;
    const userId = req.user!.id;

    // 创建同步任务
    const taskId = await dataSyncService.createSyncTask(
      userId,
      clientId,
      changes,
      priority
    );

    // 获取增量变更
    const incrementalChanges = await dataSyncService.getIncrementalChanges(
      userId,
      clientId,
      lastSyncTimestamp
    );

    // 估算处理时间
    const estimatedTime = changes.length * 100; // 每个变更100ms

    res.json({
      taskId,
      status: 'accepted',
      incrementalChanges,
      estimatedTime,
      serverTimestamp: Date.now(),
    });
  })
);

/**
 * @swagger
 * /sync/incremental:
 *   post:
 *     summary: 获取增量数据
 *     description: 获取指定时间戳之后的服务器端变更
 *     tags: [Sync]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - clientId
 *               - lastSyncTimestamp
 *             properties:
 *               clientId:
 *                 type: string
 *               lastSyncTimestamp:
 *                 type: number
 *               entityTypes:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 增量数据
 */
router.post('/incremental',
  validateRequest({ body: incrementalSyncSchema }),
  asyncHandler(async (req, res) => {
    const { clientId, lastSyncTimestamp, entityTypes } = req.body;
    const userId = req.user!.id;

    const changes = await dataSyncService.getIncrementalChanges(
      userId,
      clientId,
      lastSyncTimestamp,
      entityTypes
    );

    res.json({
      changes,
      serverTimestamp: Date.now(),
      hasMore: changes.length >= 100, // 分页标识
    });
  })
);

/**
 * @swagger
 * /sync/status/{taskId}:
 *   get:
 *     summary: 获取同步任务状态
 *     description: 查询同步任务的处理状态和结果
 *     tags: [Sync]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: taskId
 *         required: true
 *         schema:
 *           type: string
 *         description: 同步任务ID
 *     responses:
 *       200:
 *         description: 任务状态信息
 *       404:
 *         description: 任务不存在
 */
router.get('/status/:taskId',
  asyncHandler(async (req, res) => {
    const { taskId } = req.params;

    const task = dataSyncService.getSyncTaskStatus(taskId);

    if (!task) {
      return res.status(404).json({ error: 'Sync task not found' });
    }

    res.json({
      task,
      serverTimestamp: Date.now(),
    });
  })
);

/**
 * @swagger
 * /sync/conflicts:
 *   get:
 *     summary: 获取数据冲突列表
 *     description: 获取当前用户的未解决数据冲突
 *     tags: [Sync]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 冲突列表
 */
router.get('/conflicts',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;

    const conflicts = dataSyncService.getUnresolvedConflicts(userId);

    res.json({
      conflicts,
      total: conflicts.length,
    });
  })
);

/**
 * @swagger
 * /sync/conflicts/resolve:
 *   post:
 *     summary: 解决数据冲突
 *     description: 提供冲突解决方案并应用
 *     tags: [Sync]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - conflictId
 *               - resolution
 *             properties:
 *               conflictId:
 *                 type: string
 *               resolution:
 *                 type: string
 *                 enum: [client, server, merge, manual]
 *               mergedData:
 *                 type: object
 *                 description: 手动合并的数据（resolution为manual时必需）
 *     responses:
 *       200:
 *         description: 冲突解决成功
 */
router.post('/conflicts/resolve',
  validateRequest({ body: conflictResolutionSchema }),
  asyncHandler(async (req, res) => {
    const { conflictId, resolution, mergedData } = req.body;

    await dataSyncService.resolveConflict(conflictId, resolution, mergedData);

    res.json({
      message: 'Conflict resolved successfully',
      conflictId,
      resolution,
    });
  })
);

/**
 * @swagger
 * /sync/offline/queue:
 *   post:
 *     summary: 添加离线操作队列
 *     description: 将离线操作添加到同步队列
 *     tags: [Sync]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - clientId
 *               - operations
 *             properties:
 *               clientId:
 *                 type: string
 *               operations:
 *                 type: array
 *                 items:
 *                   type: object
 *     responses:
 *       201:
 *         description: 离线操作已添加到队列
 */
router.post('/offline/queue',
  validateRequest({ body: offlineOperationSchema }),
  asyncHandler(async (req, res) => {
    const { clientId, operations } = req.body;
    const userId = req.user!.id;

    const operationIds = await offlineSyncService.addBatchOfflineOperations(
      userId,
      clientId,
      operations
    );

    res.status(201).json({
      message: 'Offline operations queued successfully',
      operationIds,
      count: operationIds.length,
    });
  })
);

/**
 * @swagger
 * /sync/offline/sync:
 *   post:
 *     summary: 同步离线队列
 *     description: 触发离线队列的同步处理
 *     tags: [Sync]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - clientId
 *             properties:
 *               clientId:
 *                 type: string
 *     responses:
 *       200:
 *         description: 离线队列同步完成
 */
router.post('/offline/sync',
  asyncHandler(async (req, res) => {
    const { clientId } = req.body;
    const userId = req.user!.id;

    if (!clientId) {
      return res.status(400).json({ error: 'clientId is required' });
    }

    await offlineSyncService.syncOfflineQueue(userId, clientId);

    res.json({
      message: 'Offline queue sync completed',
      userId,
      clientId,
    });
  })
);

/**
 * @swagger
 * /sync/offline/status:
 *   get:
 *     summary: 获取离线状态
 *     description: 获取客户端的离线同步状态
 *     tags: [Sync]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *         description: 客户端ID
 *     responses:
 *       200:
 *         description: 离线状态信息
 */
router.get('/offline/status',
  asyncHandler(async (req, res) => {
    const { clientId } = req.query;
    const userId = req.user!.id;

    if (!clientId || typeof clientId !== 'string') {
      return res.status(400).json({ error: 'clientId is required' });
    }

    const status = await offlineSyncService.getOfflineStatus(userId, clientId);

    res.json({
      status,
      userId,
      clientId,
    });
  })
);

/**
 * @swagger
 * /sync/offline/merge:
 *   post:
 *     summary: 合并离线数据
 *     description: 合并本地和服务器端的数据变更
 *     tags: [Sync]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - entityType
 *               - localData
 *               - serverData
 *             properties:
 *               entityType:
 *                 type: string
 *               localData:
 *                 type: object
 *               serverData:
 *                 type: object
 *               baseData:
 *                 type: object
 *     responses:
 *       200:
 *         description: 数据合并结果
 */
router.post('/offline/merge',
  asyncHandler(async (req, res) => {
    const { entityType, localData, serverData, baseData } = req.body;

    if (!entityType || !localData || !serverData) {
      return res.status(400).json({ 
        error: 'entityType, localData, and serverData are required' 
      });
    }

    const result = await offlineSyncService.mergeOfflineData(
      entityType,
      localData,
      serverData,
      baseData
    );

    res.json({
      mergedData: result.mergedData,
      conflicts: result.conflicts,
      hasConflicts: result.conflicts.length > 0,
    });
  })
);

/**
 * @swagger
 * /sync/stats:
 *   get:
 *     summary: 获取同步统计信息
 *     description: 获取数据同步服务的统计信息
 *     tags: [Sync]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 同步统计信息
 */
router.get('/stats',
  asyncHandler(async (req, res) => {
    const syncStats = dataSyncService.getStats();
    const offlineStats = offlineSyncService.getQueueStats();
    const websocketStats = webSocketService.getStats();

    res.json({
      sync: syncStats,
      offline: offlineStats,
      websocket: websocketStats,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /sync/health:
 *   get:
 *     summary: 同步服务健康检查
 *     description: 检查数据同步服务的健康状态
 *     tags: [Sync]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 健康状态信息
 */
router.get('/health',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    const userConnections = webSocketService.getUserConnections(userId);

    const health = {
      status: 'healthy',
      services: {
        dataSync: 'healthy',
        offlineSync: 'healthy',
        websocket: 'healthy',
      },
      connections: {
        total: userConnections.length,
        active: userConnections.filter(conn => 
          Date.now() - conn.lastActivity.getTime() < 60000
        ).length,
      },
      timestamp: new Date().toISOString(),
    };

    res.json(health);
  })
);

export default router;
