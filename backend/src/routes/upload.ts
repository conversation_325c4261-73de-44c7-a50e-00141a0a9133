// ERP智能助手后端服务 - 文件上传路由

import { Router } from 'express';
import { authMiddleware } from '@/middleware/auth';
import { asyncHandler } from '@/middleware/error-handler';
import { uploadRateLimiter } from '@/middleware/rate-limiter';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Upload
 *   description: 文件上传管理
 */

// 所有上传路由都需要认证和速率限制
router.use(authMiddleware);
router.use(uploadRateLimiter);

/**
 * @swagger
 * /upload:
 *   post:
 *     summary: 上传文件
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 上传成功
 */
router.post('/',
  asyncHandler(async (req, res) => {
    // TODO: 实现文件上传
    res.json({
      file: {
        id: 'file-id',
        filename: 'example.jpg',
        url: '/uploads/example.jpg',
        size: 1024,
        uploadedAt: new Date(),
      },
    });
  })
);

export default router;
