// ERP智能助手后端服务 - 路由配置

import { Application, Router } from 'express';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { config } from '@/config';
import { logger } from '@/utils/logger';

// 导入路由模块
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/users';
import recordingRoutes from '@/routes/recordings';
import aiRoutes from '@/routes/ai';
import vectorRoutes from '@/routes/vector';
import healthRoutes from '@/routes/health';
import uploadRoutes from '@/routes/upload';

// Swagger配置
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'ERP智能助手 API',
      version: process.env.npm_package_version || '1.0.0',
      description: '基于AI大模型的企业ERP系统用户体验增强服务API文档',
      contact: {
        name: 'ERP Assistant Team',
        email: '<EMAIL>',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: `http://${config.host}:${config.port}${config.api.prefix}/${config.api.version}`,
        description: '开发环境',
      },
      {
        url: `https://api.erpassistant.com${config.api.prefix}/${config.api.version}`,
        description: '生产环境',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
        apiKey: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
        },
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                code: { type: 'string' },
                statusCode: { type: 'number' },
                timestamp: { type: 'string' },
                path: { type: 'string' },
                method: { type: 'string' },
              },
            },
          },
        },
        User: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            email: { type: 'string', format: 'email' },
            username: { type: 'string' },
            role: { type: 'string', enum: ['admin', 'user', 'viewer'] },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        RecordingSession: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            title: { type: 'string' },
            description: { type: 'string' },
            userId: { type: 'string', format: 'uuid' },
            status: { type: 'string', enum: ['active', 'completed', 'failed'] },
            startTime: { type: 'string', format: 'date-time' },
            endTime: { type: 'string', format: 'date-time' },
            operationCount: { type: 'number' },
            tags: { type: 'array', items: { type: 'string' } },
            isPublic: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        Pagination: {
          type: 'object',
          properties: {
            page: { type: 'number', minimum: 1 },
            limit: { type: 'number', minimum: 1, maximum: 100 },
            total: { type: 'number' },
            totalPages: { type: 'number' },
            hasNext: { type: 'boolean' },
            hasPrev: { type: 'boolean' },
          },
        },
      },
    },
    security: [
      { bearerAuth: [] },
    ],
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts',
  ],
};

// 生成Swagger文档
const swaggerSpec = swaggerJsdoc(swaggerOptions);

/**
 * 设置应用路由
 */
export function setupRoutes(app: Application): void {
  logger.info('Setting up application routes...');

  // API版本前缀
  const apiPrefix = `${config.api.prefix}/${config.api.version}`;

  // 创建主路由器
  const apiRouter = Router();

  // 健康检查路由（无需认证）
  apiRouter.use('/health', healthRoutes);

  // 认证路由
  apiRouter.use('/auth', authRoutes);

  // 用户路由
  apiRouter.use('/users', userRoutes);

  // 录制会话路由
  apiRouter.use('/recordings', recordingRoutes);

  // AI服务路由
  apiRouter.use('/ai', aiRoutes);

  // 向量数据库路由
  apiRouter.use('/vector', vectorRoutes);

  // 文件上传路由
  apiRouter.use('/upload', uploadRoutes);

  // 挂载API路由
  app.use(apiPrefix, apiRouter);

  // Swagger文档路由
  if (config.development.swaggerEnabled) {
    app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
      customSiteTitle: 'ERP智能助手 API文档',
      swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
      },
    }));

    // Swagger JSON端点
    app.get('/api/docs.json', (req, res) => {
      res.setHeader('Content-Type', 'application/json');
      res.send(swaggerSpec);
    });

    logger.info('Swagger documentation enabled', {
      docsUrl: `http://${config.host}:${config.port}/api/docs`,
      jsonUrl: `http://${config.host}:${config.port}/api/docs.json`,
    });
  }

  // API信息端点
  apiRouter.get('/', (req, res) => {
    res.json({
      name: 'ERP智能助手 API',
      version: process.env.npm_package_version || '1.0.0',
      description: '基于AI大模型的企业ERP系统用户体验增强服务',
      environment: config.env,
      timestamp: new Date().toISOString(),
      endpoints: {
        auth: `${apiPrefix}/auth`,
        users: `${apiPrefix}/users`,
        recordings: `${apiPrefix}/recordings`,
        ai: `${apiPrefix}/ai`,
        vector: `${apiPrefix}/vector`,
        upload: `${apiPrefix}/upload`,
        health: `${apiPrefix}/health`,
        docs: config.development.swaggerEnabled ? '/api/docs' : null,
      },
    });
  });

  // 路由统计中间件
  apiRouter.use((req, res, next) => {
    const startTime = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      logger.debug('API request completed', {
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        duration,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
      });
    });
    
    next();
  });

  logger.info('Application routes setup completed', {
    apiPrefix,
    routesCount: getRouteCount(apiRouter),
  });
}

/**
 * 获取路由数量
 */
function getRouteCount(router: Router): number {
  let count = 0;
  
  function countRoutes(layer: any) {
    if (layer.route) {
      count++;
    } else if (layer.name === 'router' && layer.handle.stack) {
      layer.handle.stack.forEach(countRoutes);
    }
  }
  
  (router as any).stack.forEach(countRoutes);
  return count;
}

/**
 * 获取所有路由信息
 */
export function getRouteInfo(app: Application): Array<{
  method: string;
  path: string;
  middleware: string[];
}> {
  const routes: Array<{ method: string; path: string; middleware: string[] }> = [];
  
  function extractRoutes(stack: any[], basePath = '') {
    stack.forEach((layer) => {
      if (layer.route) {
        // 直接路由
        const methods = Object.keys(layer.route.methods);
        methods.forEach((method) => {
          routes.push({
            method: method.toUpperCase(),
            path: basePath + layer.route.path,
            middleware: layer.route.stack.map((s: any) => s.name || 'anonymous'),
          });
        });
      } else if (layer.name === 'router' && layer.handle.stack) {
        // 嵌套路由器
        const routerPath = layer.regexp.source
          .replace('\\', '')
          .replace('(?=\\/|$)', '')
          .replace('^', '');
        extractRoutes(layer.handle.stack, basePath + routerPath);
      }
    });
  }
  
  extractRoutes((app as any)._router.stack);
  return routes;
}

/**
 * 路由性能统计
 */
export class RouteStats {
  private static stats = new Map<string, {
    count: number;
    totalTime: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
    errors: number;
  }>();

  static recordRequest(path: string, method: string, duration: number, error = false) {
    const key = `${method} ${path}`;
    const current = this.stats.get(key) || {
      count: 0,
      totalTime: 0,
      averageTime: 0,
      minTime: Infinity,
      maxTime: 0,
      errors: 0,
    };

    current.count++;
    current.totalTime += duration;
    current.averageTime = current.totalTime / current.count;
    current.minTime = Math.min(current.minTime, duration);
    current.maxTime = Math.max(current.maxTime, duration);
    
    if (error) {
      current.errors++;
    }

    this.stats.set(key, current);
  }

  static getStats() {
    return Object.fromEntries(this.stats);
  }

  static getTopRoutes(limit = 10) {
    return Array.from(this.stats.entries())
      .sort(([, a], [, b]) => b.count - a.count)
      .slice(0, limit)
      .map(([route, stats]) => ({ route, ...stats }));
  }

  static getSlowestRoutes(limit = 10) {
    return Array.from(this.stats.entries())
      .sort(([, a], [, b]) => b.averageTime - a.averageTime)
      .slice(0, limit)
      .map(([route, stats]) => ({ route, ...stats }));
  }

  static reset() {
    this.stats.clear();
  }
}
