// ERP智能助手后端服务 - 录制会话路由

import { Router } from 'express';
import { authMiddleware } from '@/middleware/auth';
import { asyncHandler } from '@/middleware/error-handler';
import { validatePagination, validateRecordingSession } from '@/middleware/validation';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Recordings
 *   description: 录制会话管理
 */

// 所有录制路由都需要认证
router.use(authMiddleware);

/**
 * @swagger
 * /recordings:
 *   get:
 *     summary: 获取录制会话列表
 *     tags: [Recordings]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 录制会话列表
 */
router.get('/',
  validatePagination,
  asyncHandler(async (req, res) => {
    // TODO: 实现录制会话列表获取
    res.json({
      data: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    });
  })
);

/**
 * @swagger
 * /recordings:
 *   post:
 *     summary: 创建录制会话
 *     tags: [Recordings]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       201:
 *         description: 录制会话创建成功
 */
router.post('/',
  validateRecordingSession,
  asyncHandler(async (req, res) => {
    // TODO: 实现录制会话创建
    res.status(201).json({
      session: {
        id: 'session-id',
        title: req.body.title,
        status: 'active',
        createdAt: new Date(),
      },
    });
  })
);

export default router;
