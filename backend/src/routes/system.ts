// ERP智能助手后端服务 - 系统管理路由

import { Router } from 'express';
import { authMiddleware, requireRole } from '@/middleware/auth';
import { asyncHandler } from '@/middleware/error-handler';
import { validateRequest } from '@/middleware/validation';
import { securityService } from '@/services/security';
import { performanceMonitorService } from '@/services/performance-monitor';
import { cacheOptimizerService } from '@/services/cache-optimizer';
import { healthCheckService } from '@/services/health-check';
import Joi from 'joi';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: System
 *   description: 系统管理、安全和性能监控
 */

// 所有系统路由都需要认证
router.use(authMiddleware);

// 验证模式
const passwordChangeSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().min(8).required(),
});

const conflictResolutionSchema = Joi.object({
  conflictId: Joi.string().required(),
  resolution: Joi.string().valid('client', 'server', 'merge', 'manual').required(),
  mergedData: Joi.any().optional(),
});

const cacheOperationSchema = Joi.object({
  keys: Joi.array().items(Joi.string()).optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  levels: Joi.array().items(Joi.string().valid('l1_memory', 'l2_redis')).optional(),
});

/**
 * @swagger
 * /system/health:
 *   get:
 *     summary: 获取系统健康状态
 *     description: 获取完整的系统健康检查报告
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 系统健康报告
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 overall:
 *                   type: string
 *                   enum: [healthy, degraded, unhealthy, critical]
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 uptime:
 *                   type: number
 *                 checks:
 *                   type: array
 *                   items:
 *                     type: object
 */
router.get('/health',
  asyncHandler(async (req, res) => {
    const healthReport = await healthCheckService.performHealthCheck();
    
    res.json({
      ...healthReport,
      requestId: req.headers['x-request-id'],
    });
  })
);

/**
 * @swagger
 * /system/health/{serviceName}:
 *   get:
 *     summary: 获取特定服务健康状态
 *     description: 获取指定服务的健康检查状态
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceName
 *         required: true
 *         schema:
 *           type: string
 *         description: 服务名称
 *     responses:
 *       200:
 *         description: 服务健康状态
 *       404:
 *         description: 服务不存在
 */
router.get('/health/:serviceName',
  asyncHandler(async (req, res) => {
    const { serviceName } = req.params;
    
    const serviceHealth = healthCheckService.getServiceHealth(serviceName);
    
    if (!serviceHealth) {
      return res.status(404).json({ error: 'Service not found' });
    }
    
    res.json({
      service: serviceName,
      ...serviceHealth,
    });
  })
);

/**
 * @swagger
 * /system/performance:
 *   get:
 *     summary: 获取性能统计
 *     description: 获取系统性能监控统计信息
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 性能统计信息
 */
router.get('/performance',
  requireRole('admin'),
  asyncHandler(async (req, res) => {
    const performanceStats = await performanceMonitorService.getPerformanceStats();
    const activeAlerts = performanceMonitorService.getActiveAlerts();
    
    res.json({
      stats: performanceStats,
      alerts: activeAlerts,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /system/performance/alerts:
 *   get:
 *     summary: 获取性能告警
 *     description: 获取当前活跃的性能告警列表
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 性能告警列表
 */
router.get('/performance/alerts',
  requireRole('admin'),
  asyncHandler(async (req, res) => {
    const alerts = performanceMonitorService.getActiveAlerts();
    
    res.json({
      alerts,
      count: alerts.length,
    });
  })
);

/**
 * @swagger
 * /system/performance/alerts/{alertId}/resolve:
 *   post:
 *     summary: 解决性能告警
 *     description: 标记指定的性能告警为已解决
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: alertId
 *         required: true
 *         schema:
 *           type: string
 *         description: 告警ID
 *     responses:
 *       200:
 *         description: 告警已解决
 */
router.post('/performance/alerts/:alertId/resolve',
  requireRole('admin'),
  asyncHandler(async (req, res) => {
    const { alertId } = req.params;
    
    await performanceMonitorService.resolveAlert(alertId);
    
    res.json({
      message: 'Alert resolved successfully',
      alertId,
    });
  })
);

/**
 * @swagger
 * /system/cache/stats:
 *   get:
 *     summary: 获取缓存统计
 *     description: 获取多层缓存的统计信息
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 缓存统计信息
 */
router.get('/cache/stats',
  requireRole('admin'),
  asyncHandler(async (req, res) => {
    const cacheStats = cacheOptimizerService.getCacheStats();
    const hotspots = cacheOptimizerService.getCacheHotspots();
    
    const statsObject = Object.fromEntries(cacheStats);
    
    res.json({
      stats: statsObject,
      hotspots,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /system/cache/optimize:
 *   post:
 *     summary: 优化缓存
 *     description: 触发缓存优化过程
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 缓存优化已启动
 */
router.post('/cache/optimize',
  requireRole('admin'),
  asyncHandler(async (req, res) => {
    // 异步执行缓存优化
    cacheOptimizerService.optimizeCache().catch(error => {
      console.error('Cache optimization failed:', error);
    });
    
    res.json({
      message: 'Cache optimization started',
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /system/cache/clear:
 *   post:
 *     summary: 清理缓存
 *     description: 清理指定的缓存项或标签
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               keys:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 要清理的缓存键
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 要清理的缓存标签
 *               levels:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [l1_memory, l2_redis]
 *                 description: 要清理的缓存层级
 *     responses:
 *       200:
 *         description: 缓存清理完成
 */
router.post('/cache/clear',
  requireRole('admin'),
  validateRequest({ body: cacheOperationSchema }),
  asyncHandler(async (req, res) => {
    const { keys, tags, levels } = req.body;
    let clearedCount = 0;

    if (keys && keys.length > 0) {
      // 清理指定键
      for (const key of keys) {
        await cacheOptimizerService.delete(key, levels);
        clearedCount++;
      }
    }

    if (tags && tags.length > 0) {
      // 清理指定标签
      const tagClearedCount = await cacheOptimizerService.deleteByTags(tags, levels);
      clearedCount += tagClearedCount;
    }

    res.json({
      message: 'Cache cleared successfully',
      clearedCount,
      keys: keys || [],
      tags: tags || [],
      levels: levels || ['l1_memory', 'l2_redis'],
    });
  })
);

/**
 * @swagger
 * /system/security/stats:
 *   get:
 *     summary: 获取安全统计
 *     description: 获取系统安全统计信息
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 安全统计信息
 */
router.get('/security/stats',
  requireRole('admin'),
  asyncHandler(async (req, res) => {
    const securityStats = await securityService.getSecurityStats();
    
    res.json({
      ...securityStats,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /system/security/events:
 *   get:
 *     summary: 获取安全事件
 *     description: 获取最近的安全事件列表
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: 返回事件数量限制
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *         description: 事件严重性过滤
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: 事件类型过滤
 *     responses:
 *       200:
 *         description: 安全事件列表
 */
router.get('/security/events',
  requireRole('admin'),
  asyncHandler(async (req, res) => {
    const { limit = 50, severity, type } = req.query;
    
    // 这里应该从数据库查询安全事件
    // 简化实现，返回空数组
    const events = [];
    
    res.json({
      events,
      total: events.length,
      filters: {
        limit: parseInt(limit as string),
        severity,
        type,
      },
    });
  })
);

/**
 * @swagger
 * /system/security/password:
 *   post:
 *     summary: 修改密码
 *     description: 修改当前用户的密码
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *                 description: 当前密码
 *               newPassword:
 *                 type: string
 *                 minLength: 8
 *                 description: 新密码
 *     responses:
 *       200:
 *         description: 密码修改成功
 *       400:
 *         description: 密码验证失败
 */
router.post('/security/password',
  validateRequest({ body: passwordChangeSchema }),
  asyncHandler(async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user!.id;

    // 验证密码强度
    const passwordValidation = securityService.validatePasswordStrength(newPassword);
    if (!passwordValidation.valid) {
      return res.status(400).json({
        error: 'Password does not meet requirements',
        details: passwordValidation.errors,
      });
    }

    // 这里应该验证当前密码并更新新密码
    // 简化实现
    const hashedPassword = await securityService.hashPassword(newPassword);
    
    // 记录密码修改事件
    await securityService.logSecurityEvent({
      type: 'password_change' as any,
      userId,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent') || '',
      severity: 'low',
      details: { userId },
    });

    res.json({
      message: 'Password changed successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /system/security/sessions:
 *   get:
 *     summary: 获取用户会话
 *     description: 获取当前用户的活跃会话列表
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 用户会话列表
 */
router.get('/security/sessions',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    
    // 这里应该从数据库查询用户会话
    // 简化实现
    const sessions = [];
    
    res.json({
      sessions,
      total: sessions.length,
      userId,
    });
  })
);

/**
 * @swagger
 * /system/metrics:
 *   get:
 *     summary: 获取系统指标
 *     description: 获取系统运行指标的汇总信息
 *     tags: [System]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 系统指标汇总
 */
router.get('/metrics',
  requireRole('admin'),
  asyncHandler(async (req, res) => {
    const [
      healthReport,
      performanceStats,
      cacheStats,
      securityStats,
    ] = await Promise.all([
      healthCheckService.getLatestHealthReport(),
      performanceMonitorService.getPerformanceStats(),
      cacheOptimizerService.getCacheStats(),
      securityService.getSecurityStats(),
    ]);

    res.json({
      health: {
        overall: healthReport?.overall || 'unknown',
        uptime: healthReport?.uptime || 0,
        checksCount: healthReport?.checks.length || 0,
      },
      performance: {
        responseTime: performanceStats.applicationMetrics.responseTime.avg,
        throughput: performanceStats.applicationMetrics.throughput,
        errorRate: performanceStats.applicationMetrics.errorRate,
        cpuUsage: performanceStats.systemMetrics.cpuUsage,
        memoryUsage: performanceStats.systemMetrics.memoryUsage,
      },
      cache: {
        levels: Object.fromEntries(cacheStats),
        totalHitRate: Array.from(cacheStats.values())
          .reduce((sum, stat) => sum + stat.hitRate, 0) / cacheStats.size,
      },
      security: {
        activeSessions: securityStats.activeSessions,
        recentEventsCount: securityStats.recentEvents.length,
        lockoutCount: securityStats.lockoutCount,
      },
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /system/status:
 *   get:
 *     summary: 获取系统状态
 *     description: 获取系统基本状态信息（轻量级健康检查）
 *     tags: [System]
 *     responses:
 *       200:
 *         description: 系统状态
 */
router.get('/status',
  asyncHandler(async (req, res) => {
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();
    
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
      },
      pid: process.pid,
    });
  })
);

export default router;
