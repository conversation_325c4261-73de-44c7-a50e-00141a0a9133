// ERP智能助手后端服务 - 主入口文件

import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { createServer } from 'http';

import { config } from '@/config/index';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/error-handler';
import { rateLimiter } from '@/middleware/rate-limiter';
import { authMiddleware } from '@/middleware/auth';
import { validateRequest } from '@/middleware/validation';
import { setupRoutes } from '@/routes';
import { DatabaseService } from '@/services/database';
import { RedisService } from '@/services/redis';
import { VectorService } from '@/services/vector';
import { HealthService } from '@/services/health';

class ERPAssistantServer {
  private app: express.Application;
  private server: any;
  private databaseService: DatabaseService;
  private redisService: RedisService;
  private vectorService: VectorService;
  private healthService: HealthService;

  constructor() {
    this.app = express();
    this.databaseService = new DatabaseService();
    this.redisService = new RedisService();
    this.vectorService = new VectorService();
    this.healthService = new HealthService();
  }

  /**
   * 初始化服务器
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing ERP Assistant Backend Server...');

      // 初始化服务
      await this.initializeServices();

      // 配置中间件
      this.setupMiddleware();

      // 配置路由
      this.setupRoutes();

      // 配置错误处理
      this.setupErrorHandling();

      logger.info('Server initialization completed successfully');
    } catch (error) {
      logger.error('Failed to initialize server:', error);
      throw error;
    }
  }

  /**
   * 初始化服务
   */
  private async initializeServices(): Promise<void> {
    logger.info('Initializing services...');

    // 初始化数据库
    await this.databaseService.initialize();
    logger.info('Database service initialized');

    // 初始化Redis
    await this.redisService.initialize();
    logger.info('Redis service initialized');

    // 初始化向量数据库
    await this.vectorService.initialize();
    logger.info('Vector service initialized');

    // 初始化健康检查服务
    await this.healthService.initialize();
    logger.info('Health service initialized');
  }

  /**
   * 配置中间件
   */
  private setupMiddleware(): void {
    logger.info('Setting up middleware...');

    // 安全中间件
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
      crossOriginEmbedderPolicy: false,
    }));

    // CORS配置
    this.app.use(cors({
      origin: config.cors.origins,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // 压缩中间件
    this.app.use(compression());

    // 请求日志
    this.app.use(morgan('combined', {
      stream: {
        write: (message: string) => logger.info(message.trim())
      }
    }));

    // 请求解析
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 速率限制
    this.app.use(rateLimiter);

    // 健康检查端点（无需认证）
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        environment: config.env
      });
    });

    // API版本信息
    this.app.get('/', (req, res) => {
      res.json({
        name: 'ERP智能助手后端服务',
        version: process.env.npm_package_version || '1.0.0',
        description: '基于AI大模型的企业ERP系统用户体验增强服务',
        endpoints: {
          health: '/health',
          api: '/api/v1',
          docs: '/api/docs'
        }
      });
    });
  }

  /**
   * 配置路由
   */
  private setupRoutes(): void {
    logger.info('Setting up routes...');
    setupRoutes(this.app);
  }

  /**
   * 配置错误处理
   */
  private setupErrorHandling(): void {
    // 404处理
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`,
        timestamp: new Date().toISOString()
      });
    });

    // 全局错误处理
    this.app.use(errorHandler);
  }

  /**
   * 启动服务器
   */
  async start(): Promise<void> {
    try {
      await this.initialize();

      this.server = createServer(this.app);

      this.server.listen(config.port, config.host, () => {
        logger.info(`🚀 ERP Assistant Backend Server started successfully!`);
        logger.info(`📍 Server running at http://${config.host}:${config.port}`);
        logger.info(`🌍 Environment: ${config.env}`);
        logger.info(`📚 API Documentation: http://${config.host}:${config.port}/api/docs`);
        logger.info(`❤️  Health Check: http://${config.host}:${config.port}/health`);
      });

      // 优雅关闭处理
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  /**
   * 配置优雅关闭
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      if (this.server) {
        this.server.close(async () => {
          logger.info('HTTP server closed');

          try {
            // 关闭数据库连接
            await this.databaseService.close();
            logger.info('Database connections closed');

            // 关闭Redis连接
            await this.redisService.close();
            logger.info('Redis connections closed');

            // 关闭向量数据库连接
            await this.vectorService.close();
            logger.info('Vector database connections closed');

            logger.info('Graceful shutdown completed');
            process.exit(0);
          } catch (error) {
            logger.error('Error during graceful shutdown:', error);
            process.exit(1);
          }
        });
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
  }
}

// 启动服务器
const server = new ERPAssistantServer();
server.start().catch((error) => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});

export { ERPAssistantServer };
