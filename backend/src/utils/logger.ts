// ERP智能助手后端服务 - 日志工具

import winston from 'winston';
import path from 'path';
import fs from 'fs';
import { config } from '@/config';

// 确保日志目录存在
const logDir = path.dirname(config.logging.file);
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    // 添加堆栈信息（如果有错误）
    if (stack) {
      log += `\n${stack}`;
    }
    
    // 添加元数据
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// 控制台格式（开发环境）
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, stack }) => {
    let log = `${timestamp} ${level}: ${message}`;
    if (stack) {
      log += `\n${stack}`;
    }
    return log;
  })
);

// 创建传输器
const transports: winston.transport[] = [];

// 文件传输器
transports.push(
  new winston.transports.File({
    filename: config.logging.file,
    level: config.logging.level,
    format: logFormat,
    maxsize: parseSize(config.logging.maxSize),
    maxFiles: config.logging.maxFiles,
    tailable: true,
  })
);

// 错误日志文件
transports.push(
  new winston.transports.File({
    filename: path.join(logDir, 'error.log'),
    level: 'error',
    format: logFormat,
    maxsize: parseSize(config.logging.maxSize),
    maxFiles: config.logging.maxFiles,
    tailable: true,
  })
);

// 控制台传输器（开发环境）
if (config.isDevelopment) {
  transports.push(
    new winston.transports.Console({
      level: config.logging.level,
      format: consoleFormat,
    })
  );
}

// 创建日志器
export const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  transports,
  exitOnError: false,
  // 处理未捕获的异常
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log'),
      format: logFormat,
    })
  ],
  // 处理未处理的Promise拒绝
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'rejections.log'),
      format: logFormat,
    })
  ]
});

// 解析文件大小字符串
function parseSize(sizeStr: string): number {
  const units: { [key: string]: number } = {
    'b': 1,
    'k': 1024,
    'm': 1024 * 1024,
    'g': 1024 * 1024 * 1024,
  };
  
  const match = sizeStr.toLowerCase().match(/^(\d+)([bkmg]?)$/);
  if (!match) {
    return 10 * 1024 * 1024; // 默认10MB
  }
  
  const [, size, unit] = match;
  return parseInt(size, 10) * (units[unit] || 1);
}

// 创建子日志器
export function createChildLogger(service: string): winston.Logger {
  return logger.child({ service });
}

// 日志级别
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

// 结构化日志接口
export interface LogContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  ip?: string;
  userAgent?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  responseTime?: number;
  error?: Error;
  [key: string]: any;
}

// 扩展日志器类
export class Logger {
  private logger: winston.Logger;
  private context: LogContext;

  constructor(service: string, context: LogContext = {}) {
    this.logger = createChildLogger(service);
    this.context = context;
  }

  private log(level: LogLevel, message: string, meta: LogContext = {}): void {
    this.logger.log(level, message, { ...this.context, ...meta });
  }

  error(message: string, error?: Error | LogContext, meta?: LogContext): void {
    if (error instanceof Error) {
      this.log(LogLevel.ERROR, message, { error, ...meta });
    } else {
      this.log(LogLevel.ERROR, message, { ...error, ...meta });
    }
  }

  warn(message: string, meta?: LogContext): void {
    this.log(LogLevel.WARN, message, meta);
  }

  info(message: string, meta?: LogContext): void {
    this.log(LogLevel.INFO, message, meta);
  }

  debug(message: string, meta?: LogContext): void {
    this.log(LogLevel.DEBUG, message, meta);
  }

  // 请求日志
  request(req: any, res: any, responseTime: number): void {
    this.info('HTTP Request', {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      statusCode: res.statusCode,
      responseTime,
      userId: req.user?.id,
      sessionId: req.sessionId,
    });
  }

  // 数据库查询日志
  query(sql: string, params: any[], duration: number): void {
    this.debug('Database Query', {
      sql: sql.replace(/\s+/g, ' ').trim(),
      params,
      duration,
    });
  }

  // AI服务调用日志
  aiCall(service: string, model: string, tokens: number, duration: number): void {
    this.info('AI Service Call', {
      service,
      model,
      tokens,
      duration,
    });
  }

  // 缓存操作日志
  cache(operation: string, key: string, hit?: boolean): void {
    this.debug('Cache Operation', {
      operation,
      key,
      hit,
    });
  }

  // 更新上下文
  setContext(context: LogContext): void {
    this.context = { ...this.context, ...context };
  }

  // 创建子日志器
  child(context: LogContext): Logger {
    return new Logger(this.logger.defaultMeta?.service || 'unknown', {
      ...this.context,
      ...context,
    });
  }
}

// 默认导出
export default logger;

// 性能监控日志
export class PerformanceLogger {
  private startTime: number;
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
    this.startTime = Date.now();
  }

  static start(logger: Logger): PerformanceLogger {
    return new PerformanceLogger(logger);
  }

  end(operation: string, meta?: LogContext): void {
    const duration = Date.now() - this.startTime;
    this.logger.debug(`Performance: ${operation}`, {
      duration,
      ...meta,
    });
  }
}

// 错误日志助手
export function logError(error: Error, context?: LogContext): void {
  logger.error(error.message, error, context);
}

// 审计日志
export function auditLog(action: string, userId: string, resource: string, meta?: LogContext): void {
  logger.info(`Audit: ${action}`, {
    userId,
    resource,
    action,
    timestamp: new Date().toISOString(),
    ...meta,
  });
}
