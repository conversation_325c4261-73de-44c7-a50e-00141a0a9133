// ERP智能助手后端服务 - 配置管理

// 注意：这里暂时不使用zod，使用简单的环境变量解析

// 环境变量解析函数
function getEnvVar(key: string, defaultValue?: string): string {
  return process.env[key] || defaultValue || '';
}

function getEnvNumber(key: string, defaultValue: number): number {
  const value = process.env[key];
  return value ? parseInt(value, 10) : defaultValue;
}

function getEnvBoolean(key: string, defaultValue: boolean): boolean {
  const value = process.env[key];
  return value ? value === 'true' : defaultValue;
}

// 环境变量配置
const env = {
  // 服务器配置
  NODE_ENV: getEnvVar('NODE_ENV', 'development') as 'development' | 'production' | 'test',
  PORT: getEnvNumber('PORT', 3000),
  HOST: getEnvVar('HOST', 'localhost'),

  // 数据库配置
  DB_HOST: getEnvVar('DB_HOST', 'localhost'),
  DB_PORT: getEnvNumber('DB_PORT', 5432),
  DB_NAME: getEnvVar('DB_NAME', 'erp_assistant'),
  DB_USER: getEnvVar('DB_USER', 'postgres'),
  DB_PASSWORD: getEnvVar('DB_PASSWORD'),
  DB_SSL: getEnvBoolean('DB_SSL', false),
  DB_POOL_MIN: getEnvNumber('DB_POOL_MIN', 2),
  DB_POOL_MAX: getEnvNumber('DB_POOL_MAX', 10),

  // Redis配置
  REDIS_HOST: getEnvVar('REDIS_HOST', 'localhost'),
  REDIS_PORT: getEnvNumber('REDIS_PORT', 6379),
  REDIS_PASSWORD: getEnvVar('REDIS_PASSWORD'),
  REDIS_DB: getEnvNumber('REDIS_DB', 0),

  // JWT配置
  JWT_SECRET: getEnvVar('JWT_SECRET'),
  JWT_EXPIRES_IN: getEnvVar('JWT_EXPIRES_IN', '7d'),
  JWT_REFRESH_SECRET: getEnvVar('JWT_REFRESH_SECRET'),
  JWT_REFRESH_EXPIRES_IN: getEnvVar('JWT_REFRESH_EXPIRES_IN', '30d'),

  // 加密配置
  BCRYPT_ROUNDS: getEnvNumber('BCRYPT_ROUNDS', 12),
  ENCRYPTION_KEY: getEnvVar('ENCRYPTION_KEY'),

  // AI服务配置
  OPENAI_API_KEY: getEnvVar('OPENAI_API_KEY'),
  OPENAI_MODEL: getEnvVar('OPENAI_MODEL', 'gpt-4'),
  OPENAI_MAX_TOKENS: getEnvNumber('OPENAI_MAX_TOKENS', 2000),
  OPENAI_TEMPERATURE: parseFloat(getEnvVar('OPENAI_TEMPERATURE', '0.7')),

  ANTHROPIC_API_KEY: getEnvVar('ANTHROPIC_API_KEY'),
  ANTHROPIC_MODEL: getEnvVar('ANTHROPIC_MODEL', 'claude-3-sonnet-20240229'),
  ANTHROPIC_MAX_TOKENS: getEnvNumber('ANTHROPIC_MAX_TOKENS', 2000),

  // ChromaDB配置
  CHROMA_HOST: getEnvVar('CHROMA_HOST', 'localhost'),
  CHROMA_PORT: getEnvNumber('CHROMA_PORT', 8000),
  CHROMA_COLLECTION_NAME: getEnvVar('CHROMA_COLLECTION_NAME', 'erp_assistant_vectors'),
  CHROMA_DISTANCE_FUNCTION: getEnvVar('CHROMA_DISTANCE_FUNCTION', 'cosine') as 'cosine' | 'euclidean' | 'manhattan',

  // 文件上传配置
  UPLOAD_MAX_SIZE: getEnvNumber('UPLOAD_MAX_SIZE', 10485760), // 10MB
  UPLOAD_ALLOWED_TYPES: getEnvVar('UPLOAD_ALLOWED_TYPES', 'image/jpeg,image/png,image/gif,application/pdf,text/plain'),
  UPLOAD_DEST: getEnvVar('UPLOAD_DEST', 'uploads/'),

  // 日志配置
  LOG_LEVEL: getEnvVar('LOG_LEVEL', 'info') as 'error' | 'warn' | 'info' | 'debug',
  LOG_FILE: getEnvVar('LOG_FILE', 'logs/app.log'),
  LOG_MAX_SIZE: getEnvVar('LOG_MAX_SIZE', '10m'),
  LOG_MAX_FILES: getEnvNumber('LOG_MAX_FILES', 5),

  // 安全配置
  CORS_ORIGIN: getEnvVar('CORS_ORIGIN', 'http://localhost:3000,chrome-extension://*'),
  RATE_LIMIT_WINDOW_MS: getEnvNumber('RATE_LIMIT_WINDOW_MS', 900000), // 15分钟
  RATE_LIMIT_MAX_REQUESTS: getEnvNumber('RATE_LIMIT_MAX_REQUESTS', 100),
  RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS: getEnvBoolean('RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS', true),

  // 监控配置
  HEALTH_CHECK_INTERVAL: getEnvNumber('HEALTH_CHECK_INTERVAL', 30000),
  METRICS_ENABLED: getEnvBoolean('METRICS_ENABLED', true),
  METRICS_PORT: getEnvNumber('METRICS_PORT', 9090),

  // 缓存配置
  CACHE_TTL: getEnvNumber('CACHE_TTL', 3600),
  CACHE_MAX_KEYS: getEnvNumber('CACHE_MAX_KEYS', 1000),

  // 任务队列配置
  QUEUE_REDIS_HOST: getEnvVar('QUEUE_REDIS_HOST', 'localhost'),
  QUEUE_REDIS_PORT: getEnvNumber('QUEUE_REDIS_PORT', 6379),
  QUEUE_CONCURRENCY: getEnvNumber('QUEUE_CONCURRENCY', 5),

  // API配置
  API_VERSION: getEnvVar('API_VERSION', 'v1'),
  API_PREFIX: getEnvVar('API_PREFIX', '/api'),

  // 开发配置
  DEBUG: getEnvVar('DEBUG'),
  SWAGGER_ENABLED: getEnvBoolean('SWAGGER_ENABLED', true),
  MOCK_AI_RESPONSES: getEnvBoolean('MOCK_AI_RESPONSES', false),
};

// 导出配置对象
export const config = {
  // 环境信息
  env: env.NODE_ENV,
  isDevelopment: env.NODE_ENV === 'development',
  isProduction: env.NODE_ENV === 'production',
  isTest: env.NODE_ENV === 'test',

  // 服务器配置
  port: env.PORT,
  host: env.HOST,

  // 数据库配置
  database: {
    host: env.DB_HOST,
    port: env.DB_PORT,
    database: env.DB_NAME,
    user: env.DB_USER,
    password: env.DB_PASSWORD,
    ssl: env.DB_SSL,
    pool: {
      min: env.DB_POOL_MIN,
      max: env.DB_POOL_MAX,
    },
  },

  // Redis配置
  redis: {
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    password: env.REDIS_PASSWORD,
    db: env.REDIS_DB,
  },

  // JWT配置
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
    refreshSecret: env.JWT_REFRESH_SECRET,
    refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
  },

  // 加密配置
  encryption: {
    bcryptRounds: env.BCRYPT_ROUNDS,
    key: env.ENCRYPTION_KEY,
  },

  // AI服务配置
  ai: {
    openai: {
      apiKey: env.OPENAI_API_KEY,
      model: env.OPENAI_MODEL,
      maxTokens: env.OPENAI_MAX_TOKENS,
      temperature: env.OPENAI_TEMPERATURE,
    },
    anthropic: {
      apiKey: env.ANTHROPIC_API_KEY,
      model: env.ANTHROPIC_MODEL,
      maxTokens: env.ANTHROPIC_MAX_TOKENS,
    },
    mockResponses: env.MOCK_AI_RESPONSES,
  },

  // 向量数据库配置
  vector: {
    host: env.CHROMA_HOST,
    port: env.CHROMA_PORT,
    collectionName: env.CHROMA_COLLECTION_NAME,
    distanceFunction: env.CHROMA_DISTANCE_FUNCTION,
  },

  // 文件上传配置
  upload: {
    maxSize: env.UPLOAD_MAX_SIZE,
    allowedTypes: env.UPLOAD_ALLOWED_TYPES.split(','),
    destination: env.UPLOAD_DEST,
  },

  // 日志配置
  logging: {
    level: env.LOG_LEVEL,
    file: env.LOG_FILE,
    maxSize: env.LOG_MAX_SIZE,
    maxFiles: env.LOG_MAX_FILES,
  },

  // 安全配置
  security: {
    corsOrigins: env.CORS_ORIGIN.split(','),
    rateLimitWindowMs: env.RATE_LIMIT_WINDOW_MS,
    rateLimitMaxRequests: env.RATE_LIMIT_MAX_REQUESTS,
    rateLimitSkipSuccessful: env.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS,
  },

  // CORS配置
  cors: {
    origins: env.CORS_ORIGIN.split(','),
  },

  // 监控配置
  monitoring: {
    healthCheckInterval: env.HEALTH_CHECK_INTERVAL,
    metricsEnabled: env.METRICS_ENABLED,
    metricsPort: env.METRICS_PORT,
  },

  // 缓存配置
  cache: {
    ttl: env.CACHE_TTL,
    maxKeys: env.CACHE_MAX_KEYS,
  },

  // 任务队列配置
  queue: {
    redis: {
      host: env.QUEUE_REDIS_HOST,
      port: env.QUEUE_REDIS_PORT,
    },
    concurrency: env.QUEUE_CONCURRENCY,
  },

  // API配置
  api: {
    version: env.API_VERSION,
    prefix: env.API_PREFIX,
  },

  // 开发配置
  development: {
    debug: env.DEBUG,
    swaggerEnabled: env.SWAGGER_ENABLED,
  },
} as const;

// 配置验证
export function validateConfig(): void {
  const requiredKeys = [
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'ENCRYPTION_KEY',
    'DB_PASSWORD',
  ];

  const missingKeys = requiredKeys.filter(key => !process.env[key]);
  
  if (missingKeys.length > 0) {
    throw new Error(`Missing required environment variables: ${missingKeys.join(', ')}`);
  }

  // AI服务至少需要一个
  if (!env.OPENAI_API_KEY && !env.ANTHROPIC_API_KEY && !env.MOCK_AI_RESPONSES) {
    throw new Error('At least one AI service API key is required (OPENAI_API_KEY or ANTHROPIC_API_KEY), or enable MOCK_AI_RESPONSES');
  }
}

// 导出类型
export type Config = typeof config;
