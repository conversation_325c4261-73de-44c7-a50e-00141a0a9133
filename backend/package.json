{"name": "erp-assistant-backend", "version": "1.0.0", "description": "ERP智能助手后端服务 - 基于Node.js和Express的RESTful API服务", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "seed": "knex seed:run", "docker:build": "docker build -t erp-assistant-backend .", "docker:run": "docker run -p 3000:3000 erp-assistant-backend"}, "keywords": ["erp", "ai", "assistant", "chrome-extension", "backend", "api", "nodejs", "express", "postgresql", "vector-database"], "author": "ERP Assistant Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "rate-limiter-flexible": "^3.0.8", "pg": "^8.11.3", "knex": "^3.0.1", "chromadb": "^1.7.3", "openai": "^4.20.1", "anthropic": "^0.9.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "node-cron": "^3.0.3", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "redis": "^4.6.10", "bull": "^4.12.2"}, "devDependencies": {"@types/node": "^20.8.10", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node-cron": "^3.0.11", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.2.2", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/erp-intelligent-assistant.git"}, "bugs": {"url": "https://github.com/your-org/erp-intelligent-assistant/issues"}, "homepage": "https://github.com/your-org/erp-intelligent-assistant#readme"}