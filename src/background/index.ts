// ERP智能助手 - Service Worker主文件

import { MessageType, Message, MessageHandler, RecordingStatus } from '@/types';
import { MessageRouter } from './message-router';
import { StorageManager } from './storage-manager';
import { SessionManager } from './session-manager';
import { Logger } from '@/utils/logger';

class BackgroundService {
  private messageRouter: MessageRouter;
  private storageManager: StorageManager;
  private sessionManager: SessionManager;
  private logger: Logger;
  private isInitialized = false;

  constructor() {
    this.logger = new Logger('BackgroundService');
    this.messageRouter = new MessageRouter();
    this.storageManager = new StorageManager();
    this.sessionManager = new SessionManager(this.storageManager);
    
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing background service...');
      
      // 初始化存储管理器
      await this.storageManager.initialize();
      
      // 初始化会话管理器
      await this.sessionManager.initialize();
      
      // 设置消息处理器
      this.setupMessageHandlers();
      
      // 设置Chrome API事件监听器
      this.setupChromeEventListeners();
      
      // 设置定时任务
      this.setupPeriodicTasks();
      
      this.isInitialized = true;
      this.logger.info('Background service initialized successfully');
      
      // 通知所有标签页服务已就绪
      this.broadcastToAllTabs({
        type: MessageType.SUCCESS,
        payload: { message: 'Background service ready' }
      });
      
    } catch (error) {
      this.logger.error('Failed to initialize background service:', error);
      throw error;
    }
  }

  private setupMessageHandlers(): void {
    // 录制相关消息处理
    this.messageRouter.addHandler(MessageType.START_RECORDING, this.handleStartRecording.bind(this));
    this.messageRouter.addHandler(MessageType.STOP_RECORDING, this.handleStopRecording.bind(this));
    this.messageRouter.addHandler(MessageType.PAUSE_RECORDING, this.handlePauseRecording.bind(this));
    this.messageRouter.addHandler(MessageType.RESUME_RECORDING, this.handleResumeRecording.bind(this));
    this.messageRouter.addHandler(MessageType.RECORDING_STATUS, this.handleGetRecordingStatus.bind(this));
    
    // DOM事件处理
    this.messageRouter.addHandler(MessageType.DOM_EVENT, this.handleDOMEvent.bind(this));
    this.messageRouter.addHandler(MessageType.PAGE_NAVIGATION, this.handlePageNavigation.bind(this));
    this.messageRouter.addHandler(MessageType.SCREENSHOT_TAKEN, this.handleScreenshot.bind(this));
    
    // 设置相关
    this.messageRouter.addHandler(MessageType.GET_SETTINGS, this.handleGetSettings.bind(this));
    this.messageRouter.addHandler(MessageType.UPDATE_SETTINGS, this.handleUpdateSettings.bind(this));
    
    // 通用消息
    this.messageRouter.addHandler(MessageType.PING, this.handlePing.bind(this));
    
    // 启动消息路由器
    this.messageRouter.start();
  }

  private setupChromeEventListeners(): void {
    // 扩展安装/启动事件
    chrome.runtime.onInstalled.addListener((details) => {
      this.logger.info('Extension installed/updated:', details);
      this.handleExtensionInstalled(details);
    });

    // 扩展启动事件
    chrome.runtime.onStartup.addListener(() => {
      this.logger.info('Extension startup');
      this.handleExtensionStartup();
    });

    // 标签页更新事件
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.url) {
        this.handleTabUpdated(tabId, tab);
      }
    });

    // 标签页激活事件
    chrome.tabs.onActivated.addListener((activeInfo) => {
      this.handleTabActivated(activeInfo.tabId);
    });

    // 命令事件（快捷键）
    chrome.commands.onCommand.addListener((command) => {
      this.handleCommand(command);
    });

    // 上下文菜单点击事件
    chrome.contextMenus.onClicked.addListener((info, tab) => {
      this.handleContextMenuClick(info, tab);
    });
  }

  private setupPeriodicTasks(): void {
    // 每5分钟清理过期数据
    setInterval(() => {
      this.cleanupExpiredData();
    }, 5 * 60 * 1000);

    // 每小时检查存储使用情况
    setInterval(() => {
      this.checkStorageUsage();
    }, 60 * 60 * 1000);
  }

  // 消息处理器实现
  private async handleStartRecording(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      const session = await this.sessionManager.startRecording(sender.tab?.id);
      sendResponse({ success: true, sessionId: session.id });
      
      // 通知所有相关标签页
      this.broadcastToTab(sender.tab?.id, {
        type: MessageType.RECORDING_STATUS,
        payload: { status: RecordingStatus.RECORDING, sessionId: session.id }
      });
      
    } catch (error) {
      this.logger.error('Failed to start recording:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  private async handleStopRecording(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      const session = await this.sessionManager.stopRecording();
      sendResponse({ success: true, session });
      
      // 通知所有相关标签页
      this.broadcastToTab(sender.tab?.id, {
        type: MessageType.RECORDING_STATUS,
        payload: { status: RecordingStatus.IDLE }
      });
      
    } catch (error) {
      this.logger.error('Failed to stop recording:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  private async handlePauseRecording(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      await this.sessionManager.pauseRecording();
      sendResponse({ success: true });
      
      this.broadcastToTab(sender.tab?.id, {
        type: MessageType.RECORDING_STATUS,
        payload: { status: RecordingStatus.PAUSED }
      });
      
    } catch (error) {
      this.logger.error('Failed to pause recording:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  private async handleResumeRecording(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      await this.sessionManager.resumeRecording();
      sendResponse({ success: true });
      
      this.broadcastToTab(sender.tab?.id, {
        type: MessageType.RECORDING_STATUS,
        payload: { status: RecordingStatus.RECORDING }
      });
      
    } catch (error) {
      this.logger.error('Failed to resume recording:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  private async handleGetRecordingStatus(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      const status = this.sessionManager.getCurrentStatus();
      const currentSession = this.sessionManager.getCurrentSession();
      
      sendResponse({
        success: true,
        status,
        sessionId: currentSession?.id,
        operationCount: currentSession?.operations.length || 0
      });
      
    } catch (error) {
      this.logger.error('Failed to get recording status:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  private async handleDOMEvent(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      if (this.sessionManager.getCurrentStatus() === RecordingStatus.RECORDING) {
        await this.sessionManager.addOperation('dom_event', message.payload);
        sendResponse({ success: true });
      } else {
        sendResponse({ success: false, error: 'Not recording' });
      }
    } catch (error) {
      this.logger.error('Failed to handle DOM event:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  private async handlePageNavigation(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      if (this.sessionManager.getCurrentStatus() === RecordingStatus.RECORDING) {
        await this.sessionManager.addOperation('navigation', message.payload);
      }
      sendResponse({ success: true });
    } catch (error) {
      this.logger.error('Failed to handle page navigation:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  private async handleScreenshot(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      const tabId = sender.tab?.id;
      if (!tabId) {
        throw new Error('No tab ID available for screenshot');
      }

      // 捕获可见标签页的截图
      const screenshotDataUrl = await this.captureVisibleTab(tabId);

      if (!screenshotDataUrl) {
        throw new Error('Failed to capture screenshot');
      }

      // 创建截图数据
      const screenshotData = {
        dataUrl: screenshotDataUrl,
        timestamp: Date.now(),
        pageUrl: message.payload?.pageUrl || sender.tab?.url || '',
        elementBounds: message.payload?.elementBounds,
        quality: message.payload?.quality || 'medium',
        context: {
          url: sender.tab?.url || '',
          title: sender.tab?.title || '',
          viewport: message.payload?.viewport || { width: 0, height: 0 },
          scrollPosition: message.payload?.scrollPosition || { x: 0, y: 0 }
        }
      };

      // 如果正在录制，添加到操作记录
      if (this.sessionManager.getCurrentStatus() === RecordingStatus.RECORDING) {
        await this.sessionManager.addOperation('screenshot', screenshotData);
      }

      sendResponse({
        success: true,
        screenshot: screenshotDataUrl,
        screenshotData
      });

    } catch (error) {
      this.logger.error('Failed to handle screenshot:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  /**
   * 捕获可见标签页截图
   */
  private async captureVisibleTab(tabId: number): Promise<string | null> {
    try {
      // 确保标签页是活动的
      await chrome.tabs.update(tabId, { active: true });

      // 等待一小段时间确保标签页完全激活
      await new Promise(resolve => setTimeout(resolve, 100));

      // 捕获截图
      const dataUrl = await chrome.tabs.captureVisibleTab(undefined, {
        format: 'png',
        quality: 90
      });

      return dataUrl;

    } catch (error) {
      this.logger.error('Failed to capture visible tab:', error);
      return null;
    }
  }

  /**
   * 智能截图 - 根据质量设置优化截图
   */
  private async captureSmartScreenshot(
    tabId: number,
    quality: 'high' | 'medium' | 'low' = 'medium'
  ): Promise<string | null> {
    try {
      const qualitySettings = {
        high: { format: 'png' as const, quality: 100 },
        medium: { format: 'png' as const, quality: 90 },
        low: { format: 'jpeg' as const, quality: 70 }
      };

      const settings = qualitySettings[quality];

      // 确保标签页是活动的
      await chrome.tabs.update(tabId, { active: true });
      await new Promise(resolve => setTimeout(resolve, 100));

      // 捕获截图
      const dataUrl = await chrome.tabs.captureVisibleTab(undefined, settings);

      // 如果是高质量模式，可以进行额外处理
      if (quality === 'high') {
        return await this.enhanceScreenshot(dataUrl);
      }

      return dataUrl;

    } catch (error) {
      this.logger.error('Failed to capture smart screenshot:', error);
      return null;
    }
  }

  /**
   * 增强截图质量
   */
  private async enhanceScreenshot(dataUrl: string): Promise<string> {
    try {
      // 这里可以添加图像处理逻辑，如压缩、裁剪等
      // 目前直接返回原始数据
      return dataUrl;
    } catch (error) {
      this.logger.error('Failed to enhance screenshot:', error);
      return dataUrl;
    }
  }

  private async handleGetSettings(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      const settings = await this.storageManager.getSettings();
      sendResponse({ success: true, settings });
    } catch (error) {
      this.logger.error('Failed to get settings:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  private async handleUpdateSettings(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      await this.storageManager.updateSettings(message.payload);
      sendResponse({ success: true });
    } catch (error) {
      this.logger.error('Failed to update settings:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  private handlePing(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): void {
    sendResponse({
      type: MessageType.PONG,
      payload: { timestamp: Date.now(), initialized: this.isInitialized }
    });
  }

  // Chrome事件处理器
  private async handleExtensionInstalled(details: chrome.runtime.InstalledDetails): Promise<void> {
    if (details.reason === 'install') {
      // 首次安装，设置默认配置
      await this.storageManager.initializeDefaults();
      
      // 创建上下文菜单
      this.createContextMenus();
      
      // 显示欢迎通知
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title: 'ERP智能助手',
        message: '安装成功！点击插件图标开始使用。'
      });
    }
  }

  private async handleExtensionStartup(): Promise<void> {
    // 恢复之前的会话状态
    await this.sessionManager.restoreSession();
  }

  private async handleTabUpdated(tabId: number, tab: chrome.tabs.Tab): Promise<void> {
    // 检查是否是ERP页面，如果是则注入内容脚本
    if (tab.url && this.isERPPage(tab.url)) {
      try {
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['content.js']
        });
      } catch (error) {
        this.logger.warn('Failed to inject content script:', error);
      }
    }
  }

  private async handleTabActivated(tabId: number): Promise<void> {
    // 标签页激活时的处理逻辑
    this.logger.debug('Tab activated:', tabId);
  }

  private async handleCommand(command: string): Promise<void> {
    switch (command) {
      case 'start_recording':
        // 获取当前活动标签页并开始录制
        const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (activeTab.id) {
          this.broadcastToTab(activeTab.id, { type: MessageType.START_RECORDING });
        }
        break;
      case 'toggle_guidance':
        // 切换智能引导
        break;
      case 'open_options':
        chrome.runtime.openOptionsPage();
        break;
    }
  }

  private handleContextMenuClick(
    info: chrome.contextMenus.OnClickData,
    tab?: chrome.tabs.Tab
  ): void {
    // 上下文菜单点击处理
  }

  // 工具方法
  private async broadcastToAllTabs(message: Message): Promise<void> {
    const tabs = await chrome.tabs.query({});
    for (const tab of tabs) {
      if (tab.id) {
        this.broadcastToTab(tab.id, message);
      }
    }
  }

  private async broadcastToTab(tabId: number | undefined, message: Message): Promise<void> {
    if (!tabId) return;
    
    try {
      await chrome.tabs.sendMessage(tabId, message);
    } catch (error) {
      // 忽略无法发送消息的错误（如标签页已关闭）
      this.logger.debug('Failed to send message to tab:', tabId, error);
    }
  }

  private isERPPage(url: string): boolean {
    // 简单的ERP页面检测逻辑
    const erpPatterns = [
      /sap\./i,
      /oracle\./i,
      /dynamics\./i,
      /netsuite\./i,
      /workday\./i,
      /erp/i
    ];
    
    return erpPatterns.some(pattern => pattern.test(url));
  }

  private createContextMenus(): void {
    chrome.contextMenus.create({
      id: 'erp-assistant-start-recording',
      title: '开始录制操作',
      contexts: ['page']
    });
    
    chrome.contextMenus.create({
      id: 'erp-assistant-analyze-page',
      title: '分析当前页面',
      contexts: ['page']
    });
  }

  private async cleanupExpiredData(): Promise<void> {
    try {
      await this.storageManager.cleanupExpiredData();
      this.logger.debug('Expired data cleaned up');
    } catch (error) {
      this.logger.error('Failed to cleanup expired data:', error);
    }
  }

  private async checkStorageUsage(): Promise<void> {
    try {
      const usage = await this.storageManager.getStorageUsage();
      if (usage.percentage > 80) {
        this.logger.warn('Storage usage high:', usage);
        // 可以触发清理或通知用户
      }
    } catch (error) {
      this.logger.error('Failed to check storage usage:', error);
    }
  }
}

// 创建并启动背景服务
const backgroundService = new BackgroundService();

// 导出用于测试
export { BackgroundService };
