// ERP智能助手 - Card组件

import React, { forwardRef } from 'react';
import { CardProps } from '../types';
import { cn } from '../utils';

export const Card = forwardRef<HTMLDivElement, CardProps>(
  (
    {
      variant = 'default',
      padding = 'md',
      header,
      footer,
      hoverable = false,
      className,
      children,
      style,
      ...props
    },
    ref
  ) => {
    const paddingMap = {
      xs: '8px',
      sm: '12px',
      md: '16px',
      lg: '24px',
      xl: '32px',
    };

    const baseStyles = {
      backgroundColor: 'var(--color-surface)',
      borderRadius: 'var(--radius-lg)',
      transition: 'all var(--duration-normal) var(--easing-ease)',
      overflow: 'hidden' as const,
      ...style,
    };

    const variantStyles = {
      default: {
        border: '1px solid var(--color-border-light)',
        boxShadow: 'var(--shadow-sm)',
      },
      outlined: {
        border: '1px solid var(--color-border)',
        boxShadow: 'none',
      },
      elevated: {
        border: 'none',
        boxShadow: 'var(--shadow-lg)',
      },
    };

    const hoverStyles = hoverable
      ? {
          cursor: 'pointer',
        }
      : {};

    const cardStyles = {
      ...baseStyles,
      ...variantStyles[variant],
      ...hoverStyles,
    };

    const contentPadding = paddingMap[padding];

    return (
      <>
        <style>
          {`
            .erp-card.hoverable:hover {
              transform: translateY(-2px);
              box-shadow: var(--shadow-lg);
            }
            
            .erp-card.hoverable:active {
              transform: translateY(-1px);
            }
            
            .erp-card-header {
              padding: ${contentPadding};
              border-bottom: 1px solid var(--color-border-light);
              background-color: var(--color-background);
            }
            
            .erp-card-body {
              padding: ${contentPadding};
            }
            
            .erp-card-footer {
              padding: ${contentPadding};
              border-top: 1px solid var(--color-border-light);
              background-color: var(--color-background);
            }
            
            .erp-card-header:empty,
            .erp-card-footer:empty {
              display: none;
            }
            
            .erp-card-header + .erp-card-body {
              padding-top: ${contentPadding};
            }
            
            .erp-card-body + .erp-card-footer {
              padding-top: 0;
            }
          `}
        </style>
        <div
          ref={ref}
          className={cn(
            'erp-card',
            `erp-card-${variant}`,
            {
              hoverable,
            },
            className
          )}
          style={cardStyles}
          {...props}
        >
          {header && (
            <div className="erp-card-header">
              {header}
            </div>
          )}
          
          <div className="erp-card-body">
            {children}
          </div>
          
          {footer && (
            <div className="erp-card-footer">
              {footer}
            </div>
          )}
        </div>
      </>
    );
  }
);

Card.displayName = 'Card';

// 卡片标题组件
export interface CardTitleProps {
  children: React.ReactNode;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  className?: string;
}

export const CardTitle: React.FC<CardTitleProps> = ({
  children,
  level = 3,
  className,
}) => {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements;
  
  const titleStyles = {
    margin: 0,
    fontSize: level <= 2 ? 'var(--font-size-lg)' : 'var(--font-size-md)',
    fontWeight: 'var(--font-weight-semibold)',
    color: 'var(--color-text-primary)',
    lineHeight: 1.4,
  };

  return (
    <Tag
      className={cn('erp-card-title', className)}
      style={titleStyles}
    >
      {children}
    </Tag>
  );
};

CardTitle.displayName = 'CardTitle';

// 卡片描述组件
export interface CardDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

export const CardDescription: React.FC<CardDescriptionProps> = ({
  children,
  className,
}) => {
  const descriptionStyles = {
    margin: '4px 0 0 0',
    fontSize: 'var(--font-size-sm)',
    color: 'var(--color-text-secondary)',
    lineHeight: 1.5,
  };

  return (
    <p
      className={cn('erp-card-description', className)}
      style={descriptionStyles}
    >
      {children}
    </p>
  );
};

CardDescription.displayName = 'CardDescription';

// 卡片操作组件
export interface CardActionsProps {
  children: React.ReactNode;
  align?: 'left' | 'center' | 'right';
  className?: string;
}

export const CardActions: React.FC<CardActionsProps> = ({
  children,
  align = 'right',
  className,
}) => {
  const actionsStyles = {
    display: 'flex',
    gap: '8px',
    justifyContent: align === 'left' ? 'flex-start' : align === 'center' ? 'center' : 'flex-end',
    alignItems: 'center',
    marginTop: '16px',
  };

  return (
    <div
      className={cn('erp-card-actions', className)}
      style={actionsStyles}
    >
      {children}
    </div>
  );
};

CardActions.displayName = 'CardActions';

// 卡片网格组件
export interface CardGridProps {
  children: React.ReactNode;
  columns?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  gap?: string;
  className?: string;
}

export const CardGrid: React.FC<CardGridProps> = ({
  children,
  columns = 3,
  gap = '16px',
  className,
}) => {
  const getGridColumns = () => {
    if (typeof columns === 'number') {
      return `repeat(${columns}, 1fr)`;
    }
    
    // 响应式列数
    const { xs = 1, sm = 2, md = 3, lg = 4, xl = 5 } = columns;
    return {
      gridTemplateColumns: `repeat(${xs}, 1fr)`,
      '@media (min-width: 576px)': {
        gridTemplateColumns: `repeat(${sm}, 1fr)`,
      },
      '@media (min-width: 768px)': {
        gridTemplateColumns: `repeat(${md}, 1fr)`,
      },
      '@media (min-width: 992px)': {
        gridTemplateColumns: `repeat(${lg}, 1fr)`,
      },
      '@media (min-width: 1200px)': {
        gridTemplateColumns: `repeat(${xl}, 1fr)`,
      },
    };
  };

  const gridStyles = {
    display: 'grid',
    gap,
    ...(typeof columns === 'number' 
      ? { gridTemplateColumns: getGridColumns() }
      : {}
    ),
  };

  return (
    <>
      {typeof columns !== 'number' && (
        <style>
          {`
            .erp-card-grid {
              grid-template-columns: repeat(1, 1fr);
            }
            
            @media (min-width: 576px) {
              .erp-card-grid {
                grid-template-columns: repeat(${columns.sm || 2}, 1fr);
              }
            }
            
            @media (min-width: 768px) {
              .erp-card-grid {
                grid-template-columns: repeat(${columns.md || 3}, 1fr);
              }
            }
            
            @media (min-width: 992px) {
              .erp-card-grid {
                grid-template-columns: repeat(${columns.lg || 4}, 1fr);
              }
            }
            
            @media (min-width: 1200px) {
              .erp-card-grid {
                grid-template-columns: repeat(${columns.xl || 5}, 1fr);
              }
            }
          `}
        </style>
      )}
      <div
        className={cn('erp-card-grid', className)}
        style={gridStyles}
      >
        {children}
      </div>
    </>
  );
};

CardGrid.displayName = 'CardGrid';
