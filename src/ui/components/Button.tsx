// ERP智能助手 - Button组件

import React, { forwardRef } from 'react';
import { ButtonProps } from '../types';
import { cn, sizeMap, variantMap } from '../utils';

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      loading = false,
      disabled = false,
      icon,
      iconPosition = 'left',
      fullWidth = false,
      className,
      children,
      style,
      ...props
    },
    ref
  ) => {
    const baseStyles = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px',
      border: 'none',
      borderRadius: 'var(--radius-md)',
      fontWeight: 'var(--font-weight-medium)',
      cursor: disabled || loading ? 'not-allowed' : 'pointer',
      transition: 'all var(--duration-fast) var(--easing-ease)',
      outline: 'none',
      position: 'relative' as const,
      overflow: 'hidden' as const,
      userSelect: 'none' as const,
      ...sizeMap[size],
      ...variantMap[variant],
      width: fullWidth ? '100%' : 'auto',
      opacity: disabled ? 0.6 : 1,
      ...style,
    };

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (disabled || loading) {
        event.preventDefault();
        return;
      }
      
      // 添加点击波纹效果
      const button = event.currentTarget;
      const rect = button.getBoundingClientRect();
      const ripple = document.createElement('span');
      const size = Math.max(rect.width, rect.height);
      const x = event.clientX - rect.left - size / 2;
      const y = event.clientY - rect.top - size / 2;
      
      ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
        z-index: 1;
      `;
      
      button.appendChild(ripple);
      
      setTimeout(() => {
        ripple.remove();
      }, 600);
      
      props.onClick?.(event);
    };

    const renderIcon = () => {
      if (loading) {
        return (
          <span
            className="button-loading-icon"
            style={{
              display: 'inline-block',
              width: '16px',
              height: '16px',
              border: '2px solid currentColor',
              borderTop: '2px solid transparent',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
            }}
          />
        );
      }
      
      if (icon) {
        return <span className="button-icon">{icon}</span>;
      }
      
      return null;
    };

    const iconElement = renderIcon();

    return (
      <>
        <style>
          {`
            @keyframes ripple {
              to {
                transform: scale(4);
                opacity: 0;
              }
            }
            
            @keyframes spin {
              from {
                transform: rotate(0deg);
              }
              to {
                transform: rotate(360deg);
              }
            }
            
            .erp-button:hover:not(:disabled) {
              transform: translateY(-1px);
              box-shadow: var(--shadow-md);
            }
            
            .erp-button:active:not(:disabled) {
              transform: translateY(0);
            }
            
            .erp-button:focus-visible {
              outline: 2px solid var(--color-primary);
              outline-offset: 2px;
            }
            
            .erp-button-primary:hover:not(:disabled) {
              background-color: var(--color-primary-hover);
            }
            
            .erp-button-secondary:hover:not(:disabled) {
              background-color: var(--color-border-light);
              border-color: var(--color-primary);
            }
            
            .erp-button-outline:hover:not(:disabled) {
              background-color: var(--color-primary);
              color: white;
            }
            
            .erp-button-ghost:hover:not(:disabled) {
              background-color: var(--color-border-light);
            }
          `}
        </style>
        <button
          ref={ref}
          className={cn(
            'erp-button',
            `erp-button-${variant}`,
            `erp-button-${size}`,
            {
              'erp-button-loading': loading,
              'erp-button-full-width': fullWidth,
            },
            className
          )}
          style={baseStyles}
          disabled={disabled || loading}
          onClick={handleClick}
          {...props}
        >
          {iconPosition === 'left' && iconElement}
          {children && <span className="button-text">{children}</span>}
          {iconPosition === 'right' && iconElement}
        </button>
      </>
    );
  }
);

Button.displayName = 'Button';

// 按钮组组件
export interface ButtonGroupProps {
  children: React.ReactNode;
  size?: ButtonProps['size'];
  variant?: ButtonProps['variant'];
  className?: string;
  vertical?: boolean;
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  size,
  variant,
  className,
  vertical = false,
}) => {
  const groupStyles = {
    display: 'inline-flex',
    flexDirection: vertical ? ('column' as const) : ('row' as const),
    gap: '1px',
    borderRadius: 'var(--radius-md)',
    overflow: 'hidden',
    boxShadow: 'var(--shadow-sm)',
  };

  return (
    <>
      <style>
        {`
          .erp-button-group .erp-button {
            border-radius: 0;
            margin: 0;
          }
          
          .erp-button-group .erp-button:first-child {
            border-radius: var(--radius-md) 0 0 var(--radius-md);
          }
          
          .erp-button-group .erp-button:last-child {
            border-radius: 0 var(--radius-md) var(--radius-md) 0;
          }
          
          .erp-button-group.vertical .erp-button:first-child {
            border-radius: var(--radius-md) var(--radius-md) 0 0;
          }
          
          .erp-button-group.vertical .erp-button:last-child {
            border-radius: 0 0 var(--radius-md) var(--radius-md);
          }
          
          .erp-button-group .erp-button:only-child {
            border-radius: var(--radius-md);
          }
        `}
      </style>
      <div
        className={cn(
          'erp-button-group',
          { vertical },
          className
        )}
        style={groupStyles}
      >
        {React.Children.map(children, (child) => {
          if (React.isValidElement(child) && child.type === Button) {
            return React.cloneElement(child, {
              size: child.props.size || size,
              variant: child.props.variant || variant,
            });
          }
          return child;
        })}
      </div>
    </>
  );
};

ButtonGroup.displayName = 'ButtonGroup';
