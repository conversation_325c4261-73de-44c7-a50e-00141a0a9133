// ERP智能助手 - Input组件

import React, { forwardRef, useState } from 'react';
import { InputProps } from '../types';
import { cn, generateId } from '../utils';

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      helperText,
      prefix,
      suffix,
      variant = 'default',
      size = 'md',
      fullWidth = false,
      className,
      style,
      ...props
    },
    ref
  ) => {
    const [focused, setFocused] = useState(false);
    const [hasValue, setHasValue] = useState(!!props.value || !!props.defaultValue);
    const inputId = props.id || generateId('input');

    const sizeMap = {
      xs: { padding: '6px 8px', fontSize: '12px', height: '28px' },
      sm: { padding: '8px 12px', fontSize: '14px', height: '32px' },
      md: { padding: '10px 12px', fontSize: '14px', height: '40px' },
      lg: { padding: '12px 16px', fontSize: '16px', height: '48px' },
      xl: { padding: '14px 20px', fontSize: '18px', height: '56px' },
    };

    const baseInputStyles = {
      width: fullWidth ? '100%' : 'auto',
      border: '1px solid var(--color-border)',
      borderRadius: 'var(--radius-md)',
      backgroundColor: 'var(--color-surface)',
      color: 'var(--color-text-primary)',
      fontFamily: 'var(--font-family)',
      transition: 'all var(--duration-fast) var(--easing-ease)',
      outline: 'none',
      ...sizeMap[size],
      ...style,
    };

    const variantStyles = {
      default: {},
      filled: {
        backgroundColor: 'var(--color-background)',
        border: '1px solid transparent',
      },
      outlined: {
        backgroundColor: 'transparent',
        border: '2px solid var(--color-border)',
      },
    };

    const inputStyles = {
      ...baseInputStyles,
      ...variantStyles[variant],
      borderColor: error
        ? 'var(--color-error)'
        : focused
        ? 'var(--color-primary)'
        : 'var(--color-border)',
      boxShadow: focused
        ? `0 0 0 3px ${error ? 'rgba(239, 68, 68, 0.1)' : 'var(--color-primary-light)'}`
        : 'none',
    };

    const wrapperStyles = {
      position: 'relative' as const,
      display: 'inline-block',
      width: fullWidth ? '100%' : 'auto',
    };

    const labelStyles = {
      display: 'block',
      marginBottom: '6px',
      fontSize: 'var(--font-size-sm)',
      fontWeight: 'var(--font-weight-medium)',
      color: error ? 'var(--color-error)' : 'var(--color-text-primary)',
    };

    const helperTextStyles = {
      marginTop: '4px',
      fontSize: 'var(--font-size-xs)',
      color: error ? 'var(--color-error)' : 'var(--color-text-secondary)',
    };

    const affixStyles = {
      position: 'absolute' as const,
      top: '50%',
      transform: 'translateY(-50%)',
      display: 'flex',
      alignItems: 'center',
      color: 'var(--color-text-secondary)',
      pointerEvents: 'none' as const,
      zIndex: 1,
    };

    const prefixStyles = {
      ...affixStyles,
      left: '12px',
    };

    const suffixStyles = {
      ...affixStyles,
      right: '12px',
    };

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(true);
      props.onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(false);
      props.onBlur?.(e);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(!!e.target.value);
      props.onChange?.(e);
    };

    const inputPaddingLeft = prefix ? '40px' : '12px';
    const inputPaddingRight = suffix ? '40px' : '12px';

    return (
      <>
        <style>
          {`
            .erp-input-wrapper {
              font-family: var(--font-family);
            }
            
            .erp-input:disabled {
              opacity: 0.6;
              cursor: not-allowed;
              background-color: var(--color-border-light);
            }
            
            .erp-input::placeholder {
              color: var(--color-text-muted);
            }
            
            .erp-input:hover:not(:disabled):not(:focus) {
              border-color: var(--color-primary);
            }
            
            .erp-input-error {
              border-color: var(--color-error) !important;
            }
            
            .erp-input-error:focus {
              box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
            }
          `}
        </style>
        <div className={cn('erp-input-wrapper', className)} style={wrapperStyles}>
          {label && (
            <label
              htmlFor={inputId}
              className="erp-input-label"
              style={labelStyles}
            >
              {label}
            </label>
          )}
          
          <div style={{ position: 'relative' }}>
            {prefix && (
              <div className="erp-input-prefix" style={prefixStyles}>
                {prefix}
              </div>
            )}
            
            <input
              ref={ref}
              id={inputId}
              className={cn(
                'erp-input',
                `erp-input-${variant}`,
                `erp-input-${size}`,
                {
                  'erp-input-error': !!error,
                  'erp-input-focused': focused,
                  'erp-input-has-value': hasValue,
                  'erp-input-full-width': fullWidth,
                }
              )}
              style={{
                ...inputStyles,
                paddingLeft: inputPaddingLeft,
                paddingRight: inputPaddingRight,
              }}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onChange={handleChange}
              {...props}
            />
            
            {suffix && (
              <div className="erp-input-suffix" style={suffixStyles}>
                {suffix}
              </div>
            )}
          </div>
          
          {(error || helperText) && (
            <div className="erp-input-helper-text" style={helperTextStyles}>
              {error || helperText}
            </div>
          )}
        </div>
      </>
    );
  }
);

Input.displayName = 'Input';

// 密码输入框组件
export interface PasswordInputProps extends Omit<InputProps, 'type'> {
  visibilityToggle?: boolean;
}

export const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ visibilityToggle = true, ...props }, ref) => {
    const [visible, setVisible] = useState(false);

    const toggleVisibility = () => {
      setVisible(!visible);
    };

    const eyeIcon = (
      <button
        type="button"
        onClick={toggleVisibility}
        style={{
          background: 'none',
          border: 'none',
          cursor: 'pointer',
          padding: '4px',
          color: 'var(--color-text-secondary)',
          pointerEvents: 'auto',
        }}
        tabIndex={-1}
      >
        {visible ? '👁️' : '👁️‍🗨️'}
      </button>
    );

    return (
      <Input
        ref={ref}
        type={visible ? 'text' : 'password'}
        suffix={visibilityToggle ? eyeIcon : props.suffix}
        {...props}
      />
    );
  }
);

PasswordInput.displayName = 'PasswordInput';

// 搜索输入框组件
export interface SearchInputProps extends Omit<InputProps, 'type'> {
  onSearch?: (value: string) => void;
  loading?: boolean;
}

export const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(
  ({ onSearch, loading = false, ...props }, ref) => {
    const [value, setValue] = useState(props.value || props.defaultValue || '');

    const handleSearch = () => {
      onSearch?.(String(value));
    };

    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        handleSearch();
      }
      props.onKeyPress?.(e);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setValue(e.target.value);
      props.onChange?.(e);
    };

    const searchIcon = (
      <button
        type="button"
        onClick={handleSearch}
        disabled={loading}
        style={{
          background: 'none',
          border: 'none',
          cursor: loading ? 'not-allowed' : 'pointer',
          padding: '4px',
          color: 'var(--color-text-secondary)',
          pointerEvents: 'auto',
        }}
        tabIndex={-1}
      >
        {loading ? '⏳' : '🔍'}
      </button>
    );

    return (
      <Input
        ref={ref}
        type="search"
        placeholder="搜索..."
        suffix={searchIcon}
        onKeyPress={handleKeyPress}
        onChange={handleChange}
        {...props}
        value={value}
      />
    );
  }
);

SearchInput.displayName = 'SearchInput';
