// ERP智能助手 - 主题提供者

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Theme } from '../types';
import { scribeTheme, themes, ThemeName } from './themes';

interface ThemeContextValue {
  theme: Theme;
  themeName: ThemeName;
  setTheme: (theme: ThemeName | Theme) => void;
  toggleTheme: () => void;
  isDark: boolean;
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: ThemeName | Theme;
  storageKey?: string;
}

export function ThemeProvider({ 
  children, 
  defaultTheme = 'scribe',
  storageKey = 'erp-assistant-theme'
}: ThemeProviderProps) {
  const [currentTheme, setCurrentTheme] = useState<Theme>(scribeTheme);
  const [currentThemeName, setCurrentThemeName] = useState<ThemeName>('scribe');

  // 初始化主题
  useEffect(() => {
    const initializeTheme = () => {
      try {
        // 从本地存储读取主题设置
        const savedTheme = localStorage.getItem(storageKey);
        
        if (savedTheme) {
          const parsedTheme = JSON.parse(savedTheme);
          
          if (typeof parsedTheme === 'string' && parsedTheme in themes) {
            setTheme(parsedTheme as ThemeName);
          } else if (typeof parsedTheme === 'object') {
            setCurrentTheme(parsedTheme);
            setCurrentThemeName('custom');
          }
        } else {
          // 检测系统主题偏好
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          const initialTheme = prefersDark ? 'dark' : (typeof defaultTheme === 'string' ? defaultTheme : 'scribe');
          setTheme(initialTheme);
        }
      } catch (error) {
        console.warn('Failed to load theme from storage:', error);
        setTheme(typeof defaultTheme === 'string' ? defaultTheme : 'scribe');
      }
    };

    initializeTheme();

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      const savedTheme = localStorage.getItem(storageKey);
      if (!savedTheme) {
        setTheme(e.matches ? 'dark' : 'scribe');
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, [defaultTheme, storageKey]);

  // 应用CSS变量
  useEffect(() => {
    const root = document.documentElement;
    
    // 设置CSS变量
    Object.entries(currentTheme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`, value);
    });

    Object.entries(currentTheme.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--shadow-${key}`, value);
    });

    Object.entries(currentTheme.radius).forEach(([key, value]) => {
      root.style.setProperty(`--radius-${key}`, value);
    });

    Object.entries(currentTheme.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value);
    });

    Object.entries(currentTheme.typography.fontSize).forEach(([key, value]) => {
      root.style.setProperty(`--font-size-${key}`, value);
    });

    Object.entries(currentTheme.typography.fontWeight).forEach(([key, value]) => {
      root.style.setProperty(`--font-weight-${key}`, value.toString());
    });

    root.style.setProperty('--font-family', currentTheme.typography.fontFamily);

    Object.entries(currentTheme.animation.duration).forEach(([key, value]) => {
      root.style.setProperty(`--duration-${key}`, value);
    });

    Object.entries(currentTheme.animation.easing).forEach(([key, value]) => {
      root.style.setProperty(`--easing-${key}`, value);
    });

    // 设置主题类名
    root.className = root.className.replace(/theme-\w+/g, '');
    root.classList.add(`theme-${currentThemeName}`);
  }, [currentTheme, currentThemeName]);

  const setTheme = (theme: ThemeName | Theme) => {
    try {
      if (typeof theme === 'string') {
        if (theme in themes) {
          setCurrentTheme(themes[theme]);
          setCurrentThemeName(theme);
          localStorage.setItem(storageKey, JSON.stringify(theme));
        }
      } else {
        setCurrentTheme(theme);
        setCurrentThemeName('custom');
        localStorage.setItem(storageKey, JSON.stringify(theme));
      }
    } catch (error) {
      console.warn('Failed to save theme to storage:', error);
    }
  };

  const toggleTheme = () => {
    const newTheme = currentThemeName === 'dark' ? 'scribe' : 'dark';
    setTheme(newTheme);
  };

  const isDark = currentThemeName === 'dark' || 
    (currentThemeName === 'custom' && currentTheme.colors.background === '#111827');

  const contextValue: ThemeContextValue = {
    theme: currentTheme,
    themeName: currentThemeName,
    setTheme,
    toggleTheme,
    isDark,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme(): ThemeContextValue {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// 主题切换Hook
export function useThemeToggle() {
  const { toggleTheme, isDark } = useTheme();
  return { toggleTheme, isDark };
}

// 响应式主题Hook
export function useResponsiveTheme() {
  const { theme, setTheme } = useTheme();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return {
    theme,
    setTheme,
    isMobile,
    isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
    isDesktop: window.innerWidth >= 1024,
  };
}
