// ERP智能助手 - UI组件类型定义

import { ReactNode, HTMLAttributes, ButtonHTMLAttributes, InputHTMLAttributes, SelectHTMLAttributes } from 'react';

// 基础主题类型
export interface Theme {
  colors: {
    primary: string;
    primaryHover: string;
    primaryLight: string;
    primaryDark: string;
    secondary: string;
    background: string;
    surface: string;
    textPrimary: string;
    textSecondary: string;
    textMuted: string;
    border: string;
    borderLight: string;
    success: string;
    warning: string;
    error: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
  radius: {
    sm: string;
    md: string;
    lg: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    fontWeight: {
      normal: number;
      medium: number;
      semibold: number;
      bold: number;
    };
  };
  animation: {
    duration: {
      fast: string;
      normal: string;
      slow: string;
    };
    easing: {
      ease: string;
      easeIn: string;
      easeOut: string;
      easeInOut: string;
    };
  };
}

// 基础组件属性
export interface ComponentProps {
  className?: string;
  children?: ReactNode;
  style?: React.CSSProperties;
}

// 尺寸类型
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// 变体类型
export type Variant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'outline';

// 按钮组件属性
export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement>, ComponentProps {
  variant?: Variant;
  size?: Size;
  loading?: boolean;
  disabled?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
}

// 卡片组件属性
export interface CardProps extends HTMLAttributes<HTMLDivElement>, ComponentProps {
  variant?: 'default' | 'outlined' | 'elevated';
  padding?: Size;
  header?: ReactNode;
  footer?: ReactNode;
  hoverable?: boolean;
}

// 模态框组件属性
export interface ModalProps extends ComponentProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  size?: Size;
  closable?: boolean;
  maskClosable?: boolean;
  footer?: ReactNode;
  centered?: boolean;
}

// 表单组件属性
export interface FormProps extends HTMLAttributes<HTMLFormElement>, ComponentProps {
  layout?: 'horizontal' | 'vertical' | 'inline';
  labelWidth?: string;
  onSubmit?: (data: any) => void;
  onReset?: () => void;
}

// 输入框组件属性
export interface InputProps extends InputHTMLAttributes<HTMLInputElement>, ComponentProps {
  label?: string;
  error?: string;
  helperText?: string;
  prefix?: ReactNode;
  suffix?: ReactNode;
  variant?: 'default' | 'filled' | 'outlined';
  size?: Size;
  fullWidth?: boolean;
}

// 选择框组件属性
export interface SelectProps extends SelectHTMLAttributes<HTMLSelectElement>, ComponentProps {
  label?: string;
  error?: string;
  helperText?: string;
  options: Array<{
    value: string | number;
    label: string;
    disabled?: boolean;
  }>;
  placeholder?: string;
  size?: Size;
  fullWidth?: boolean;
}

// 复选框组件属性
export interface CheckboxProps extends ComponentProps {
  checked?: boolean;
  defaultChecked?: boolean;
  onChange?: (checked: boolean) => void;
  label?: string;
  disabled?: boolean;
  indeterminate?: boolean;
  size?: Size;
}

// 徽章组件属性
export interface BadgeProps extends ComponentProps {
  variant?: Variant;
  size?: Size;
  dot?: boolean;
  count?: number;
  showZero?: boolean;
  max?: number;
}

// 工具提示组件属性
export interface TooltipProps extends ComponentProps {
  title: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  trigger?: 'hover' | 'click' | 'focus';
  delay?: number;
  arrow?: boolean;
}

// 通知组件属性
export interface NotificationProps extends ComponentProps {
  type?: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  message: string;
  duration?: number;
  closable?: boolean;
  onClose?: () => void;
  action?: ReactNode;
}

// 加载组件属性
export interface LoadingProps extends ComponentProps {
  size?: Size;
  spinning?: boolean;
  tip?: string;
  delay?: number;
}

// 标签页组件属性
export interface TabsProps extends ComponentProps {
  activeKey?: string;
  defaultActiveKey?: string;
  onChange?: (key: string) => void;
  items: Array<{
    key: string;
    label: string;
    children: ReactNode;
    disabled?: boolean;
  }>;
  size?: Size;
  type?: 'line' | 'card';
}

// 动画类型
export interface AnimationConfig {
  duration?: number;
  easing?: string;
  delay?: number;
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
}

// 响应式断点
export interface Breakpoints {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
}

// 组件状态
export type ComponentState = 'default' | 'hover' | 'active' | 'focus' | 'disabled';

// 事件处理器类型
export type EventHandler<T = any> = (event: T) => void;

// 渲染函数类型
export type RenderFunction<T = any> = (props: T) => ReactNode;
