// ERP智能助手 - UI工具函数

import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { ComponentProps, AnimationConfig } from '../types';

// 类名合并工具
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// 创建组件工具
export function createComponent<T extends ComponentProps>(
  displayName: string,
  defaultProps?: Partial<T>
) {
  return function Component(props: T) {
    const mergedProps = { ...defaultProps, ...props };
    return mergedProps;
  };
}

// 动画工具
export function withAnimation<T extends ComponentProps>(
  Component: React.ComponentType<T>,
  animationConfig: AnimationConfig
) {
  return function AnimatedComponent(props: T) {
    const style = {
      ...props.style,
      transition: `all ${animationConfig.duration || 250}ms ${animationConfig.easing || 'ease'}`,
      animationDelay: animationConfig.delay ? `${animationConfig.delay}ms` : undefined,
      animationFillMode: animationConfig.fillMode || 'both',
    };

    return <Component {...props} style={style} />;
  };
}

// 尺寸映射
export const sizeMap = {
  xs: {
    padding: '4px 8px',
    fontSize: '12px',
    height: '24px',
  },
  sm: {
    padding: '6px 12px',
    fontSize: '14px',
    height: '32px',
  },
  md: {
    padding: '8px 16px',
    fontSize: '16px',
    height: '40px',
  },
  lg: {
    padding: '12px 20px',
    fontSize: '18px',
    height: '48px',
  },
  xl: {
    padding: '16px 24px',
    fontSize: '20px',
    height: '56px',
  },
};

// 变体样式映射
export const variantMap = {
  primary: {
    backgroundColor: 'var(--color-primary)',
    color: 'white',
    border: '1px solid var(--color-primary)',
  },
  secondary: {
    backgroundColor: 'var(--color-surface)',
    color: 'var(--color-text-primary)',
    border: '1px solid var(--color-border)',
  },
  success: {
    backgroundColor: 'var(--color-success)',
    color: 'white',
    border: '1px solid var(--color-success)',
  },
  warning: {
    backgroundColor: 'var(--color-warning)',
    color: 'white',
    border: '1px solid var(--color-warning)',
  },
  error: {
    backgroundColor: 'var(--color-error)',
    color: 'white',
    border: '1px solid var(--color-error)',
  },
  ghost: {
    backgroundColor: 'transparent',
    color: 'var(--color-text-primary)',
    border: '1px solid transparent',
  },
  outline: {
    backgroundColor: 'transparent',
    color: 'var(--color-primary)',
    border: '1px solid var(--color-primary)',
  },
};

// 响应式断点
export const breakpoints = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
};

// 媒体查询工具
export function createMediaQuery(breakpoint: keyof typeof breakpoints) {
  return `@media (min-width: ${breakpoints[breakpoint]}px)`;
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 生成唯一ID
export function generateId(prefix = 'erp-ui'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

// 检测设备类型
export function getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
  const width = window.innerWidth;
  
  if (width < 768) return 'mobile';
  if (width < 1024) return 'tablet';
  return 'desktop';
}

// 检测是否支持触摸
export function isTouchDevice(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

// 颜色工具
export const colorUtils = {
  // 十六进制转RGB
  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },

  // RGB转十六进制
  rgbToHex(r: number, g: number, b: number): string {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  },

  // 获取对比色
  getContrastColor(hex: string): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return '#000000';
    
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  },

  // 调整透明度
  withOpacity(color: string, opacity: number): string {
    const rgb = this.hexToRgb(color);
    if (!rgb) return color;
    
    return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity})`;
  },
};

// 动画工具
export const animationUtils = {
  // 淡入动画
  fadeIn: {
    duration: 250,
    easing: 'ease-out',
    keyframes: {
      from: { opacity: 0 },
      to: { opacity: 1 },
    },
  },

  // 淡出动画
  fadeOut: {
    duration: 250,
    easing: 'ease-in',
    keyframes: {
      from: { opacity: 1 },
      to: { opacity: 0 },
    },
  },

  // 滑入动画
  slideIn: {
    duration: 300,
    easing: 'ease-out',
    keyframes: {
      from: { transform: 'translateY(-10px)', opacity: 0 },
      to: { transform: 'translateY(0)', opacity: 1 },
    },
  },

  // 缩放动画
  scale: {
    duration: 200,
    easing: 'ease-out',
    keyframes: {
      from: { transform: 'scale(0.95)', opacity: 0 },
      to: { transform: 'scale(1)', opacity: 1 },
    },
  },

  // 弹跳动画
  bounce: {
    duration: 600,
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    keyframes: {
      from: { transform: 'scale(0.3)', opacity: 0 },
      '50%': { transform: 'scale(1.05)' },
      to: { transform: 'scale(1)', opacity: 1 },
    },
  },
};

// 可访问性工具
export const a11yUtils = {
  // 生成ARIA标签
  generateAriaLabel(element: HTMLElement): string {
    const tagName = element.tagName.toLowerCase();
    const type = element.getAttribute('type');
    const placeholder = element.getAttribute('placeholder');
    const title = element.getAttribute('title');
    
    if (placeholder) return placeholder;
    if (title) return title;
    
    const labelMap: { [key: string]: string } = {
      'button': '按钮',
      'input': type === 'password' ? '密码输入框' : '输入框',
      'select': '选择框',
      'textarea': '文本区域',
    };
    
    return labelMap[tagName] || '交互元素';
  },

  // 检查颜色对比度
  checkColorContrast(foreground: string, background: string): number {
    const getLuminance = (color: string) => {
      const rgb = colorUtils.hexToRgb(color);
      if (!rgb) return 0;
      
      const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      
      return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };
    
    const l1 = getLuminance(foreground);
    const l2 = getLuminance(background);
    
    return (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);
  },

  // 检查是否满足WCAG标准
  meetsWCAG(foreground: string, background: string, level: 'AA' | 'AAA' = 'AA'): boolean {
    const contrast = this.checkColorContrast(foreground, background);
    return level === 'AA' ? contrast >= 4.5 : contrast >= 7;
  },
};

// 表单验证工具
export const validationUtils = {
  // 邮箱验证
  isEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // 手机号验证
  isPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  // URL验证
  isUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  // 密码强度检查
  checkPasswordStrength(password: string): 'weak' | 'medium' | 'strong' {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[^a-zA-Z\d]/.test(password)) score++;
    
    if (score < 3) return 'weak';
    if (score < 5) return 'medium';
    return 'strong';
  },
};

// 导出所有工具
export {
  sizeMap,
  variantMap,
  breakpoints,
  createMediaQuery,
  debounce,
  throttle,
  generateId,
  getDeviceType,
  isTouchDevice,
  colorUtils,
  animationUtils,
  a11yUtils,
  validationUtils,
};
