// ERP智能助手 - DOM事件捕获器

import { MessageType, DOMEventData } from '@/types';
import { MessageBridge } from './message-bridge';
import { Logger } from '@/utils/logger';

export class DOMEventCapture {
  private messageBridge: MessageBridge;
  private logger: Logger;
  private isCapturing = false;
  private isPaused = false;
  private eventListeners: Map<string, EventListener> = new Map();
  private captureConfig = {
    captureClicks: true,
    captureInputs: true,
    captureFormSubmits: true,
    captureScrolls: false,
    captureMouseMoves: false,
    debounceDelay: 100,
    // 截图配置
    enableScreenshots: true,
    screenshotQuality: 'medium' as const,
    screenshotInterval: 1000,
    screenshotEvents: ['click', 'submit', 'change', 'beforeunload', 'error'],
    maxScreenshots: 50,
    enableSmartScreenshots: true
  };

  // 截图相关属性
  private lastScreenshotTime = 0;
  private screenshotCounter = 0;
  private screenshotStats = {
    captured: 0,
    skipped: 0,
    errors: 0
  };

  // 需要捕获的事件类型
  private readonly EVENT_TYPES = [
    'click',
    'dblclick',
    'input',
    'change',
    'submit',
    'focus',
    'blur',
    'keydown',
    'keyup'
  ] as const;

  // 需要忽略的元素选择器
  private readonly IGNORE_SELECTORS = [
    '.erp-assistant-overlay',
    '.erp-assistant-tooltip',
    '.erp-assistant-sidebar',
    '.erp-assistant-recording-indicator',
    '[data-erp-assistant-ignore]'
  ];

  constructor(messageBridge: MessageBridge) {
    this.messageBridge = messageBridge;
    this.logger = new Logger('DOMEventCapture');
  }

  /**
   * 初始化事件捕获器
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing DOM event capture...');
      
      // 设置事件监听器
      this.setupEventListeners();
      
      this.logger.info('DOM event capture initialized successfully');
      
    } catch (error) {
      this.logger.error('Failed to initialize DOM event capture:', error);
      throw error;
    }
  }

  /**
   * 开始捕获事件
   */
  async startCapture(): Promise<void> {
    if (this.isCapturing) {
      this.logger.warn('Event capture is already active');
      return;
    }

    this.isCapturing = true;
    this.isPaused = false;
    
    // 添加事件监听器到DOM
    this.attachEventListeners();
    
    this.logger.info('DOM event capture started');
  }

  /**
   * 停止捕获事件
   */
  async stopCapture(): Promise<void> {
    if (!this.isCapturing) {
      this.logger.warn('Event capture is not active');
      return;
    }

    this.isCapturing = false;
    this.isPaused = false;
    
    // 移除事件监听器
    this.detachEventListeners();
    
    this.logger.info('DOM event capture stopped');
  }

  /**
   * 暂停捕获事件
   */
  async pauseCapture(): Promise<void> {
    if (!this.isCapturing) {
      this.logger.warn('Event capture is not active');
      return;
    }

    this.isPaused = true;
    this.logger.info('DOM event capture paused');
  }

  /**
   * 恢复捕获事件
   */
  async resumeCapture(): Promise<void> {
    if (!this.isCapturing) {
      this.logger.warn('Event capture is not active');
      return;
    }

    this.isPaused = false;
    this.logger.info('DOM event capture resumed');
  }

  /**
   * 更新捕获配置
   */
  updateConfig(config: Partial<typeof this.captureConfig>): void {
    this.captureConfig = { ...this.captureConfig, ...config };
    this.logger.debug('Capture config updated:', this.captureConfig);
  }

  // 私有方法

  private setupEventListeners(): void {
    // 为每种事件类型创建监听器
    this.EVENT_TYPES.forEach(eventType => {
      const listener = this.createEventListener(eventType);
      this.eventListeners.set(eventType, listener);
    });
  }

  private createEventListener(eventType: string): EventListener {
    return (event: Event) => {
      this.handleDOMEvent(eventType, event);
    };
  }

  private attachEventListeners(): void {
    this.eventListeners.forEach((listener, eventType) => {
      document.addEventListener(eventType, listener, {
        capture: true,
        passive: true
      });
    });
  }

  private detachEventListeners(): void {
    this.eventListeners.forEach((listener, eventType) => {
      document.removeEventListener(eventType, listener, true);
    });
  }

  private async handleDOMEvent(eventType: string, event: Event): Promise<void> {
    // 检查是否应该捕获此事件
    if (!this.shouldCaptureEvent(eventType, event)) {
      return;
    }

    try {
      const eventData = this.extractEventData(eventType, event);

      if (eventData) {
        // 检查是否需要截图
        let screenshot: string | null = null;
        if (this.shouldTakeScreenshot(eventType, event)) {
          const importance = this.getEventImportance(eventType, event);
          screenshot = await this.requestSmartScreenshot(eventType, event.target as Element);
        }

        // 如果有截图，添加到事件数据中
        if (screenshot) {
          (eventData as any).screenshot = screenshot;
        }

        await this.messageBridge.sendMessage({
          type: MessageType.DOM_EVENT,
          payload: eventData
        });

        this.logger.debug(`Captured ${eventType} event:`, eventData.target.tagName, screenshot ? 'with screenshot' : '');
      }

    } catch (error) {
      this.logger.error('Failed to handle DOM event:', error);
    }
  }

  private shouldCaptureEvent(eventType: string, event: Event): boolean {
    // 检查是否正在捕获且未暂停
    if (!this.isCapturing || this.isPaused) {
      return false;
    }

    // 检查事件类型配置
    if (!this.isEventTypeEnabled(eventType)) {
      return false;
    }

    // 检查目标元素
    const target = event.target as Element;
    if (!target || !target.tagName) {
      return false;
    }

    // 忽略插件自身的元素
    if (this.shouldIgnoreElement(target)) {
      return false;
    }

    // 忽略某些特定事件
    if (this.shouldIgnoreSpecificEvent(eventType, event)) {
      return false;
    }

    return true;
  }

  private isEventTypeEnabled(eventType: string): boolean {
    switch (eventType) {
      case 'click':
      case 'dblclick':
        return this.captureConfig.captureClicks;
      case 'input':
      case 'change':
        return this.captureConfig.captureInputs;
      case 'submit':
        return this.captureConfig.captureFormSubmits;
      case 'scroll':
        return this.captureConfig.captureScrolls;
      case 'mousemove':
        return this.captureConfig.captureMouseMoves;
      default:
        return true;
    }
  }

  private shouldIgnoreElement(element: Element): boolean {
    // 检查是否匹配忽略选择器
    return this.IGNORE_SELECTORS.some(selector => {
      try {
        return element.matches(selector) || element.closest(selector);
      } catch (error) {
        return false;
      }
    });
  }

  private shouldIgnoreSpecificEvent(eventType: string, event: Event): boolean {
    // 忽略某些特定的事件情况
    
    // 忽略密码输入
    if (eventType === 'input' || eventType === 'change') {
      const target = event.target as HTMLInputElement;
      if (target && target.type === 'password') {
        return true;
      }
    }

    // 忽略快速连续的相同事件
    if (this.isRapidFireEvent(eventType, event)) {
      return true;
    }

    return false;
  }

  private isRapidFireEvent(eventType: string, event: Event): boolean {
    // 简单的防抖逻辑，可以根据需要扩展
    const now = Date.now();
    const key = `${eventType}_${(event.target as Element)?.tagName}`;
    
    if (!this.lastEventTimes) {
      this.lastEventTimes = new Map();
    }
    
    const lastTime = this.lastEventTimes.get(key) || 0;
    
    if (now - lastTime < this.captureConfig.debounceDelay) {
      return true;
    }
    
    this.lastEventTimes.set(key, now);
    return false;
  }

  private lastEventTimes: Map<string, number> = new Map();

  private extractEventData(eventType: string, event: Event): DOMEventData | null {
    const target = event.target as Element;
    
    if (!target) {
      return null;
    }

    try {
      const eventData: DOMEventData = {
        type: eventType,
        target: {
          tagName: target.tagName.toLowerCase(),
          id: target.id || undefined,
          className: target.className || undefined,
          textContent: this.getElementText(target),
          value: this.getElementValue(target),
          attributes: this.getElementAttributes(target)
        },
        timestamp: Date.now(),
        pageUrl: window.location.href,
        xpath: this.getElementXPath(target)
      };

      // 添加特定事件类型的额外数据
      this.addEventSpecificData(eventData, eventType, event);

      return eventData;

    } catch (error) {
      this.logger.error('Failed to extract event data:', error);
      return null;
    }
  }

  private getElementText(element: Element): string | undefined {
    if (!element.textContent) {
      return undefined;
    }
    
    // 限制文本长度并清理
    const text = element.textContent.trim();
    return text.length > 200 ? text.substring(0, 200) + '...' : text;
  }

  private getElementValue(element: Element): string | undefined {
    if (element instanceof HTMLInputElement || 
        element instanceof HTMLTextAreaElement || 
        element instanceof HTMLSelectElement) {
      
      // 对于密码字段，不记录实际值
      if (element instanceof HTMLInputElement && element.type === 'password') {
        return '[PASSWORD]';
      }
      
      // 限制值的长度
      const value = element.value;
      return value.length > 100 ? value.substring(0, 100) + '...' : value;
    }
    
    return undefined;
  }

  private getElementAttributes(element: Element): Record<string, string> {
    const attributes: Record<string, string> = {};
    const importantAttrs = ['name', 'type', 'placeholder', 'title', 'role', 'data-*'];
    
    for (const attr of element.attributes) {
      const name = attr.name.toLowerCase();
      
      // 只记录重要的属性
      if (importantAttrs.some(pattern => 
        pattern.endsWith('*') ? name.startsWith(pattern.slice(0, -1)) : name === pattern
      )) {
        attributes[name] = attr.value;
      }
    }
    
    return attributes;
  }

  private getElementXPath(element: Element): string {
    if (element.id) {
      return `//*[@id="${element.id}"]`;
    }
    
    const parts: string[] = [];
    let current: Element | null = element;
    
    while (current && current.nodeType === Node.ELEMENT_NODE) {
      let selector = current.tagName.toLowerCase();
      
      if (current.className) {
        const classes = current.className.trim().split(/\s+/).slice(0, 2); // 最多2个类名
        selector += '.' + classes.join('.');
      }
      
      // 添加位置信息（如果有同名兄弟元素）
      const siblings = Array.from(current.parentNode?.children || [])
        .filter(child => child.tagName === current!.tagName);
      
      if (siblings.length > 1) {
        const index = siblings.indexOf(current) + 1;
        selector += `[${index}]`;
      }
      
      parts.unshift(selector);
      current = current.parentElement;
      
      // 限制XPath深度
      if (parts.length > 10) {
        break;
      }
    }
    
    return '/' + parts.join('/');
  }

  private addEventSpecificData(eventData: DOMEventData, eventType: string, event: Event): void {
    // 根据事件类型添加特定数据
    switch (eventType) {
      case 'click':
      case 'dblclick':
        const mouseEvent = event as MouseEvent;
        (eventData as any).coordinates = {
          x: mouseEvent.clientX,
          y: mouseEvent.clientY
        };
        break;

      case 'keydown':
      case 'keyup':
        const keyEvent = event as KeyboardEvent;
        (eventData as any).key = {
          code: keyEvent.code,
          key: keyEvent.key,
          ctrlKey: keyEvent.ctrlKey,
          shiftKey: keyEvent.shiftKey,
          altKey: keyEvent.altKey
        };
        break;
    }
  }

  /**
   * 检查是否应该截图
   */
  private shouldTakeScreenshot(eventType: string, event: Event): boolean {
    if (!this.captureConfig.enableScreenshots) {
      return false;
    }

    // 检查截图数量限制
    if (this.screenshotCounter >= this.captureConfig.maxScreenshots) {
      this.screenshotStats.skipped++;
      return false;
    }

    // 检查时间间隔
    const now = Date.now();
    if (now - this.lastScreenshotTime < this.captureConfig.screenshotInterval) {
      this.screenshotStats.skipped++;
      return false;
    }

    // 检查事件类型
    return this.captureConfig.screenshotEvents.includes(eventType);
  }

  /**
   * 获取事件重要性
   */
  private getEventImportance(eventType: string, event: Event): 'low' | 'medium' | 'high' {
    const target = event.target as Element;

    // 高重要性事件
    const highImportanceEvents = ['submit', 'beforeunload', 'error'];
    if (highImportanceEvents.includes(eventType)) {
      return 'high';
    }

    // 检查是否是重要元素
    if (target && this.isImportantElement(target)) {
      return 'high';
    }

    // 中等重要性事件
    const mediumImportanceEvents = ['click', 'change'];
    if (mediumImportanceEvents.includes(eventType)) {
      return 'medium';
    }

    return 'low';
  }

  /**
   * 检查是否是重要元素
   */
  private isImportantElement(element: Element): boolean {
    const importantSelectors = [
      'button[type="submit"]',
      'input[type="submit"]',
      '.btn-primary',
      '.submit-btn',
      '[role="button"]',
      'a[href*="submit"]',
      'form button',
      '.modal button',
      '.dialog button'
    ];

    return importantSelectors.some(selector => {
      try {
        return element.matches(selector);
      } catch {
        return false;
      }
    });
  }

  /**
   * 请求智能截图
   */
  private async requestSmartScreenshot(eventType: string, element?: Element): Promise<string | null> {
    try {
      if (!this.captureConfig.enableSmartScreenshots) {
        return null;
      }

      // 获取元素边界信息
      let elementBounds;
      if (element) {
        const rect = element.getBoundingClientRect();
        elementBounds = {
          x: rect.left + window.scrollX,
          y: rect.top + window.scrollY,
          width: rect.width,
          height: rect.height
        };
      }

      // 确定触发类型
      const triggerMap: { [key: string]: 'user-action' | 'form-submit' | 'navigation' | 'error' } = {
        'click': 'user-action',
        'submit': 'form-submit',
        'change': 'user-action',
        'input': 'user-action',
        'beforeunload': 'navigation',
        'error': 'error'
      };

      const trigger = triggerMap[eventType] || 'user-action';

      // 请求截图
      const result = await this.messageBridge.requestSmartScreenshot(trigger);

      if (result.screenshot) {
        this.screenshotStats.captured++;
        this.screenshotCounter++;
        this.lastScreenshotTime = Date.now();

        this.logger.debug(`Smart screenshot captured for ${eventType} event`);
        return result.screenshot;
      }

      return null;

    } catch (error) {
      this.logger.error('Failed to request smart screenshot:', error);
      this.screenshotStats.errors++;
      return null;
    }
  }

  /**
   * 获取截图统计信息
   */
  getScreenshotStatistics(): {
    captured: number;
    skipped: number;
    errors: number;
    total: number;
    successRate: number;
  } {
    const total = this.screenshotStats.captured + this.screenshotStats.skipped + this.screenshotStats.errors;
    const successRate = total > 0 ? this.screenshotStats.captured / total : 0;

    return {
      ...this.screenshotStats,
      total,
      successRate
    };
  }

  /**
   * 重置截图统计
   */
  resetScreenshotStatistics(): void {
    this.screenshotStats = {
      captured: 0,
      skipped: 0,
      errors: 0
    };
    this.screenshotCounter = 0;
    this.lastScreenshotTime = 0;
  }
}
