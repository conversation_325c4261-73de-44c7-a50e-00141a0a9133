// ERP智能助手 - 消息桥接器

import { Message, MessageType, MessageHandler } from '@/types';
import { Logger } from '@/utils/logger';

export class MessageBridge {
  private logger: Logger;
  private handlers = new Map<string, MessageHandler[]>();
  private isInitialized = false;
  private messageQueue: Message[] = [];
  private connectionStatus: 'connected' | 'disconnected' | 'connecting' = 'disconnected';

  constructor() {
    this.logger = new Logger('MessageBridge');
  }

  /**
   * 初始化消息桥接器
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing message bridge...');
      
      // 设置消息监听器
      this.setupMessageListener();
      
      // 测试与后台脚本的连接
      await this.testConnection();
      
      // 处理队列中的消息
      await this.processMessageQueue();
      
      this.isInitialized = true;
      this.logger.info('Message bridge initialized successfully');
      
    } catch (error) {
      this.logger.error('Failed to initialize message bridge:', error);
      throw error;
    }
  }

  /**
   * 添加消息处理器
   */
  addHandler(messageType: MessageType | string, handler: MessageHandler): void {
    const type = typeof messageType === 'string' ? messageType : messageType.toString();
    
    if (!this.handlers.has(type)) {
      this.handlers.set(type, []);
    }
    
    this.handlers.get(type)!.push(handler);
    this.logger.debug(`Added handler for message type: ${type}`);
  }

  /**
   * 移除消息处理器
   */
  removeHandler(messageType: MessageType | string, handler: MessageHandler): void {
    const type = typeof messageType === 'string' ? messageType : messageType.toString();
    const handlers = this.handlers.get(type);
    
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
        this.logger.debug(`Removed handler for message type: ${type}`);
        
        if (handlers.length === 0) {
          this.handlers.delete(type);
        }
      }
    }
  }

  /**
   * 发送消息到后台脚本
   */
  async sendMessage(message: Message): Promise<any> {
    try {
      // 如果未初始化，将消息加入队列
      if (!this.isInitialized) {
        this.messageQueue.push(message);
        this.logger.debug('Message queued (not initialized):', message.type);
        return;
      }

      // 添加时间戳和ID
      if (!message.timestamp) {
        message.timestamp = Date.now();
      }
      if (!message.id) {
        message.id = this.generateMessageId();
      }

      this.logger.debug('Sending message:', message.type, message.id);

      // 发送消息并等待响应
      const response = await chrome.runtime.sendMessage(message);
      
      if (response && response.success === false) {
        throw new Error(response.error || 'Unknown error');
      }
      
      return response;

    } catch (error) {
      this.logger.error('Failed to send message:', error);
      
      // 如果是连接错误，更新连接状态
      if (error.message?.includes('Extension context invalidated') || 
          error.message?.includes('Could not establish connection')) {
        this.connectionStatus = 'disconnected';
      }
      
      throw error;
    }
  }

  /**
   * 请求截图
   */
  async requestScreenshot(options?: {
    quality?: 'high' | 'medium' | 'low';
    elementBounds?: { x: number; y: number; width: number; height: number };
    includeContext?: boolean;
  }): Promise<string | null> {
    try {
      const payload = {
        pageUrl: window.location.href,
        timestamp: Date.now(),
        quality: options?.quality || 'medium',
        elementBounds: options?.elementBounds,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        scrollPosition: {
          x: window.scrollX,
          y: window.scrollY
        }
      };

      if (options?.includeContext) {
        payload.context = {
          title: document.title,
          userAgent: navigator.userAgent,
          activeElement: document.activeElement?.tagName.toLowerCase(),
          focusedElement: this.getElementXPath(document.activeElement)
        };
      }

      const response = await this.sendMessage({
        type: MessageType.SCREENSHOT_TAKEN,
        payload
      });

      return response?.screenshot || null;

    } catch (error) {
      this.logger.error('Failed to request screenshot:', error);
      return null;
    }
  }

  /**
   * 智能截图请求 - 根据当前页面状态自动优化
   */
  async requestSmartScreenshot(trigger?: 'user-action' | 'form-submit' | 'navigation' | 'error'): Promise<{
    screenshot: string | null;
    metadata: any;
  }> {
    try {
      // 根据触发类型决定截图质量和包含的信息
      const qualityMap = {
        'user-action': 'medium' as const,
        'form-submit': 'high' as const,
        'navigation': 'medium' as const,
        'error': 'high' as const
      };

      const quality = trigger ? qualityMap[trigger] : 'medium';

      // 获取当前焦点元素的边界
      const activeElement = document.activeElement;
      let elementBounds;

      if (activeElement && activeElement !== document.body) {
        const rect = activeElement.getBoundingClientRect();
        elementBounds = {
          x: rect.left + window.scrollX,
          y: rect.top + window.scrollY,
          width: rect.width,
          height: rect.height
        };
      }

      const screenshot = await this.requestScreenshot({
        quality,
        elementBounds,
        includeContext: true
      });

      const metadata = {
        trigger,
        timestamp: Date.now(),
        pageUrl: window.location.href,
        pageTitle: document.title,
        activeElement: activeElement?.tagName.toLowerCase(),
        formData: this.extractFormData(),
        pageMetrics: this.getPageMetrics()
      };

      return { screenshot, metadata };

    } catch (error) {
      this.logger.error('Failed to request smart screenshot:', error);
      return { screenshot: null, metadata: {} };
    }
  }

  /**
   * 获取元素XPath
   */
  private getElementXPath(element: Element | null): string | undefined {
    if (!element) return undefined;

    const getElementIndex = (element: Element): number => {
      let index = 1;
      let sibling = element.previousElementSibling;
      while (sibling) {
        if (sibling.tagName === element.tagName) {
          index++;
        }
        sibling = sibling.previousElementSibling;
      }
      return index;
    };

    const parts: string[] = [];
    let current: Element | null = element;

    while (current && current !== document.documentElement) {
      let part = current.tagName.toLowerCase();

      if (current.id) {
        part += `[@id="${current.id}"]`;
      } else {
        const index = getElementIndex(current);
        if (index > 1) {
          part += `[${index}]`;
        }
      }

      parts.unshift(part);
      current = current.parentElement;
    }

    return parts.length > 0 ? '/' + parts.join('/') : undefined;
  }

  /**
   * 提取表单数据
   */
  private extractFormData(): any {
    const forms = document.querySelectorAll('form');
    const formData: any = {};

    forms.forEach((form, index) => {
      const formId = form.id || `form-${index}`;
      formData[formId] = {
        action: form.action,
        method: form.method,
        fieldCount: form.elements.length,
        hasFileInput: form.querySelector('input[type="file"]') !== null,
        hasPasswordInput: form.querySelector('input[type="password"]') !== null
      };
    });

    return formData;
  }

  /**
   * 获取页面性能指标
   */
  private getPageMetrics(): any {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

    return {
      loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
      domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
      firstPaint: this.getFirstPaint(),
      scrollPosition: { x: window.scrollX, y: window.scrollY },
      viewport: { width: window.innerWidth, height: window.innerHeight },
      documentSize: {
        width: document.documentElement.scrollWidth,
        height: document.documentElement.scrollHeight
      }
    };
  }

  /**
   * 获取首次绘制时间
   */
  private getFirstPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : 0;
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): 'connected' | 'disconnected' | 'connecting' {
    return this.connectionStatus;
  }

  /**
   * 重新连接到后台脚本
   */
  async reconnect(): Promise<void> {
    try {
      this.connectionStatus = 'connecting';
      await this.testConnection();
      this.connectionStatus = 'connected';
      
      // 处理队列中的消息
      await this.processMessageQueue();
      
      this.logger.info('Reconnected to background script');
      
    } catch (error) {
      this.connectionStatus = 'disconnected';
      this.logger.error('Failed to reconnect:', error);
      throw error;
    }
  }

  // 私有方法

  private setupMessageListener(): void {
    // 监听来自后台脚本的消息
    chrome.runtime.onMessage.addListener((message: Message, sender, sendResponse) => {
      this.handleIncomingMessage(message, sender, sendResponse);
      return true; // 表示会异步调用sendResponse
    });
  }

  private async handleIncomingMessage(
    message: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      this.logger.debug('Received message:', message.type, message.id);

      // 验证消息格式
      if (!this.isValidMessage(message)) {
        this.logger.warn('Invalid message format:', message);
        sendResponse({ success: false, error: 'Invalid message format' });
        return;
      }

      // 查找处理器
      const handlers = this.handlers.get(message.type);
      if (!handlers || handlers.length === 0) {
        this.logger.warn(`No handlers found for message type: ${message.type}`);
        sendResponse({ success: false, error: `No handler for message type: ${message.type}` });
        return;
      }

      // 执行处理器
      let hasAsyncHandler = false;
      for (const handler of handlers) {
        try {
          const result = await handler(message, sender, sendResponse);
          if (result === true) {
            hasAsyncHandler = true;
          }
        } catch (error) {
          this.logger.error(`Handler error for message type ${message.type}:`, error);
          if (!hasAsyncHandler) {
            sendResponse({ 
              success: false, 
              error: 'Handler execution failed',
              details: error.message 
            });
          }
          return;
        }
      }

      // 如果没有异步处理器，发送成功响应
      if (!hasAsyncHandler) {
        sendResponse({ success: true });
      }

    } catch (error) {
      this.logger.error('Error handling incoming message:', error);
      sendResponse({ 
        success: false, 
        error: 'Message handling failed',
        details: error.message 
      });
    }
  }

  private async testConnection(): Promise<void> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: MessageType.PING,
        timestamp: Date.now()
      });
      
      if (response && response.type === MessageType.PONG) {
        this.connectionStatus = 'connected';
        this.logger.debug('Connection test successful');
      } else {
        throw new Error('Invalid ping response');
      }
      
    } catch (error) {
      this.connectionStatus = 'disconnected';
      this.logger.error('Connection test failed:', error);
      throw error;
    }
  }

  private async processMessageQueue(): Promise<void> {
    if (this.messageQueue.length === 0) {
      return;
    }

    this.logger.info(`Processing ${this.messageQueue.length} queued messages`);
    
    const messages = [...this.messageQueue];
    this.messageQueue = [];
    
    for (const message of messages) {
      try {
        await this.sendMessage(message);
      } catch (error) {
        this.logger.error('Failed to send queued message:', error);
        // 重新加入队列
        this.messageQueue.push(message);
      }
    }
  }

  private isValidMessage(message: any): message is Message {
    return (
      message &&
      typeof message === 'object' &&
      typeof message.type === 'string' &&
      message.type.length > 0
    );
  }

  private generateMessageId(): string {
    return `msg_content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 批量发送消息
   */
  async sendBatchMessages(messages: Message[]): Promise<any[]> {
    const results: any[] = [];
    
    for (const message of messages) {
      try {
        const result = await this.sendMessage(message);
        results.push({ success: true, result });
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    
    return results;
  }

  /**
   * 发送消息并忽略错误
   */
  sendMessageSilent(message: Message): void {
    this.sendMessage(message).catch(error => {
      this.logger.debug('Silent message failed (ignored):', error);
    });
  }

  /**
   * 等待特定类型的消息
   */
  async waitForMessage(messageType: MessageType | string, timeout = 5000): Promise<Message> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.removeHandler(messageType, handler);
        reject(new Error(`Timeout waiting for message: ${messageType}`));
      }, timeout);

      const handler: MessageHandler = (message: Message) => {
        clearTimeout(timeoutId);
        this.removeHandler(messageType, handler);
        resolve(message);
      };

      this.addHandler(messageType, handler);
    });
  }

  /**
   * 检查后台脚本是否可用
   */
  async isBackgroundScriptAvailable(): Promise<boolean> {
    try {
      await this.testConnection();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取消息统计信息
   */
  getStats(): {
    handlersCount: number;
    queuedMessages: number;
    connectionStatus: string;
  } {
    return {
      handlersCount: Array.from(this.handlers.values()).reduce((sum, handlers) => sum + handlers.length, 0),
      queuedMessages: this.messageQueue.length,
      connectionStatus: this.connectionStatus
    };
  }
}
