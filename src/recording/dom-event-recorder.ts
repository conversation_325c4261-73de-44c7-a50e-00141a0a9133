// ERP智能助手 - DOM事件录制引擎

import { DOMEventData, MessageType, RecordingStatus } from '@/types';
import { Logger } from '@/utils/logger';

export interface RecordingConfig {
  // 事件类型配置
  captureClicks: boolean;
  captureInputs: boolean;
  captureFormSubmits: boolean;
  captureScrolls: boolean;
  captureMouseMoves: boolean;
  captureKeyboard: boolean;
  captureFocus: boolean;

  // 性能配置
  debounceDelay: number;
  maxEventsPerSecond: number;

  // 截图配置
  includeScreenshots: boolean;
  screenshotQuality: 'high' | 'medium' | 'low';
  screenshotInterval: number; // 最小截图间隔（毫秒）
  screenshotEvents: string[]; // 触发截图的事件类型
  maxScreenshots: number; // 单次录制最大截图数量
  enableSmartScreenshots: boolean; // 启用智能截图

  // 数据配置
  maxTextLength: number;

  // 过滤配置
  ignoreSelectors: string[];
  sensitiveSelectors: string[];

  // 高级配置
  trackElementChanges: boolean;
  trackPagePerformance: boolean;
  enableSmartFiltering: boolean;
}

export interface RecordedEvent {
  id: string;
  sessionId: string;
  timestamp: number;
  type: string;
  data: DOMEventData;
  screenshot?: string;
  performance?: PerformanceData;
  context?: EventContext;
}

export interface PerformanceData {
  memoryUsage?: number;
  renderTime?: number;
  networkRequests?: number;
  domNodes?: number;
}

export interface EventContext {
  userAgent: string;
  viewport: { width: number; height: number };
  scrollPosition: { x: number; y: number };
  activeElement?: string;
  focusedElement?: string;
}

export class DOMEventRecorder {
  private logger: Logger;
  private config: RecordingConfig;
  private isRecording = false;
  private isPaused = false;
  private sessionId: string | null = null;
  private eventListeners = new Map<string, EventListener>();
  private eventQueue: RecordedEvent[] = [];
  private lastEventTime = 0;
  private eventCounter = 0;
  private performanceObserver?: PerformanceObserver;
  private mutationObserver?: MutationObserver;

  // 截图相关属性
  private lastScreenshotTime = 0;
  private screenshotCounter = 0;
  private messageBridge: any; // MessageBridge实例
  private statistics = {
    screenshotsCaptured: 0,
    screenshotsSkipped: 0,
    totalEvents: 0
  };

  // 默认配置
  private static readonly DEFAULT_CONFIG: RecordingConfig = {
    captureClicks: true,
    captureInputs: true,
    captureFormSubmits: true,
    captureScrolls: false,
    captureMouseMoves: false,
    captureKeyboard: true,
    captureFocus: true,
    debounceDelay: 100,
    maxEventsPerSecond: 50,
    includeScreenshots: true,
    screenshotQuality: 'medium',
    screenshotInterval: 1000, // 1秒最小间隔
    screenshotEvents: ['click', 'submit', 'change', 'beforeunload', 'error'],
    maxScreenshots: 50, // 单次录制最多50张截图
    enableSmartScreenshots: true,
    maxTextLength: 500,
    ignoreSelectors: [
      '.erp-assistant-overlay',
      '.erp-assistant-tooltip',
      '.erp-assistant-sidebar',
      '[data-erp-assistant-ignore]'
    ],
    sensitiveSelectors: [
      'input[type="password"]',
      'input[name*="password"]',
      'input[name*="ssn"]',
      'input[name*="credit"]',
      '[data-sensitive]'
    ],
    trackElementChanges: true,
    trackPagePerformance: true,
    enableSmartFiltering: true
  };

  // 需要捕获的事件类型
  private readonly EVENT_TYPES = [
    'click',
    'dblclick',
    'mousedown',
    'mouseup',
    'mouseover',
    'mouseout',
    'input',
    'change',
    'submit',
    'focus',
    'blur',
    'keydown',
    'keyup',
    'keypress',
    'scroll',
    'resize',
    'load',
    'unload',
    'beforeunload'
  ] as const;

  constructor(config?: Partial<RecordingConfig>, messageBridge?: any) {
    this.logger = new Logger('DOMEventRecorder');
    this.config = { ...DOMEventRecorder.DEFAULT_CONFIG, ...config };
    this.messageBridge = messageBridge;

    // 初始化统计信息
    this.statistics = {
      screenshotsCaptured: 0,
      screenshotsSkipped: 0,
      totalEvents: 0
    };
  }

  /**
   * 初始化录制引擎
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing DOM event recorder...');
      
      // 设置事件监听器
      this.setupEventListeners();
      
      // 设置性能监控
      if (this.config.trackPagePerformance) {
        this.setupPerformanceMonitoring();
      }
      
      // 设置DOM变化监控
      if (this.config.trackElementChanges) {
        this.setupMutationObserver();
      }
      
      this.logger.info('DOM event recorder initialized successfully');
      
    } catch (error) {
      this.logger.error('Failed to initialize DOM event recorder:', error);
      throw error;
    }
  }

  /**
   * 开始录制
   */
  async startRecording(sessionId: string): Promise<void> {
    if (this.isRecording) {
      this.logger.warn('Recording is already active');
      return;
    }

    try {
      this.sessionId = sessionId;
      this.isRecording = true;
      this.isPaused = false;
      this.eventCounter = 0;
      this.eventQueue = [];
      
      // 附加事件监听器
      this.attachEventListeners();
      
      // 记录初始页面状态
      await this.recordInitialState();
      
      this.logger.info(`DOM event recording started for session: ${sessionId}`);
      
    } catch (error) {
      this.logger.error('Failed to start recording:', error);
      this.isRecording = false;
      throw error;
    }
  }

  /**
   * 停止录制
   */
  async stopRecording(): Promise<RecordedEvent[]> {
    if (!this.isRecording) {
      this.logger.warn('No active recording to stop');
      return [];
    }

    try {
      this.isRecording = false;
      this.isPaused = false;
      
      // 分离事件监听器
      this.detachEventListeners();
      
      // 记录最终状态
      await this.recordFinalState();
      
      // 处理队列中的剩余事件
      const events = [...this.eventQueue];
      this.eventQueue = [];
      
      this.logger.info(`DOM event recording stopped. Recorded ${events.length} events`);
      
      return events;
      
    } catch (error) {
      this.logger.error('Failed to stop recording:', error);
      throw error;
    }
  }

  /**
   * 暂停录制
   */
  pauseRecording(): void {
    if (!this.isRecording) {
      this.logger.warn('No active recording to pause');
      return;
    }

    this.isPaused = true;
    this.logger.info('DOM event recording paused');
  }

  /**
   * 恢复录制
   */
  resumeRecording(): void {
    if (!this.isRecording) {
      this.logger.warn('No active recording to resume');
      return;
    }

    this.isPaused = false;
    this.logger.info('DOM event recording resumed');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<RecordingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.debug('Recording config updated:', newConfig);
  }

  /**
   * 获取当前状态
   */
  getStatus(): {
    isRecording: boolean;
    isPaused: boolean;
    sessionId: string | null;
    eventCount: number;
    queueSize: number;
  } {
    return {
      isRecording: this.isRecording,
      isPaused: this.isPaused,
      sessionId: this.sessionId,
      eventCount: this.eventCounter,
      queueSize: this.eventQueue.length
    };
  }

  // 私有方法

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.EVENT_TYPES.forEach(eventType => {
      const listener = this.createEventListener(eventType);
      this.eventListeners.set(eventType, listener);
    });
  }

  /**
   * 创建事件监听器
   */
  private createEventListener(eventType: string): EventListener {
    return (event: Event) => {
      this.handleDOMEvent(eventType, event);
    };
  }

  /**
   * 附加事件监听器
   */
  private attachEventListeners(): void {
    this.eventListeners.forEach((listener, eventType) => {
      if (this.shouldCaptureEventType(eventType)) {
        document.addEventListener(eventType, listener, {
          capture: true,
          passive: true
        });
      }
    });
  }

  /**
   * 分离事件监听器
   */
  private detachEventListeners(): void {
    this.eventListeners.forEach((listener, eventType) => {
      document.removeEventListener(eventType, listener, true);
    });
  }

  /**
   * 处理DOM事件
   */
  private async handleDOMEvent(eventType: string, event: Event): Promise<void> {
    // 检查是否应该捕获此事件
    if (!this.shouldCaptureEvent(eventType, event)) {
      return;
    }

    try {
      // 限流检查
      if (!this.checkRateLimit()) {
        return;
      }

      const eventData = await this.extractEventData(eventType, event);
      if (!eventData) {
        return;
      }

      const recordedEvent: RecordedEvent = {
        id: this.generateEventId(),
        sessionId: this.sessionId!,
        timestamp: Date.now(),
        type: eventType,
        data: eventData,
        context: this.getEventContext()
      };

      // 添加智能截图（如果需要）
      if (this.config.enableSmartScreenshots && this.shouldCaptureScreenshot(eventType, event)) {
        const importance = this.getEventImportance(eventType, event);
        recordedEvent.screenshot = await this.takeSmartScreenshot(eventType, event.target as Element, importance);

        // 更新截图时间戳
        if (recordedEvent.screenshot) {
          this.lastScreenshotTime = Date.now();
          this.screenshotCounter++;
        }
      }

      // 添加性能数据
      if (this.config.trackPagePerformance) {
        recordedEvent.performance = this.getPerformanceData();
      }

      // 添加到队列
      this.eventQueue.push(recordedEvent);
      this.eventCounter++;

      this.logger.debug(`Recorded ${eventType} event:`, recordedEvent.id);

    } catch (error) {
      this.logger.error('Failed to handle DOM event:', error);
    }
  }

  /**
   * 检查是否应该捕获事件
   */
  private shouldCaptureEvent(eventType: string, event: Event): boolean {
    // 检查录制状态
    if (!this.isRecording || this.isPaused) {
      return false;
    }

    // 检查事件类型
    if (!this.shouldCaptureEventType(eventType)) {
      return false;
    }

    // 检查目标元素
    const target = event.target as Element;
    if (!target || !target.tagName) {
      return false;
    }

    // 检查忽略选择器
    if (this.shouldIgnoreElement(target)) {
      return false;
    }

    // 智能过滤
    if (this.config.enableSmartFiltering && !this.passesSmartFilter(eventType, event)) {
      return false;
    }

    return true;
  }

  /**
   * 检查是否应该捕获特定事件类型
   */
  private shouldCaptureEventType(eventType: string): boolean {
    const typeMap: { [key: string]: keyof RecordingConfig } = {
      'click': 'captureClicks',
      'dblclick': 'captureClicks',
      'mousedown': 'captureClicks',
      'mouseup': 'captureClicks',
      'input': 'captureInputs',
      'change': 'captureInputs',
      'submit': 'captureFormSubmits',
      'scroll': 'captureScrolls',
      'mouseover': 'captureMouseMoves',
      'mouseout': 'captureMouseMoves',
      'keydown': 'captureKeyboard',
      'keyup': 'captureKeyboard',
      'keypress': 'captureKeyboard',
      'focus': 'captureFocus',
      'blur': 'captureFocus'
    };

    const configKey = typeMap[eventType];
    return configKey ? this.config[configKey] as boolean : true;
  }

  /**
   * 检查是否应该忽略元素
   */
  private shouldIgnoreElement(element: Element): boolean {
    return this.config.ignoreSelectors.some(selector => {
      try {
        return element.matches(selector) || element.closest(selector);
      } catch (error) {
        return false;
      }
    });
  }

  /**
   * 智能过滤检查
   */
  private passesSmartFilter(eventType: string, event: Event): boolean {
    // 过滤快速连续的相同事件
    if (this.isRapidFireEvent(eventType, event)) {
      return false;
    }

    // 过滤无意义的鼠标移动
    if (eventType === 'mouseover' || eventType === 'mouseout') {
      return this.isSignificantMouseEvent(event as MouseEvent);
    }

    // 过滤空的输入事件
    if (eventType === 'input') {
      const target = event.target as HTMLInputElement;
      return target && target.value.length > 0;
    }

    return true;
  }

  /**
   * 检查是否为快速连续事件
   */
  private isRapidFireEvent(eventType: string, event: Event): boolean {
    const now = Date.now();
    const key = `${eventType}_${this.getElementKey(event.target as Element)}`;
    
    if (!this.lastEventTimes) {
      this.lastEventTimes = new Map();
    }
    
    const lastTime = this.lastEventTimes.get(key) || 0;
    
    if (now - lastTime < this.config.debounceDelay) {
      return true;
    }
    
    this.lastEventTimes.set(key, now);
    return false;
  }

  private lastEventTimes: Map<string, number> = new Map();

  /**
   * 检查是否为有意义的鼠标事件
   */
  private isSignificantMouseEvent(event: MouseEvent): boolean {
    // 检查鼠标是否移动了足够的距离
    if (!this.lastMousePosition) {
      this.lastMousePosition = { x: event.clientX, y: event.clientY };
      return true;
    }

    const distance = Math.sqrt(
      Math.pow(event.clientX - this.lastMousePosition.x, 2) +
      Math.pow(event.clientY - this.lastMousePosition.y, 2)
    );

    this.lastMousePosition = { x: event.clientX, y: event.clientY };
    return distance > 10; // 移动超过10像素才记录
  }

  private lastMousePosition: { x: number; y: number } | null = null;

  /**
   * 检查限流
   */
  private checkRateLimit(): boolean {
    const now = Date.now();
    const timeSinceLastEvent = now - this.lastEventTime;
    const minInterval = 1000 / this.config.maxEventsPerSecond;

    if (timeSinceLastEvent < minInterval) {
      return false;
    }

    this.lastEventTime = now;
    return true;
  }

  /**
   * 提取事件数据
   */
  private async extractEventData(eventType: string, event: Event): Promise<DOMEventData | null> {
    const target = event.target as Element;
    
    if (!target) {
      return null;
    }

    try {
      const eventData: DOMEventData = {
        type: eventType,
        target: {
          tagName: target.tagName.toLowerCase(),
          id: target.id || undefined,
          className: target.className || undefined,
          textContent: this.getElementText(target),
          value: this.getElementValue(target),
          attributes: this.getElementAttributes(target)
        },
        timestamp: Date.now(),
        pageUrl: window.location.href,
        xpath: this.getElementXPath(target)
      };

      // 添加事件特定数据
      this.addEventSpecificData(eventData, eventType, event);

      return eventData;

    } catch (error) {
      this.logger.error('Failed to extract event data:', error);
      return null;
    }
  }

  /**
   * 获取元素文本
   */
  private getElementText(element: Element): string | undefined {
    if (!element.textContent) {
      return undefined;
    }
    
    const text = element.textContent.trim();
    if (text.length === 0) {
      return undefined;
    }
    
    // 限制文本长度
    return text.length > this.config.maxTextLength 
      ? text.substring(0, this.config.maxTextLength) + '...' 
      : text;
  }

  /**
   * 获取元素值
   */
  private getElementValue(element: Element): string | undefined {
    if (element instanceof HTMLInputElement || 
        element instanceof HTMLTextAreaElement || 
        element instanceof HTMLSelectElement) {
      
      // 检查是否为敏感字段
      if (this.isSensitiveElement(element)) {
        return '[SENSITIVE_DATA]';
      }
      
      const value = element.value;
      return value.length > this.config.maxTextLength 
        ? value.substring(0, this.config.maxTextLength) + '...' 
        : value;
    }
    
    return undefined;
  }

  /**
   * 检查是否为敏感元素
   */
  private isSensitiveElement(element: Element): boolean {
    return this.config.sensitiveSelectors.some(selector => {
      try {
        return element.matches(selector);
      } catch (error) {
        return false;
      }
    });
  }

  /**
   * 获取元素属性
   */
  private getElementAttributes(element: Element): Record<string, string> {
    const attributes: Record<string, string> = {};
    const importantAttrs = ['name', 'type', 'placeholder', 'title', 'role', 'aria-label', 'data-*'];
    
    for (const attr of element.attributes) {
      const name = attr.name.toLowerCase();
      
      if (importantAttrs.some(pattern => 
        pattern.endsWith('*') ? name.startsWith(pattern.slice(0, -1)) : name === pattern
      )) {
        attributes[name] = attr.value;
      }
    }
    
    return attributes;
  }

  /**
   * 获取元素XPath
   */
  private getElementXPath(element: Element): string {
    if (element.id) {
      return `//*[@id="${element.id}"]`;
    }
    
    const parts: string[] = [];
    let current: Element | null = element;
    
    while (current && current.nodeType === Node.ELEMENT_NODE) {
      let selector = current.tagName.toLowerCase();
      
      if (current.className) {
        const classes = current.className.trim().split(/\s+/).slice(0, 2);
        selector += '.' + classes.join('.');
      }
      
      const siblings = Array.from(current.parentNode?.children || [])
        .filter(child => child.tagName === current!.tagName);
      
      if (siblings.length > 1) {
        const index = siblings.indexOf(current) + 1;
        selector += `[${index}]`;
      }
      
      parts.unshift(selector);
      current = current.parentElement;
      
      if (parts.length > 10) break;
    }
    
    return '/' + parts.join('/');
  }

  /**
   * 添加事件特定数据
   */
  private addEventSpecificData(eventData: DOMEventData, eventType: string, event: Event): void {
    switch (eventType) {
      case 'click':
      case 'dblclick':
      case 'mousedown':
      case 'mouseup':
        const mouseEvent = event as MouseEvent;
        (eventData as any).coordinates = {
          x: mouseEvent.clientX,
          y: mouseEvent.clientY,
          pageX: mouseEvent.pageX,
          pageY: mouseEvent.pageY
        };
        (eventData as any).button = mouseEvent.button;
        (eventData as any).buttons = mouseEvent.buttons;
        break;
        
      case 'keydown':
      case 'keyup':
      case 'keypress':
        const keyEvent = event as KeyboardEvent;
        (eventData as any).key = {
          code: keyEvent.code,
          key: keyEvent.key,
          ctrlKey: keyEvent.ctrlKey,
          shiftKey: keyEvent.shiftKey,
          altKey: keyEvent.altKey,
          metaKey: keyEvent.metaKey
        };
        break;
        
      case 'scroll':
        (eventData as any).scroll = {
          x: window.scrollX,
          y: window.scrollY
        };
        break;
        
      case 'resize':
        (eventData as any).viewport = {
          width: window.innerWidth,
          height: window.innerHeight
        };
        break;
    }
  }

  /**
   * 检查是否应该捕获截图
   */
  private shouldCaptureScreenshot(eventType: string, event: Event): boolean {
    if (!this.config.includeScreenshots) {
      return false;
    }

    // 检查截图数量限制
    if (this.screenshotCounter >= this.config.maxScreenshots) {
      this.statistics.screenshotsSkipped++;
      return false;
    }

    // 检查时间间隔
    const now = Date.now();
    if (now - this.lastScreenshotTime < this.config.screenshotInterval) {
      this.statistics.screenshotsSkipped++;
      return false;
    }

    // 检查事件类型
    return this.config.screenshotEvents.includes(eventType);
  }

  /**
   * 获取事件重要性级别
   */
  private getEventImportance(eventType: string, event: Event): 'low' | 'medium' | 'high' {
    // 高重要性事件
    const highImportanceEvents = ['submit', 'beforeunload', 'error'];
    if (highImportanceEvents.includes(eventType)) {
      return 'high';
    }

    // 中等重要性事件
    const mediumImportanceEvents = ['click', 'change'];
    if (mediumImportanceEvents.includes(eventType)) {
      // 检查是否是重要元素
      const target = event.target as Element;
      if (target && this.isImportantElement(target)) {
        return 'high';
      }
      return 'medium';
    }

    // 低重要性事件
    return 'low';
  }

  /**
   * 检查是否是重要元素
   */
  private isImportantElement(element: Element): boolean {
    const importantSelectors = [
      'button[type="submit"]',
      'input[type="submit"]',
      '.btn-primary',
      '.submit-btn',
      '[role="button"]',
      'a[href*="submit"]',
      'form button'
    ];

    return importantSelectors.some(selector => {
      try {
        return element.matches(selector);
      } catch {
        return false;
      }
    });
  }

  /**
   * 截图
   */
  private async takeScreenshot(element?: Element): Promise<string | undefined> {
    try {
      if (!this.messageBridge) {
        this.logger.warn('Message bridge not available for screenshot');
        return undefined;
      }

      // 获取元素边界信息（如果提供了元素）
      let elementBounds;
      if (element) {
        const rect = element.getBoundingClientRect();
        elementBounds = {
          x: rect.left + window.scrollX,
          y: rect.top + window.scrollY,
          width: rect.width,
          height: rect.height
        };
      }

      // 请求截图
      const screenshot = await this.messageBridge.requestScreenshot({
        quality: this.config.screenshotQuality || 'medium',
        elementBounds,
        includeContext: true
      });

      if (screenshot) {
        this.logger.debug('Screenshot captured successfully');

        // 更新统计信息
        this.statistics.screenshotsCaptured++;

        return screenshot;
      }

      return undefined;

    } catch (error) {
      this.logger.error('Failed to take screenshot:', error);
      return undefined;
    }
  }

  /**
   * 智能截图 - 根据操作类型和重要性决定是否截图
   */
  private async takeSmartScreenshot(
    eventType: string,
    element?: Element,
    importance: 'low' | 'medium' | 'high' = 'medium'
  ): Promise<string | undefined> {
    try {
      // 检查是否应该截图
      if (!this.shouldTakeScreenshot(eventType, importance)) {
        return undefined;
      }

      if (!this.messageBridge) {
        return undefined;
      }

      // 根据事件类型确定截图触发类型
      const triggerMap: { [key: string]: 'user-action' | 'form-submit' | 'navigation' | 'error' } = {
        'click': 'user-action',
        'submit': 'form-submit',
        'change': 'user-action',
        'input': 'user-action',
        'beforeunload': 'navigation',
        'error': 'error'
      };

      const trigger = triggerMap[eventType] || 'user-action';

      // 请求智能截图
      const result = await this.messageBridge.requestSmartScreenshot(trigger);

      if (result.screenshot) {
        this.logger.debug(`Smart screenshot captured for ${eventType} event`);
        this.statistics.screenshotsCaptured++;

        // 存储截图元数据
        this.storeScreenshotMetadata(result.metadata);

        return result.screenshot;
      }

      return undefined;

    } catch (error) {
      this.logger.error('Failed to take smart screenshot:', error);
      return undefined;
    }
  }

  /**
   * 判断是否应该截图
   */
  private shouldTakeScreenshot(eventType: string, importance: 'low' | 'medium' | 'high'): boolean {
    // 检查截图配置
    if (!this.config.enableScreenshots) {
      return false;
    }

    // 检查截图频率限制
    const now = Date.now();
    const timeSinceLastScreenshot = now - this.lastScreenshotTime;
    const minInterval = this.config.screenshotInterval || 1000; // 默认1秒间隔

    if (timeSinceLastScreenshot < minInterval && importance !== 'high') {
      return false;
    }

    // 根据事件类型和重要性决定
    const screenshotEvents = this.config.screenshotEvents || [
      'click', 'submit', 'change', 'beforeunload', 'error'
    ];

    if (!screenshotEvents.includes(eventType)) {
      return false;
    }

    // 高重要性事件总是截图
    if (importance === 'high') {
      return true;
    }

    // 中等重要性事件根据配置决定
    if (importance === 'medium' && this.config.screenshotQuality !== 'low') {
      return true;
    }

    // 低重要性事件只在高质量模式下截图
    if (importance === 'low' && this.config.screenshotQuality === 'high') {
      return true;
    }

    return false;
  }

  /**
   * 存储截图元数据
   */
  private storeScreenshotMetadata(metadata: any): void {
    try {
      // 这里可以存储截图的元数据，用于后续分析
      this.logger.debug('Screenshot metadata stored:', metadata);
    } catch (error) {
      this.logger.error('Failed to store screenshot metadata:', error);
    }
  }

  /**
   * 获取事件上下文
   */
  private getEventContext(): EventContext {
    return {
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      scrollPosition: {
        x: window.scrollX,
        y: window.scrollY
      },
      activeElement: document.activeElement?.tagName.toLowerCase(),
      focusedElement: document.activeElement ? this.getElementXPath(document.activeElement) : undefined
    };
  }

  /**
   * 获取性能数据
   */
  private getPerformanceData(): PerformanceData {
    const performance = window.performance;
    const memory = (performance as any).memory;
    
    return {
      memoryUsage: memory ? memory.usedJSHeapSize : undefined,
      renderTime: performance.now(),
      domNodes: document.querySelectorAll('*').length
    };
  }

  /**
   * 记录初始状态
   */
  private async recordInitialState(): Promise<void> {
    const initialEvent: RecordedEvent = {
      id: this.generateEventId(),
      sessionId: this.sessionId!,
      timestamp: Date.now(),
      type: 'session_start',
      data: {
        type: 'session_start',
        target: {
          tagName: 'document',
          textContent: document.title
        },
        timestamp: Date.now(),
        pageUrl: window.location.href
      },
      context: this.getEventContext(),
      performance: this.config.trackPagePerformance ? this.getPerformanceData() : undefined
    };

    this.eventQueue.push(initialEvent);
  }

  /**
   * 记录最终状态
   */
  private async recordFinalState(): Promise<void> {
    const finalEvent: RecordedEvent = {
      id: this.generateEventId(),
      sessionId: this.sessionId!,
      timestamp: Date.now(),
      type: 'session_end',
      data: {
        type: 'session_end',
        target: {
          tagName: 'document',
          textContent: `Session ended with ${this.eventCounter} events`
        },
        timestamp: Date.now(),
        pageUrl: window.location.href
      },
      context: this.getEventContext(),
      performance: this.config.trackPagePerformance ? this.getPerformanceData() : undefined
    };

    this.eventQueue.push(finalEvent);
  }

  /**
   * 设置性能监控
   */
  private setupPerformanceMonitoring(): void {
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        // 处理性能条目
        for (const entry of list.getEntries()) {
          this.logger.debug('Performance entry:', entry);
        }
      });

      try {
        this.performanceObserver.observe({ entryTypes: ['navigation', 'resource', 'measure'] });
      } catch (error) {
        this.logger.warn('Failed to setup performance observer:', error);
      }
    }
  }

  /**
   * 设置DOM变化监控
   */
  private setupMutationObserver(): void {
    this.mutationObserver = new MutationObserver((mutations) => {
      if (!this.isRecording || this.isPaused) {
        return;
      }

      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // 记录重要的DOM变化
          this.recordDOMChange(mutation);
        }
      }
    });

    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false
    });
  }

  /**
   * 记录DOM变化
   */
  private recordDOMChange(mutation: MutationRecord): void {
    // 只记录重要的DOM变化
    const importantNodes = Array.from(mutation.addedNodes).filter(node => {
      if (node.nodeType !== Node.ELEMENT_NODE) {
        return false;
      }
      
      const element = node as Element;
      return this.isImportantElement(element);
    });

    if (importantNodes.length > 0) {
      // 创建DOM变化事件
      // 这里可以扩展实现
    }
  }

  /**
   * 获取录制统计信息
   */
  getRecordingStatistics(): {
    totalEvents: number;
    screenshotsCaptured: number;
    screenshotsSkipped: number;
    recordingDuration: number;
    eventRate: number;
  } {
    const duration = this.isRecording ? Date.now() - this.lastEventTime : 0;
    const eventRate = duration > 0 ? (this.statistics.totalEvents / duration) * 1000 : 0;

    return {
      totalEvents: this.statistics.totalEvents,
      screenshotsCaptured: this.statistics.screenshotsCaptured,
      screenshotsSkipped: this.statistics.screenshotsSkipped,
      recordingDuration: duration,
      eventRate
    };
  }

  /**
   * 获取截图统计信息
   */
  getScreenshotStatistics(): {
    captured: number;
    skipped: number;
    total: number;
    lastCaptureTime: number;
    averageInterval: number;
  } {
    const total = this.statistics.screenshotsCaptured + this.statistics.screenshotsSkipped;
    const averageInterval = this.statistics.screenshotsCaptured > 1
      ? (Date.now() - this.lastScreenshotTime) / this.statistics.screenshotsCaptured
      : 0;

    return {
      captured: this.statistics.screenshotsCaptured,
      skipped: this.statistics.screenshotsSkipped,
      total,
      lastCaptureTime: this.lastScreenshotTime,
      averageInterval
    };
  }

  /**
   * 获取元素键
   */
  private getElementKey(element: Element): string {
    return element.id || element.tagName + (element.className ? '.' + element.className.split(' ')[0] : '');
  }

  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 销毁录制引擎
   */
  destroy(): void {
    this.stopRecording();
    
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
    
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }
    
    this.eventListeners.clear();
    this.eventQueue = [];
    
    this.logger.info('DOM event recorder destroyed');
  }
}
