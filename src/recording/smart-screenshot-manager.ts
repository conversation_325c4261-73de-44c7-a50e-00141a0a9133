// ERP智能助手 - 智能截图管理器

import { Logger } from '@/utils/logger';

export interface ScreenshotConfig {
  quality: 'high' | 'medium' | 'low';
  format: 'png' | 'jpeg';
  maxWidth: number;
  maxHeight: number;
  compression: number; // 0-1
  enableOptimization: boolean;
  enableWatermark: boolean;
}

export interface ScreenshotMetadata {
  id: string;
  timestamp: number;
  url: string;
  title: string;
  viewport: { width: number; height: number };
  scrollPosition: { x: number; y: number };
  elementBounds?: { x: number; y: number; width: number; height: number };
  trigger: 'user-action' | 'form-submit' | 'navigation' | 'error' | 'manual';
  importance: 'low' | 'medium' | 'high';
  fileSize: number;
  quality: string;
}

export interface ScreenshotData {
  dataUrl: string;
  metadata: ScreenshotMetadata;
  thumbnail?: string;
  annotations?: ScreenshotAnnotation[];
}

export interface ScreenshotAnnotation {
  type: 'highlight' | 'arrow' | 'text' | 'blur';
  position: { x: number; y: number };
  size?: { width: number; height: number };
  text?: string;
  color?: string;
}

export interface ScreenshotAnalytics {
  totalScreenshots: number;
  screenshotsByTrigger: { [trigger: string]: number };
  screenshotsByQuality: { [quality: string]: number };
  averageFileSize: number;
  totalStorageUsed: number;
  compressionRatio: number;
  captureSuccessRate: number;
}

export class SmartScreenshotManager {
  private logger: Logger;
  private config: ScreenshotConfig;
  private screenshots = new Map<string, ScreenshotData>();
  private analytics: ScreenshotAnalytics;
  private captureAttempts = 0;
  private captureSuccesses = 0;

  // 默认配置
  private static readonly DEFAULT_CONFIG: ScreenshotConfig = {
    quality: 'medium',
    format: 'png',
    maxWidth: 1920,
    maxHeight: 1080,
    compression: 0.8,
    enableOptimization: true,
    enableWatermark: false
  };

  constructor(config?: Partial<ScreenshotConfig>) {
    this.logger = new Logger('SmartScreenshotManager');
    this.config = { ...SmartScreenshotManager.DEFAULT_CONFIG, ...config };
    
    this.analytics = {
      totalScreenshots: 0,
      screenshotsByTrigger: {},
      screenshotsByQuality: {},
      averageFileSize: 0,
      totalStorageUsed: 0,
      compressionRatio: 0,
      captureSuccessRate: 0
    };
  }

  /**
   * 捕获智能截图
   */
  async captureSmartScreenshot(
    dataUrl: string,
    metadata: Partial<ScreenshotMetadata>
  ): Promise<ScreenshotData | null> {
    try {
      this.captureAttempts++;
      
      // 创建完整的元数据
      const fullMetadata: ScreenshotMetadata = {
        id: this.generateScreenshotId(),
        timestamp: Date.now(),
        url: window.location.href,
        title: document.title,
        viewport: { width: window.innerWidth, height: window.innerHeight },
        scrollPosition: { x: window.scrollX, y: window.scrollY },
        trigger: 'manual',
        importance: 'medium',
        fileSize: 0,
        quality: this.config.quality,
        ...metadata
      };

      // 优化截图
      const optimizedDataUrl = await this.optimizeScreenshot(dataUrl);
      
      // 计算文件大小
      fullMetadata.fileSize = this.calculateDataUrlSize(optimizedDataUrl);
      
      // 生成缩略图
      const thumbnail = await this.generateThumbnail(optimizedDataUrl);
      
      // 创建截图数据
      const screenshotData: ScreenshotData = {
        dataUrl: optimizedDataUrl,
        metadata: fullMetadata,
        thumbnail,
        annotations: []
      };

      // 存储截图
      this.screenshots.set(fullMetadata.id, screenshotData);
      
      // 更新分析数据
      this.updateAnalytics(screenshotData);
      
      this.captureSuccesses++;
      this.logger.debug(`Screenshot captured: ${fullMetadata.id}`);
      
      return screenshotData;
      
    } catch (error) {
      this.logger.error('Failed to capture smart screenshot:', error);
      return null;
    }
  }

  /**
   * 优化截图
   */
  private async optimizeScreenshot(dataUrl: string): Promise<string> {
    if (!this.config.enableOptimization) {
      return dataUrl;
    }

    try {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d')!;
          
          // 计算优化后的尺寸
          const { width, height } = this.calculateOptimalSize(img.width, img.height);
          
          canvas.width = width;
          canvas.height = height;
          
          // 绘制优化后的图像
          ctx.drawImage(img, 0, 0, width, height);
          
          // 添加水印（如果启用）
          if (this.config.enableWatermark) {
            this.addWatermark(ctx, width, height);
          }
          
          // 转换为优化后的数据URL
          const optimizedDataUrl = canvas.toDataURL(
            `image/${this.config.format}`,
            this.config.compression
          );
          
          resolve(optimizedDataUrl);
        };
        
        img.onerror = () => resolve(dataUrl);
        img.src = dataUrl;
      });
      
    } catch (error) {
      this.logger.error('Failed to optimize screenshot:', error);
      return dataUrl;
    }
  }

  /**
   * 计算最优尺寸
   */
  private calculateOptimalSize(originalWidth: number, originalHeight: number): { width: number; height: number } {
    const maxWidth = this.config.maxWidth;
    const maxHeight = this.config.maxHeight;
    
    if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
      return { width: originalWidth, height: originalHeight };
    }
    
    const widthRatio = maxWidth / originalWidth;
    const heightRatio = maxHeight / originalHeight;
    const ratio = Math.min(widthRatio, heightRatio);
    
    return {
      width: Math.round(originalWidth * ratio),
      height: Math.round(originalHeight * ratio)
    };
  }

  /**
   * 添加水印
   */
  private addWatermark(ctx: CanvasRenderingContext2D, width: number, height: number): void {
    ctx.save();
    
    // 设置水印样式
    ctx.globalAlpha = 0.3;
    ctx.fillStyle = '#666666';
    ctx.font = '12px Arial';
    ctx.textAlign = 'right';
    ctx.textBaseline = 'bottom';
    
    // 添加时间戳水印
    const timestamp = new Date().toLocaleString();
    ctx.fillText(`ERP Assistant - ${timestamp}`, width - 10, height - 10);
    
    ctx.restore();
  }

  /**
   * 生成缩略图
   */
  private async generateThumbnail(dataUrl: string, size = 150): Promise<string> {
    try {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d')!;
          
          // 计算缩略图尺寸（保持宽高比）
          const aspectRatio = img.width / img.height;
          let thumbWidth = size;
          let thumbHeight = size;
          
          if (aspectRatio > 1) {
            thumbHeight = size / aspectRatio;
          } else {
            thumbWidth = size * aspectRatio;
          }
          
          canvas.width = thumbWidth;
          canvas.height = thumbHeight;
          
          // 绘制缩略图
          ctx.drawImage(img, 0, 0, thumbWidth, thumbHeight);
          
          const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.7);
          resolve(thumbnailDataUrl);
        };
        
        img.onerror = () => resolve('');
        img.src = dataUrl;
      });
      
    } catch (error) {
      this.logger.error('Failed to generate thumbnail:', error);
      return '';
    }
  }

  /**
   * 计算数据URL大小
   */
  private calculateDataUrlSize(dataUrl: string): number {
    // Base64编码的数据URL大小估算
    const base64Data = dataUrl.split(',')[1] || '';
    return Math.round((base64Data.length * 3) / 4);
  }

  /**
   * 更新分析数据
   */
  private updateAnalytics(screenshotData: ScreenshotData): void {
    const { metadata } = screenshotData;
    
    this.analytics.totalScreenshots++;
    this.analytics.totalStorageUsed += metadata.fileSize;
    this.analytics.averageFileSize = this.analytics.totalStorageUsed / this.analytics.totalScreenshots;
    
    // 按触发类型统计
    this.analytics.screenshotsByTrigger[metadata.trigger] = 
      (this.analytics.screenshotsByTrigger[metadata.trigger] || 0) + 1;
    
    // 按质量统计
    this.analytics.screenshotsByQuality[metadata.quality] = 
      (this.analytics.screenshotsByQuality[metadata.quality] || 0) + 1;
    
    // 计算成功率
    this.analytics.captureSuccessRate = this.captureSuccesses / this.captureAttempts;
    
    // 计算压缩比
    if (this.config.enableOptimization) {
      this.analytics.compressionRatio = this.config.compression;
    }
  }

  /**
   * 获取截图
   */
  getScreenshot(id: string): ScreenshotData | undefined {
    return this.screenshots.get(id);
  }

  /**
   * 获取所有截图
   */
  getAllScreenshots(): ScreenshotData[] {
    return Array.from(this.screenshots.values());
  }

  /**
   * 删除截图
   */
  deleteScreenshot(id: string): boolean {
    const screenshot = this.screenshots.get(id);
    if (screenshot) {
      this.screenshots.delete(id);
      this.analytics.totalScreenshots--;
      this.analytics.totalStorageUsed -= screenshot.metadata.fileSize;
      return true;
    }
    return false;
  }

  /**
   * 清理旧截图
   */
  cleanupOldScreenshots(maxAge: number = 24 * 60 * 60 * 1000): number {
    const now = Date.now();
    let deletedCount = 0;
    
    for (const [id, screenshot] of this.screenshots.entries()) {
      if (now - screenshot.metadata.timestamp > maxAge) {
        this.deleteScreenshot(id);
        deletedCount++;
      }
    }
    
    this.logger.debug(`Cleaned up ${deletedCount} old screenshots`);
    return deletedCount;
  }

  /**
   * 获取分析数据
   */
  getAnalytics(): ScreenshotAnalytics {
    return { ...this.analytics };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ScreenshotConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.debug('Screenshot config updated:', newConfig);
  }

  /**
   * 生成截图ID
   */
  private generateScreenshotId(): string {
    return `screenshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.screenshots.clear();
    this.logger.info('Smart screenshot manager destroyed');
  }
}
