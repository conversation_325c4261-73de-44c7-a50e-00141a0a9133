// ERP智能助手Chrome插件 - Popup界面脚本

class PopupManager {
  constructor() {
    this.isRecording = false;
    this.isConnected = false;
    this.currentSettings = {};
    this.init();
  }

  init() {
    this.bindEvents();
    this.loadSettings();
    this.checkConnectionStatus();
    this.updateUI();
  }

  bindEvents() {
    // 录制按钮
    document.getElementById('recordBtn').addEventListener('click', () => {
      this.toggleRecording();
    });

    // 智能引导按钮
    document.getElementById('guidanceBtn').addEventListener('click', () => {
      this.toggleGuidance();
    });

    // 快速操作按钮
    document.getElementById('documentsBtn').addEventListener('click', () => {
      this.openDocuments();
    });

    document.getElementById('settingsBtn').addEventListener('click', () => {
      this.openSettings();
    });

    document.getElementById('helpBtn').addEventListener('click', () => {
      this.openHelp();
    });

    document.getElementById('feedbackBtn').addEventListener('click', () => {
      this.openFeedback();
    });

    // 高级设置链接
    document.getElementById('optionsLink').addEventListener('click', (e) => {
      e.preventDefault();
      this.openOptions();
    });
  }

  async toggleRecording() {
    const recordBtn = document.getElementById('recordBtn');
    
    if (this.isRecording) {
      // 停止录制
      recordBtn.classList.add('loading');
      
      try {
        await this.sendMessage({ action: 'stopRecording' });
        this.isRecording = false;
        recordBtn.textContent = '开始录制';
        recordBtn.classList.remove('loading');
        this.showNotification('录制已停止', 'success');
      } catch (error) {
        console.error('停止录制失败:', error);
        recordBtn.classList.remove('loading');
        this.showNotification('停止录制失败', 'error');
      }
    } else {
      // 开始录制
      recordBtn.classList.add('loading');
      
      try {
        await this.sendMessage({ action: 'startRecording' });
        this.isRecording = true;
        recordBtn.textContent = '停止录制';
        recordBtn.classList.remove('loading');
        this.showNotification('录制已开始', 'success');
      } catch (error) {
        console.error('开始录制失败:', error);
        recordBtn.classList.remove('loading');
        this.showNotification('开始录制失败', 'error');
      }
    }
    
    this.updateUI();
  }

  async toggleGuidance() {
    const guidanceBtn = document.getElementById('guidanceBtn');
    guidanceBtn.classList.add('loading');
    
    try {
      await this.sendMessage({ action: 'toggleGuidance' });
      guidanceBtn.classList.remove('loading');
      this.showNotification('智能引导已切换', 'info');
    } catch (error) {
      console.error('切换智能引导失败:', error);
      guidanceBtn.classList.remove('loading');
      this.showNotification('切换智能引导失败', 'error');
    }
  }

  openDocuments() {
    chrome.tabs.create({ url: chrome.runtime.getURL('documents.html') });
  }

  openSettings() {
    this.showSettingsPanel();
  }

  openHelp() {
    chrome.tabs.create({ url: 'https://help.erp-assistant.com' });
  }

  openFeedback() {
    this.showFeedbackForm();
  }

  openOptions() {
    chrome.runtime.openOptionsPage();
  }

  showSettingsPanel() {
    const panel = document.createElement('div');
    panel.className = 'quick-settings-panel';
    panel.innerHTML = `
      <div class="panel-content">
        <div class="panel-header">
          <h3>快速设置</h3>
          <button class="close-btn">&times;</button>
        </div>
        <div class="setting-item">
          <label>
            <input type="checkbox" id="autoRecord"> 自动录制
          </label>
        </div>
        <div class="setting-item">
          <label>
            <input type="checkbox" id="smartGuidance"> 智能引导
          </label>
        </div>
        <div class="setting-item">
          <label for="aiModel">AI模型:</label>
          <select id="aiModel">
            <option value="gpt-4">GPT-4</option>
            <option value="gpt-3.5">GPT-3.5</option>
            <option value="claude">Claude</option>
          </select>
        </div>
        <div class="panel-actions">
          <button class="btn btn-secondary" id="cancelSettings">取消</button>
          <button class="btn btn-primary" id="saveSettings">保存</button>
        </div>
      </div>
    `;

    document.body.appendChild(panel);

    // 加载当前设置
    document.getElementById('autoRecord').checked = this.currentSettings.autoRecord || false;
    document.getElementById('smartGuidance').checked = this.currentSettings.smartGuidance || false;
    document.getElementById('aiModel').value = this.currentSettings.aiModel || 'gpt-4';

    // 绑定事件
    panel.querySelector('.close-btn').addEventListener('click', () => {
      document.body.removeChild(panel);
    });

    panel.querySelector('#cancelSettings').addEventListener('click', () => {
      document.body.removeChild(panel);
    });

    panel.querySelector('#saveSettings').addEventListener('click', async () => {
      const settings = {
        autoRecord: document.getElementById('autoRecord').checked,
        smartGuidance: document.getElementById('smartGuidance').checked,
        aiModel: document.getElementById('aiModel').value,
      };

      try {
        await this.saveSettings(settings);
        this.currentSettings = settings;
        document.body.removeChild(panel);
        this.showNotification('设置已保存', 'success');
      } catch (error) {
        console.error('保存设置失败:', error);
        this.showNotification('保存设置失败', 'error');
      }
    });

    // 点击背景关闭
    panel.addEventListener('click', (e) => {
      if (e.target === panel) {
        document.body.removeChild(panel);
      }
    });
  }

  showFeedbackForm() {
    const form = document.createElement('div');
    form.className = 'feedback-form';
    form.innerHTML = `
      <div class="form-content">
        <div class="form-header">
          <h3>意见反馈</h3>
          <button class="close-btn">&times;</button>
        </div>
        <div class="form-group">
          <label for="feedbackType">反馈类型:</label>
          <select id="feedbackType">
            <option value="bug">问题报告</option>
            <option value="feature">功能建议</option>
            <option value="improvement">改进建议</option>
            <option value="other">其他</option>
          </select>
        </div>
        <div class="form-group">
          <label for="feedbackTitle">标题:</label>
          <input type="text" id="feedbackTitle" placeholder="请简要描述您的反馈">
        </div>
        <div class="form-group">
          <label for="feedbackContent">详细描述:</label>
          <textarea id="feedbackContent" placeholder="请详细描述您的问题或建议"></textarea>
        </div>
        <div class="form-actions">
          <button class="btn btn-secondary" id="cancelFeedback">取消</button>
          <button class="btn btn-primary" id="submitFeedback">提交</button>
        </div>
      </div>
    `;

    document.body.appendChild(form);

    // 绑定事件
    form.querySelector('.close-btn').addEventListener('click', () => {
      document.body.removeChild(form);
    });

    form.querySelector('#cancelFeedback').addEventListener('click', () => {
      document.body.removeChild(form);
    });

    form.querySelector('#submitFeedback').addEventListener('click', async () => {
      const feedbackData = {
        type: document.getElementById('feedbackType').value,
        title: document.getElementById('feedbackTitle').value,
        content: document.getElementById('feedbackContent').value,
        timestamp: Date.now(),
      };

      if (!feedbackData.title || !feedbackData.content) {
        this.showNotification('请填写完整的反馈信息', 'error');
        return;
      }

      try {
        await this.submitFeedback(feedbackData);
        document.body.removeChild(form);
        this.showNotification('反馈已提交，感谢您的建议！', 'success');
      } catch (error) {
        console.error('提交反馈失败:', error);
        this.showNotification('提交反馈失败', 'error');
      }
    });

    // 点击背景关闭
    form.addEventListener('click', (e) => {
      if (e.target === form) {
        document.body.removeChild(form);
      }
    });
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
      notification.style.opacity = '1';
    }, 10);
    
    // 自动隐藏
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  async sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, message, (response) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve(response);
            }
          });
        } else {
          reject(new Error('No active tab found'));
        }
      });
    });
  }

  async loadSettings() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['settings'], (result) => {
        this.currentSettings = result.settings || {};
        resolve(this.currentSettings);
      });
    });
  }

  async saveSettings(settings) {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set({ settings }, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async submitFeedback(feedbackData) {
    // 这里应该发送到后端API
    console.log('提交反馈:', feedbackData);
    
    // 模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true });
      }, 1000);
    });
  }

  async checkConnectionStatus() {
    try {
      // 检查与后端服务的连接状态
      const response = await fetch('http://localhost:3000/api/health');
      this.isConnected = response.ok;
    } catch (error) {
      this.isConnected = false;
    }
  }

  updateUI() {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const recordBtn = document.getElementById('recordBtn');

    // 更新连接状态
    if (this.isConnected) {
      statusDot.classList.add('active');
      statusText.textContent = '已连接';
    } else {
      statusDot.classList.remove('active');
      statusText.textContent = '未连接';
    }

    // 更新录制按钮状态
    if (this.isRecording) {
      recordBtn.textContent = '停止录制';
      recordBtn.classList.add('btn-secondary');
      recordBtn.classList.remove('btn-primary');
    } else {
      recordBtn.textContent = '开始录制';
      recordBtn.classList.add('btn-primary');
      recordBtn.classList.remove('btn-secondary');
    }
  }
}

// 初始化Popup管理器
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});
