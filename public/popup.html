<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ERP智能助手</title>
  <style>
    body {
      width: 350px;
      min-height: 400px;
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8f9fa;
    }
    
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px;
      text-align: center;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
    
    .header p {
      margin: 4px 0 0 0;
      font-size: 12px;
      opacity: 0.9;
    }
    
    .content {
      padding: 16px;
    }
    
    .status-card {
      background: white;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
    }
    
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #dc3545;
    }
    
    .status-dot.active {
      background: #28a745;
    }
    
    .status-text {
      font-size: 14px;
      font-weight: 500;
    }
    
    .controls {
      display: flex;
      gap: 8px;
      margin-top: 12px;
    }
    
    .btn {
      flex: 1;
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .btn-primary {
      background: #007bff;
      color: white;
    }
    
    .btn-primary:hover {
      background: #0056b3;
    }
    
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background: #545b62;
    }
    
    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    .quick-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      margin-top: 16px;
    }
    
    .action-btn {
      padding: 12px;
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .action-btn:hover {
      background: #f8f9fa;
      border-color: #007bff;
    }
    
    .action-icon {
      font-size: 20px;
      margin-bottom: 4px;
    }
    
    .action-text {
      font-size: 12px;
      color: #6c757d;
    }
    
    .footer {
      padding: 12px 16px;
      border-top: 1px solid #dee2e6;
      background: white;
      text-align: center;
    }
    
    .footer a {
      color: #007bff;
      text-decoration: none;
      font-size: 12px;
    }
    
    .footer a:hover {
      text-decoration: underline;
    }

    /* 加载状态 */
    .btn.loading {
      position: relative;
      color: transparent;
    }

    .btn.loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 16px;
      height: 16px;
      border: 2px solid #fff;
      border-top-color: transparent;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to {
        transform: translate(-50%, -50%) rotate(360deg);
      }
    }

    /* 通知样式 */
    .notification {
      position: fixed;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      z-index: 1000;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .notification.notification-success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .notification.notification-error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .notification.notification-info {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    /* 快速设置面板 */
    .quick-settings-panel {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0,0,0,0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .quick-settings-panel .panel-content {
      background: white;
      border-radius: 8px;
      padding: 20px;
      min-width: 300px;
      max-width: 400px;
    }

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #dee2e6;
    }

    .panel-header h3 {
      margin: 0;
      font-size: 16px;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #6c757d;
    }

    .close-btn:hover {
      color: #333;
    }

    .setting-item {
      margin-bottom: 12px;
    }

    .setting-item label {
      display: block;
      margin-bottom: 4px;
      font-size: 14px;
      font-weight: 500;
    }

    .setting-item input,
    .setting-item select {
      width: 100%;
      padding: 6px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    .setting-item input[type="checkbox"] {
      width: auto;
      margin-right: 8px;
    }

    .panel-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #dee2e6;
    }

    /* 反馈表单 */
    .feedback-form {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0,0,0,0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .feedback-form .form-content {
      background: white;
      border-radius: 8px;
      padding: 20px;
      min-width: 350px;
      max-width: 450px;
    }

    .form-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #dee2e6;
    }

    .form-header h3 {
      margin: 0;
      font-size: 16px;
    }

    .form-group {
      margin-bottom: 16px;
    }

    .form-group label {
      display: block;
      margin-bottom: 6px;
      font-size: 14px;
      font-weight: 500;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      font-family: inherit;
    }

    .form-group textarea {
      resize: vertical;
      min-height: 80px;
    }

    .form-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #dee2e6;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>ERP智能助手</h1>
    <p>AI驱动的操作指导与文档生成</p>
  </div>
  
  <div class="content">
    <div class="status-card">
      <div class="status-indicator">
        <div class="status-dot" id="statusDot"></div>
        <span class="status-text" id="statusText">未连接</span>
      </div>
      <div class="controls">
        <button class="btn btn-primary" id="recordBtn">开始录制</button>
        <button class="btn btn-secondary" id="guidanceBtn">智能引导</button>
      </div>
    </div>
    
    <div class="quick-actions">
      <div class="action-btn" id="documentsBtn">
        <div class="action-icon">📄</div>
        <div class="action-text">我的文档</div>
      </div>
      <div class="action-btn" id="settingsBtn">
        <div class="action-icon">⚙️</div>
        <div class="action-text">设置</div>
      </div>
      <div class="action-btn" id="helpBtn">
        <div class="action-icon">❓</div>
        <div class="action-text">帮助</div>
      </div>
      <div class="action-btn" id="feedbackBtn">
        <div class="action-icon">💬</div>
        <div class="action-text">反馈</div>
      </div>
    </div>
  </div>
  
  <div class="footer">
    <a href="#" id="optionsLink">高级设置</a>
  </div>

  <script src="js/popup.js"></script>
</body>
</html>
