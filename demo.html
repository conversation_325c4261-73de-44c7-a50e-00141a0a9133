<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ERP智能助手 - 界面演示</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: #f7f9fc;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }
    
    .demo-container {
      display: grid;
      grid-template-columns: 320px 1fr;
      gap: 30px;
      max-width: 1200px;
      width: 100%;
    }
    
    .popup-demo {
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
      height: fit-content;
    }
    
    .options-demo {
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
      max-height: 80vh;
      overflow-y: auto;
    }
    
    /* Popup样式 */
    .popup-header {
      background: linear-gradient(135deg, #007bff, #0056b3);
      color: white;
      padding: 20px;
      text-align: center;
    }
    
    .popup-header h1 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
    }
    
    .popup-header p {
      font-size: 12px;
      opacity: 0.9;
    }
    
    .popup-content {
      padding: 20px;
    }
    
    .status-section {
      margin-bottom: 20px;
    }
    
    .status-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .status-item:last-child {
      border-bottom: none;
    }
    
    .status-label {
      font-size: 14px;
      color: #333;
    }
    
    .status-value {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
    }
    
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #28a745;
    }
    
    .status-dot.inactive {
      background: #dc3545;
    }
    
    .btn {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      margin-bottom: 8px;
    }
    
    .btn-primary {
      background: #007bff;
      color: white;
    }
    
    .btn-primary:hover {
      background: #0056b3;
      transform: translateY(-1px);
    }
    
    .btn-secondary {
      background: #f8f9fa;
      color: #333;
      border: 1px solid #dee2e6;
    }
    
    .btn-secondary:hover {
      background: #e9ecef;
    }
    
    /* Options样式 */
    .options-header {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      padding: 30px;
      text-align: center;
    }
    
    .options-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
    }
    
    .options-header p {
      font-size: 14px;
      opacity: 0.9;
    }
    
    .tabs {
      display: flex;
      background: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
    }
    
    .tab {
      flex: 1;
      padding: 15px 10px;
      text-align: center;
      cursor: pointer;
      font-size: 13px;
      font-weight: 500;
      color: #666;
      border-bottom: 3px solid transparent;
      transition: all 0.2s;
    }
    
    .tab:hover {
      background: #e9ecef;
      color: #333;
    }
    
    .tab.active {
      background: white;
      color: #007bff;
      border-bottom-color: #007bff;
    }
    
    .tab-content {
      padding: 30px;
      display: none;
    }
    
    .tab-content.active {
      display: block;
    }
    
    .form-section {
      margin-bottom: 30px;
    }
    
    .form-section h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 2px solid #007bff;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.2s;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
    }
    
    .checkbox-group {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .checkbox-group input[type="checkbox"] {
      width: auto;
      margin: 0;
    }
    
    .help-text {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }
    
    .model-selector {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-top: 12px;
    }
    
    .model-card {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 15px;
      cursor: pointer;
      transition: all 0.2s;
      text-align: center;
    }
    
    .model-card:hover {
      border-color: #007bff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,123,255,0.15);
    }
    
    .model-card.selected {
      border-color: #007bff;
      background: #f8f9ff;
    }
    
    .model-name {
      font-weight: 600;
      margin-bottom: 4px;
      color: #333;
    }
    
    .model-desc {
      font-size: 12px;
      color: #666;
    }
    
    .alert {
      padding: 12px 15px;
      border-radius: 6px;
      margin-bottom: 20px;
      font-size: 14px;
    }
    
    .alert-info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    
    .save-section {
      background: #f8f9fa;
      padding: 20px 30px;
      border-top: 1px solid #dee2e6;
      display: flex;
      gap: 10px;
    }
    
    .save-section .btn {
      width: auto;
      padding: 10px 20px;
      margin: 0;
    }
    
    @media (max-width: 768px) {
      .demo-container {
        grid-template-columns: 1fr;
        gap: 20px;
      }
      
      .popup-demo {
        order: 2;
      }
      
      .options-demo {
        order: 1;
        max-height: none;
      }
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <!-- Popup界面演示 -->
    <div class="popup-demo">
      <div class="popup-header">
        <h1>🤖 ERP智能助手</h1>
        <p>您的专属AI操作助手</p>
      </div>
      
      <div class="popup-content">
        <div class="status-section">
          <div class="status-item">
            <span class="status-label">系统状态</span>
            <div class="status-value">
              <div class="status-dot"></div>
              <span>运行中</span>
            </div>
          </div>
          
          <div class="status-item">
            <span class="status-label">AI模型</span>
            <div class="status-value">
              <div class="status-dot"></div>
              <span>Llama 3.2 3B</span>
            </div>
          </div>
          
          <div class="status-item">
            <span class="status-label">录制状态</span>
            <div class="status-value">
              <div class="status-dot inactive"></div>
              <span>未录制</span>
            </div>
          </div>
          
          <div class="status-item">
            <span class="status-label">智能引导</span>
            <div class="status-value">
              <div class="status-dot"></div>
              <span>已启用</span>
            </div>
          </div>
        </div>
        
        <button class="btn btn-primary">🔴 开始录制</button>
        <button class="btn btn-secondary">✨ 智能引导</button>
        <button class="btn btn-secondary">💬 AI助手</button>
        <button class="btn btn-secondary">⚙️ 设置</button>
      </div>
    </div>
    
    <!-- Options界面演示 -->
    <div class="options-demo">
      <div class="options-header">
        <h1>⚙️ ERP智能助手设置</h1>
        <p>配置您的AI助手，获得最佳的使用体验</p>
      </div>
      
      <div class="tabs">
        <div class="tab active" onclick="switchTab('general')">常规设置</div>
        <div class="tab" onclick="switchTab('ai')">AI模型</div>
        <div class="tab" onclick="switchTab('privacy')">隐私安全</div>
        <div class="tab" onclick="switchTab('guidance')">智能引导</div>
        <div class="tab" onclick="switchTab('advanced')">高级选项</div>
      </div>
      
      <div class="tab-content active" id="general">
        <div class="form-section">
          <h3>🌐 基础配置</h3>
          
          <div class="form-group">
            <label for="language">界面语言</label>
            <select id="language">
              <option value="zh-CN">简体中文</option>
              <option value="en-US">English</option>
            </select>
          </div>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="autoStart" checked>
              <label for="autoStart">自动启动智能引导</label>
            </div>
            <div class="help-text">在支持的ERP页面自动显示操作提示</div>
          </div>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="notifications" checked>
              <label for="notifications">启用通知</label>
            </div>
            <div class="help-text">接收重要提醒和更新通知</div>
          </div>
        </div>
        
        <div class="form-section">
          <h3>📹 录制设置</h3>
          
          <div class="form-group">
            <label for="recordingQuality">录制质量</label>
            <select id="recordingQuality">
              <option value="high">高质量（推荐）</option>
              <option value="medium">中等质量</option>
              <option value="low">低质量（节省空间）</option>
            </select>
          </div>
        </div>
      </div>
      
      <div class="tab-content" id="ai">
        <div class="alert alert-info">
          <strong>💡 提示：</strong>更改AI模型设置可能影响响应速度和准确性。建议使用默认配置。
        </div>
        
        <div class="form-section">
          <h3>🏠 本地模型</h3>
          <div class="model-selector">
            <div class="model-card selected">
              <div class="model-name">Llama 3.2 3B</div>
              <div class="model-desc">快速响应，隐私保护</div>
            </div>
            <div class="model-card">
              <div class="model-name">Phi-3 Mini</div>
              <div class="model-desc">轻量级，低资源消耗</div>
            </div>
          </div>
        </div>
        
        <div class="form-section">
          <h3>☁️ 云端模型</h3>
          <div class="model-selector">
            <div class="model-card selected">
              <div class="model-name">GPT-4o</div>
              <div class="model-desc">高质量生成，复杂推理</div>
            </div>
            <div class="model-card">
              <div class="model-name">Claude 3.5 Sonnet</div>
              <div class="model-desc">优秀的理解能力</div>
            </div>
          </div>
        </div>
        
        <div class="form-section">
          <h3>🔑 API配置</h3>
          
          <div class="form-group">
            <label for="apiKey">API密钥</label>
            <input type="password" id="apiKey" placeholder="输入您的API密钥">
            <div class="help-text">用于访问云端AI服务，密钥将安全存储在本地</div>
          </div>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="enableRAG" checked>
              <label for="enableRAG">启用RAG检索增强</label>
            </div>
            <div class="help-text">使用检索增强生成技术提高回答准确性</div>
          </div>
        </div>
      </div>
      
      <div class="tab-content" id="privacy">
        <div class="form-section">
          <h3>🔒 隐私保护</h3>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="localFirst" checked>
              <label for="localFirst">优先本地处理</label>
            </div>
            <div class="help-text">敏感数据优先在本地处理，提高隐私保护</div>
          </div>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="dataAnonymization" checked>
              <label for="dataAnonymization">数据脱敏</label>
            </div>
            <div class="help-text">自动识别并脱敏敏感信息</div>
          </div>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="encryptStorage">
              <label for="encryptStorage">加密本地存储</label>
            </div>
            <div class="help-text">使用加密技术保护本地存储的数据</div>
          </div>
        </div>
        
        <div class="form-section">
          <h3>🗂️ 数据管理</h3>
          
          <div class="form-group">
            <label for="dataRetention">数据保留期限</label>
            <select id="dataRetention">
              <option value="7">7天</option>
              <option value="30" selected>30天</option>
              <option value="90">90天</option>
              <option value="365">1年</option>
              <option value="0">永久保留</option>
            </select>
          </div>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="shareUsageData">
              <label for="shareUsageData">共享使用数据</label>
            </div>
            <div class="help-text">匿名共享使用统计以帮助改进产品</div>
          </div>
        </div>
      </div>
      
      <div class="tab-content" id="guidance">
        <div class="form-section">
          <h3>🎯 引导配置</h3>
          
          <div class="form-group">
            <label for="guidanceLevel">引导详细程度</label>
            <select id="guidanceLevel">
              <option value="minimal">简洁模式</option>
              <option value="standard" selected>标准模式</option>
              <option value="detailed">详细模式</option>
            </select>
            <div class="help-text">选择适合您经验水平的引导详细程度</div>
          </div>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="showHints" checked>
              <label for="showHints">显示操作提示</label>
            </div>
            <div class="help-text">在页面上显示智能操作提示</div>
          </div>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="adaptiveGuidance" checked>
              <label for="adaptiveGuidance">自适应引导</label>
            </div>
            <div class="help-text">根据您的操作习惯自动调整引导策略</div>
          </div>
        </div>
        
        <div class="form-section">
          <h3>🤖 AI助手</h3>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="enableChatAssistant" checked>
              <label for="enableChatAssistant">启用聊天助手</label>
            </div>
            <div class="help-text">提供智能对话帮助和操作建议</div>
          </div>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="enableErrorPrevention" checked>
              <label for="enableErrorPrevention">启用错误预防</label>
            </div>
            <div class="help-text">主动检测并预防常见操作错误</div>
          </div>
        </div>
      </div>
      
      <div class="tab-content" id="advanced">
        <div class="form-section">
          <h3>🔧 高级选项</h3>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="debugMode">
              <label for="debugMode">调试模式</label>
            </div>
            <div class="help-text">启用详细日志记录，用于问题诊断</div>
          </div>
          
          <div class="form-group">
            <label for="maxMemoryUsage">最大内存使用 (MB)</label>
            <input type="number" id="maxMemoryUsage" value="512" min="256" max="2048">
            <div class="help-text">限制插件的内存使用量</div>
          </div>
          
          <div class="form-group">
            <label for="customPrompt">自定义提示词</label>
            <textarea id="customPrompt" rows="4" placeholder="输入自定义的AI提示词模板..."></textarea>
            <div class="help-text">高级用户可以自定义AI的行为模式</div>
          </div>
        </div>
        
        <div class="form-section">
          <h3>🧪 实验性功能</h3>
          
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="experimentalFeatures">
              <label for="experimentalFeatures">启用实验性功能</label>
            </div>
            <div class="help-text">启用正在开发中的实验性功能（可能不稳定）</div>
          </div>
        </div>
      </div>
      
      <div class="save-section">
        <button class="btn btn-primary">💾 保存设置</button>
        <button class="btn btn-secondary">↩️ 取消</button>
        <button class="btn btn-secondary">🔄 重置</button>
      </div>
    </div>
  </div>

  <script>
    function switchTab(tabName) {
      // 隐藏所有标签内容
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
      });
      
      // 移除所有标签的活动状态
      document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
      });
      
      // 显示选中的标签内容
      document.getElementById(tabName).classList.add('active');
      
      // 激活选中的标签
      event.target.classList.add('active');
    }
    
    // 模型卡片选择
    document.querySelectorAll('.model-card').forEach(card => {
      card.addEventListener('click', function() {
        // 移除同组其他卡片的选中状态
        const group = this.parentElement;
        group.querySelectorAll('.model-card').forEach(c => c.classList.remove('selected'));
        
        // 选中当前卡片
        this.classList.add('selected');
      });
    });
    
    // 添加一些交互效果
    document.querySelectorAll('.btn').forEach(btn => {
      btn.addEventListener('click', function() {
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
          this.style.transform = '';
        }, 150);
      });
    });
  </script>
</body>
</html>
